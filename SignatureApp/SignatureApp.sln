Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.32126.317
MinimumVisualStudioVersion = 10.0.40219.1
Project("{54A90642-561A-4BB1-A94E-469ADEE60C69}") = "frontend", "frontend\frontend.esproj", "{72067536-26A6-477D-BF28-9375B07C1715}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Backend", "Backend\Backend.csproj", "{D098767D-09A6-4EEB-9B13-30A232B9D8A1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Data", "Data\Data.csproj", "{FAAA9714-BEEC-49CA-8034-93A5D766E308}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Model", "Model\Model.csproj", "{352ED28F-A7A7-4E85-A35F-E9486B675CED}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Service", "Service\Service.csproj", "{47E78CDC-F89B-49C2-9EF4-C4B5C1764EDB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DataInterface", "DataInterface\DataInterface.csproj", "{F4B5788C-286D-4C3D-8ED9-3673795FB5D1}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{4B4B62AD-F05C-46EA-A5A5-4667FD9ED391}"
	ProjectSection(SolutionItems) = preProject
		..\README.md = ..\README.md
		..\azure-pipelines-functions.yml = ..\azure-pipelines-functions.yml
		..\azure-pipelines-backend.yml = ..\azure-pipelines-backend.yml
		..\azure-pipelines-frontend.yml = ..\azure-pipelines-frontend.yml
		CLAUDE.md = CLAUDE.md
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Service.Test", "Service.Test\Service.Test.csproj", "{3B830FEC-4F7C-4535-BBB2-D2A6146874FC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Backend.Tests", "Backend.Tests\Backend.Tests.csproj", "{A55AAA6A-EFE1-4FB5-8822-F422E11CAE13}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VeracityIngressFunctionApp", "VeracityIngressFunctionApp\VeracityIngressFunctionApp.csproj", "{B7D9B93C-502B-4370-B9EF-DB86DA8DDAB4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Tools", "Tools\Tools.csproj", "{3BC7866F-701F-4001-8BDE-B21064D285AA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DocumentIntelligenceFunction", "DocumentIntelligenceFunction\DocumentIntelligenceFunction.csproj", "{BFAFDC8B-84AA-4FCC-BC57-75B17D7331AE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UpdateUserStatsFunctionApp", "UpdateUserStatsFunctionApp\UpdateUserStatsFunctionApp.csproj", "{50649999-9EEE-480D-A3B3-EB87DF05AC66}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{72067536-26A6-477D-BF28-9375B07C1715}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{72067536-26A6-477D-BF28-9375B07C1715}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{72067536-26A6-477D-BF28-9375B07C1715}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{72067536-26A6-477D-BF28-9375B07C1715}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{72067536-26A6-477D-BF28-9375B07C1715}.Release|Any CPU.Build.0 = Release|Any CPU
		{72067536-26A6-477D-BF28-9375B07C1715}.Release|Any CPU.Deploy.0 = Release|Any CPU
		{D098767D-09A6-4EEB-9B13-30A232B9D8A1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D098767D-09A6-4EEB-9B13-30A232B9D8A1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D098767D-09A6-4EEB-9B13-30A232B9D8A1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D098767D-09A6-4EEB-9B13-30A232B9D8A1}.Release|Any CPU.Build.0 = Release|Any CPU
		{FAAA9714-BEEC-49CA-8034-93A5D766E308}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FAAA9714-BEEC-49CA-8034-93A5D766E308}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FAAA9714-BEEC-49CA-8034-93A5D766E308}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FAAA9714-BEEC-49CA-8034-93A5D766E308}.Release|Any CPU.Build.0 = Release|Any CPU
		{352ED28F-A7A7-4E85-A35F-E9486B675CED}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{352ED28F-A7A7-4E85-A35F-E9486B675CED}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{352ED28F-A7A7-4E85-A35F-E9486B675CED}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{352ED28F-A7A7-4E85-A35F-E9486B675CED}.Release|Any CPU.Build.0 = Release|Any CPU
		{47E78CDC-F89B-49C2-9EF4-C4B5C1764EDB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{47E78CDC-F89B-49C2-9EF4-C4B5C1764EDB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{47E78CDC-F89B-49C2-9EF4-C4B5C1764EDB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{47E78CDC-F89B-49C2-9EF4-C4B5C1764EDB}.Release|Any CPU.Build.0 = Release|Any CPU
		{F4B5788C-286D-4C3D-8ED9-3673795FB5D1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F4B5788C-286D-4C3D-8ED9-3673795FB5D1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F4B5788C-286D-4C3D-8ED9-3673795FB5D1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F4B5788C-286D-4C3D-8ED9-3673795FB5D1}.Release|Any CPU.Build.0 = Release|Any CPU
		{3B830FEC-4F7C-4535-BBB2-D2A6146874FC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3B830FEC-4F7C-4535-BBB2-D2A6146874FC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3B830FEC-4F7C-4535-BBB2-D2A6146874FC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3B830FEC-4F7C-4535-BBB2-D2A6146874FC}.Release|Any CPU.Build.0 = Release|Any CPU
		{A55AAA6A-EFE1-4FB5-8822-F422E11CAE13}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A55AAA6A-EFE1-4FB5-8822-F422E11CAE13}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A55AAA6A-EFE1-4FB5-8822-F422E11CAE13}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A55AAA6A-EFE1-4FB5-8822-F422E11CAE13}.Release|Any CPU.Build.0 = Release|Any CPU
		{B7D9B93C-502B-4370-B9EF-DB86DA8DDAB4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B7D9B93C-502B-4370-B9EF-DB86DA8DDAB4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B7D9B93C-502B-4370-B9EF-DB86DA8DDAB4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B7D9B93C-502B-4370-B9EF-DB86DA8DDAB4}.Release|Any CPU.Build.0 = Release|Any CPU
		{3BC7866F-701F-4001-8BDE-B21064D285AA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3BC7866F-701F-4001-8BDE-B21064D285AA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3BC7866F-701F-4001-8BDE-B21064D285AA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3BC7866F-701F-4001-8BDE-B21064D285AA}.Release|Any CPU.Build.0 = Release|Any CPU
		{BFAFDC8B-84AA-4FCC-BC57-75B17D7331AE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BFAFDC8B-84AA-4FCC-BC57-75B17D7331AE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BFAFDC8B-84AA-4FCC-BC57-75B17D7331AE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BFAFDC8B-84AA-4FCC-BC57-75B17D7331AE}.Release|Any CPU.Build.0 = Release|Any CPU
		{50649999-9EEE-480D-A3B3-EB87DF05AC66}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{50649999-9EEE-480D-A3B3-EB87DF05AC66}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{50649999-9EEE-480D-A3B3-EB87DF05AC66}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{50649999-9EEE-480D-A3B3-EB87DF05AC66}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {237A31AE-8D72-4772-AB43-550FFC2705F8}
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
	EndGlobalSection
EndGlobal
