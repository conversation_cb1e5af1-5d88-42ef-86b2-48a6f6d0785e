using Backend.Controllers;
using DataInterface.RepositoryInterfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Model.DTOs;
using Moq;
using Service.Test;
using System.Threading;
using System.Threading.Tasks;

namespace Backend.Tests;

[TestClass]
public class UploadSignatureSheetsControllerTests : DatabaseTestsBase
{
    [TestInitialize]
    public async Task TestInitialize()
    {
        await InitializeAsync();
    }

    [TestMethod]
    public async Task TestStartUpload_FailsCorrectly()
    {
        var mockSignatureSheetUploadRepository = new Mock<ISignatureSheetUploadRepository>();
        _serviceCollection.AddSingleton<ISignatureSheetUploadRepository>(mockSignatureSheetUploadRepository.Object);
        _serviceProvider = _serviceCollection.BuildServiceProvider();
        mockSignatureSheetUploadRepository
            .Setup(ssur => ssur.CheckUrlAlreadyDownloadedAsync(It.IsAny<int>(), It.IsAny<string>()))
            .ReturnsAsync(true);
        var uploadController = _serviceProvider.GetRequiredService<UploadSignatureSheetsController>();
        ActionResult result = await uploadController.StartUpload(new CreateSignatureSheetUploadDTO
        {
            DownloadUrl = "https://espencerdev.blob.core.windows.net/initial-tiff-1?sv=2023-01-03&st=2024-10-31T14%3A09%3A28Z&se=2024-11-01T14%3A09%3A28Z&sr=c&sp=rl&sig=rz1cYgDYWQKbOI9yRHXd7%2FcV%2FKnrQJRQsWnaGe%2FH0AY%3D",
            MatterId = 1,
            TemplateId = 1,
        }, new CancellationTokenSource().Token);
        var statusCodeResult = result as ObjectResult;
        Assert.IsNotNull(statusCodeResult);
        Assert.AreEqual(409, statusCodeResult.StatusCode);
    }
}