<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Front + Back" type="com.intellij.execution.configurations.multilaunch" factoryName="MultiLaunchConfiguration">
    <rows>
      <ExecutableRowSnapshot>
        <option name="condition">
          <ConditionSnapshot>
            <option name="type" value="immediately" />
          </ConditionSnapshot>
        </option>
        <option name="executable">
          <ExecutableSnapshot>
            <option name="id" value="runConfig:.NET Launch Settings Profile.Backend" />
          </ExecutableSnapshot>
        </option>
      </ExecutableRowSnapshot>
      <ExecutableRowSnapshot>
        <option name="condition">
          <ConditionSnapshot>
            <option name="type" value="immediately" />
          </ConditionSnapshot>
        </option>
        <option name="executable">
          <ExecutableSnapshot>
            <option name="id" value="runConfig:npm.start" />
          </ExecutableSnapshot>
        </option>
      </ExecutableRowSnapshot>
    </rows>
    <method v="2" />
  </configuration>
</component>