# Remove the line below if you want to inherit .editorconfig settings from higher directories
root = true

[*]
indent_style = space
indent_size = 4
end_of_line = lf
charset = utf-8
trim_trailing_whitespace = true
insert_final_newline = true

# C# files
[*.cs]

# Indentation and spacing
indent_size = 4
indent_style = space
tab_width = 4

max_line_length = 120

# New line preferences
end_of_line = crlf
insert_final_newline = false

# TypeScript and TSX files with 2-space indentation
[*.{ts,tsx}]
indent_size = 2

# JavaScript and JSX files
[*.{js,jsx}]
indent_size = 2

# JSON files
[*.json]
indent_size = 2

# YAML files
[*.{yml,yaml}]
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false

# Package files
[{package.json,package-lock.json,yarn.lock,tsconfig.json}]
indent_size = 2
