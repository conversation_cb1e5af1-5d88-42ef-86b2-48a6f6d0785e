﻿using DataInterface.RepositoryInterfaces;
using Model.Matters;
using Model.Templates;

namespace Service
{
    public class TemplateHierarchyService
    {
        private readonly ITemplateRepository _templateRepository;
        private readonly ITemplatePageRepository _templatePageRepository;
        private readonly ITranscribableFieldRepository _transcribableFieldRepository;
        private readonly ITemplateFormLineRepository _formLineRepository;
        private readonly ITemplateSignatureTableRepository _templateSignatureTableRepository;
        private readonly ITemplateSignatureColumnRepository _templateSignatureColumnRepository;
        private readonly ITemplateSignatureRowRepository _templateSignatureRowRepository;
        private readonly ITemplateSignatureCellRepository _templateSignatureCellRepository;
        private readonly SimpleFormRecognizerService _formRecognizerService;
        private readonly TemplateGeometryService _templateGeometryService;
        private readonly PdfManipulationService _pdfManipulationService;

        public TemplateHierarchyService(
            ITemplateRepository templateRepository,
            ITemplatePageRepository templatePageRepository,
            ITranscribableFieldRepository transcribableFieldRepository,
            ITemplateFormLineRepository formLineRepository,
            ITemplateSignatureTableRepository templateSignatureTableRepository,
            ITemplateSignatureColumnRepository templateSignatureColumnRepository,
            ITemplateSignatureRowRepository templateSignatureRowRepository,
            ITemplateSignatureCellRepository templateSignatureCellRepository,
            SimpleFormRecognizerService formRecognizerService,
            TemplateGeometryService templateGeometryService,
            PdfManipulationService pdfManipulationService)
        {
            _templateRepository = templateRepository;
            _templatePageRepository = templatePageRepository;
            _transcribableFieldRepository = transcribableFieldRepository;
            _formLineRepository = formLineRepository;
            _templateSignatureTableRepository = templateSignatureTableRepository;
            _templateSignatureColumnRepository = templateSignatureColumnRepository;
            _templateSignatureRowRepository = templateSignatureRowRepository;
            _templateSignatureCellRepository = templateSignatureCellRepository;
            _formRecognizerService = formRecognizerService;
            _templateGeometryService = templateGeometryService;
            _pdfManipulationService = pdfManipulationService;
        }

        public async Task CreateTemplateHierarchy(Template template, MemoryStream sourceStream)
        {
            var fieldsInForm = _pdfManipulationService.GetListOfLabeledFields(sourceStream);
            sourceStream.Position = 0;

            var matterType = template.PageSize == SupportedPageSize.Legal
                ? MatterType.Initiative
                : MatterType.Candidate;
            var formResults = await _formRecognizerService.RecognizeFormAsync(new Matter{ Type = matterType }, sourceStream, isTemplate: true);
            var results = _templateGeometryService.Transform(template, formResults, fields: fieldsInForm);

            var pagesByPageNumber = results.Pages.ToDictionary(tp => tp.PageNumber, tp => tp);

            await CreateTemplatePages(results.Pages);
            await CreateFormLines(results.FormLines);
            await CreateFields(fieldsInForm, pagesByPageNumber);
            await UpdateLineAndFieldReferences(results.FormLines, fieldsInForm);
            await CreateSignatureTable(results.SignatureTable, results.SignatureColumns, results.SignatureRows);
            //await CreateTranscribableFieldsAsync(fieldsInForm, pagesByPageNumber, results.FormLines);
        }

        private async Task UpdateLineAndFieldReferences(List<TemplateFormLine> formLines, List<TranscribableField> fieldsInForm)
        {
            foreach (var formLine in formLines)
            {
                if (formLine.LeftLine != null)
                {
                    formLine.LeftLineId = formLine.LeftLine.Id;
                    _formLineRepository.SetModified(formLine);
                }
                if (formLine.RightLine != null)
                {
                    formLine.RightLineId = formLine.RightLine.Id;
                    _formLineRepository.SetModified(formLine);
                }
                if (formLine.LeftField != null)
                {
                    formLine.LeftFieldId = formLine.LeftField.Id;
                    _formLineRepository.SetModified(formLine);
                }
                if (formLine.RightField != null)
                {
                    formLine.RightFieldId = formLine.RightField.Id;
                    _formLineRepository.SetModified(formLine);
                }
            }
            await _formLineRepository.SaveChangesAsync();
            foreach (var field in fieldsInForm)
            {
                if (field.LeftLine != null)
                {
                    field.LeftLineId = field.LeftLine.Id;
                    _transcribableFieldRepository.SetModified(field);
                }
                if (field.RightLine != null)
                {
                    field.RightLineId = field.RightLine.Id;
                    _transcribableFieldRepository.SetModified(field);
                }
            }
            await _transcribableFieldRepository.SaveChangesAsync();
        }

        public async Task DeleteTemplateHierarchy(int templateId, bool shouldDeleteTemplate = true)
        {
            await _templateRepository.DeleteTemplateHierarchy(templateId, shouldDeleteTemplate);
        }

        private async Task CreateTemplatePages(List<TemplatePage> templatePages)
        {
            foreach (var templatePage in templatePages)
            {
                _templatePageRepository.Add(templatePage);
            }
            await _templatePageRepository.SaveChangesAsync();
        }

        private async Task CreateFormLines(List<TemplateFormLine> formLines)
        {
            foreach (var formLine in formLines)
            {
                if (formLine.Id == 0)
                {
                    _formLineRepository.Add(formLine);
                    await _formLineRepository.SaveChangesAsync(); // these lines were saving out of order for some reason
                }
            }
            // await _formLineRepository.SaveChangesAsync();
        }

        private async Task CreateFields(List<TranscribableField> fields, Dictionary<int, TemplatePage> templatePages)
        {
            for (int index = 0; index < fields.Count; index++)
            {
                TranscribableField? field = fields[index];
                if (field.Id == 0 && !string.IsNullOrEmpty(field.Name))
                {
                    if (field.Name.Length > 100)
                    {
                        field.Name = field.Name.Substring(0, 100);
                    }
                    field.TemplatePage = templatePages[field.PageNumber];
                    field.FieldNumber = index + 1;
                    _transcribableFieldRepository.Add(field);
                    await _transcribableFieldRepository.SaveChangesAsync(); // these lines were saving out of order for some reason
                }
            }
            // await _transcribableFieldRepository.SaveChangesAsync();
        }

        public async Task CreateSignatureTable(TemplateSignatureTable table, List<TemplateSignatureColumn> columns, List<TemplateSignatureRow> rows)
        {
            _templateSignatureTableRepository.Add(table);
            await _templateSignatureTableRepository.SaveChangesAsync();

            foreach (var column in columns)
            {
                _templateSignatureColumnRepository.Add(column);
            }
            await _templateSignatureColumnRepository.SaveChangesAsync();

            foreach (var row in rows)
            {
                _templateSignatureRowRepository.Add(row);
                foreach (var cell in row.TemplateSignatureCells)
                {
                    cell.TemplateSignatureTableId = table.Id;
                    _templateSignatureCellRepository.Add(cell);
                }
            }
            await _templateSignatureRowRepository.SaveChangesAsync();
        }

        private async Task CreateTranscribableFieldsAsync(List<TranscribableField> fieldsInForm,
            Dictionary<int, TemplatePage> templatePages, List<TemplateFormLine> formLines)
        {
            var fields = CreateTranscribableFields(fieldsInForm, templatePages, formLines);
            _transcribableFieldRepository.AddRange(fields);
            await _transcribableFieldRepository.SaveChangesAsync();
        }

        internal List<TranscribableField> CreateTranscribableFields(List<TranscribableField> fieldsInForm,
            Dictionary<int, TemplatePage> templatePages, List<TemplateFormLine> formLines)
        {
            var fields = new List<TranscribableField>();
            foreach (var fieldInForm in fieldsInForm)
            {
                fieldInForm.TemplatePage = templatePages[fieldInForm.PageNumber];

                var sameLines = GeometryService.GetLinesOnSameLineAsField(fieldInForm, formLines);

                var leftSide = GeometryService.GetLineOnLeft(fieldInForm, sameLines);
                if (leftSide != null)
                {
                    fieldInForm.LeftLine = leftSide;
                }
                else
                {
                    var field = GeometryService.CreateFieldAboveThis(fieldInForm);
                    field.TemplatePage = fieldInForm.TemplatePage;
                    var prevLines = GeometryService.GetLinesOnSameLineAsField(field, formLines);
                    fieldInForm.LeftLine = GeometryService.GetRightMostLine(prevLines);
                }

                var rightSide = GeometryService.GetLineOnRight(fieldInForm, sameLines);
                if (rightSide != null)
                {
                    fieldInForm.RightLine = rightSide;
                }
                else
                {
                    var field = GeometryService.CreateFieldBelowThis(fieldInForm);
                    field.TemplatePage = fieldInForm.TemplatePage;
                    var nextLines = GeometryService.GetLinesOnSameLineAsField(field, formLines);
                    if (nextLines.Any())
                    {
                        fieldInForm.RightLine = GeometryService.GetLeftMostLine(nextLines);
                    }
                    else
                    {
                        int leftLineIndex = fieldInForm.LeftLine == null? 0: formLines.IndexOf(fieldInForm.LeftLine);
                        if (leftLineIndex >= 0)
                        {
                            fieldInForm.RightLine = formLines[leftLineIndex + 1];
                        }
                    }
                }

                fields.Add(fieldInForm);
            }
            return fields;
        }

        private async Task DeleteTemplatePages(List<TemplatePage> templatePages)
        {
            foreach (var templatePage in templatePages)
            {
                _templatePageRepository.Remove(templatePage);
            }
            await _templatePageRepository.SaveChangesAsync();
        }

        private async Task DeleteSignatureTable(TemplateSignatureTable table, List<TemplateSignatureColumn> columns, List<TemplateSignatureRow> rows)
        {
            if (rows is not null && rows.Count > 0)
            {
                foreach (var row in rows)
                {
                    _templateSignatureRowRepository.Remove(row);
                }
                await _templateSignatureRowRepository.SaveChangesAsync();
            }

            if (columns is not null && columns.Count > 0)
            {
                foreach (var column in columns)
                {
                    _templateSignatureColumnRepository.Remove(column);
                }
                await _templateSignatureColumnRepository.SaveChangesAsync();
            }

            _templateSignatureTableRepository.Remove(table);
            await _templateSignatureTableRepository.SaveChangesAsync();

        }
    }
}
