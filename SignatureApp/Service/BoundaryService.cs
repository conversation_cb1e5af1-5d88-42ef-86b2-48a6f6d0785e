﻿using DataInterface.RepositoryInterfaces;
using DataInterface.ServiceInterfaces;
using Microsoft.Extensions.Logging;
using Model.Boundaries;
using Model.Geocoding;

namespace Service;

public class BoundaryService
{
    const double Eps = 0.00000001; // Tolerance for floating point comparison
    private readonly ILogger<BoundaryService> _logger;
    private readonly IBoundaryRepository _boundaryRepository;
    private readonly IBoundaryOverlapRepository _boundaryOverlapRepository;

    public BoundaryService(
        ILogger<BoundaryService> logger,
        IBoundaryRepository boundaryRepository,
        IBoundaryOverlapRepository boundaryOverlapRepository)
    {
        _logger = logger;
        _boundaryRepository = boundaryRepository;
        _boundaryOverlapRepository = boundaryOverlapRepository;
    }

    public async Task<bool> IsPointInBoundary(PointD point, int usStateId, string boundaryName)
    {
        Boundary? boundary = await _boundaryRepository.GetByStateAndNameAsync(usStateId, boundaryName);
        if (boundary == null)
        {
            _logger.LogError($"Boundary {boundaryName} does not exist in the database");
            return false;
        }
        var polygonPoints = boundary.BoundaryPoints
            .OrderBy(p => p.Index)
            .Select(p => new PointD((double)p.Latitude, (double)p.Longitude))
            .ToList();
        var isPointInPolygon = IsPointInPolygon(point, polygonPoints);

        var inOrNot = isPointInPolygon ? "in" : "not in";
        _logger.LogWarning($"Point ({point.X},{point.Y}) is {inOrNot} {boundaryName}");
        return isPointInPolygon;
    }

    public bool IsPointInPolygon(PointD point, IList<PointD> polygon)
    {
        bool result = false;
        var polyPointA = polygon.Last();
        foreach (var polyPointB in polygon)
        {
            if (Math.Abs(polyPointB.X - point.X) < Eps && Math.Abs(polyPointB.Y - point.Y) < Eps)
                return true;

            if (Math.Abs(polyPointB.Y - polyPointA.Y) < Eps && Math.Abs(point.Y - polyPointA.Y) < Eps)
            {
                if ((polyPointA.X <= point.X) && (point.X <= polyPointB.X))
                    return true;

                if ((polyPointB.X <= point.X) && (point.X <= polyPointA.X))
                    return true;
            }

            if ((polyPointB.Y < point.Y) && (polyPointA.Y >= point.Y) || (polyPointA.Y < point.Y) && (polyPointB.Y >= point.Y))
            {
                if (polyPointB.X + (point.Y - polyPointB.Y) / (polyPointA.Y - polyPointB.Y) *
                    (polyPointA.X - polyPointB.X) <= point.X)
                {
                    _logger.LogWarning($"Point ({point.X},{point.Y}) and polygon points ({polyPointA.X},{polyPointA.Y}) and ({polyPointB.X},{polyPointB.Y}) caused result to flip from {result} to {!result}");
                    result = !result;
                }
            }
            polyPointA = polyPointB;
        }
        return result;
    }

    public async Task<bool> IsAddressLineInBoundary(PointD point, int useStateId, string boundsName)
    {
        return await IsPointInBoundary(point, useStateId, boundsName);
    }

    public async Task<bool> IsZipCodeInBoundary(int usStateId, string boundsName, string postalCode)
    {
        return await _boundaryOverlapRepository.DoesBoundaryOverlapAsync(usStateId, boundsName, postalCode);
    }

    public async Task<bool> IsCityInBoundary(int usStateId, string boundsName, string city)
    {
        if (string.IsNullOrEmpty(city))
        {
            _logger.LogError($"City is not provided in the address");
            return false;
        }

        var cityBoundary = await _boundaryRepository.GetByStateTypeAndNameAsync(usStateId, BoundaryType.City, city);
        if (cityBoundary != null)
        {
            return await _boundaryOverlapRepository.DoesBoundaryOverlapAsync(usStateId, boundsName, city);
        }

        var cityPointBoundary = await _boundaryRepository.GetByStateTypeAndNameAsync(usStateId, BoundaryType.CityTownPoint, city);
        if (cityPointBoundary == null)
        {
            _logger.LogError($"Boundary {boundsName} does not exist in the database");
            return false;
        }
        var cityPoint = cityPointBoundary.BoundaryPoints
            .OrderBy(p => p.Index)
            .Select(p => new PointD((double)p.Latitude, (double)p.Longitude))
            .First();

        return await IsPointInBoundary(cityPoint, usStateId, boundsName);
    }
}
