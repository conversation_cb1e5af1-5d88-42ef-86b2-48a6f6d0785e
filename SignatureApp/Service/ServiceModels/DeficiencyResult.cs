using Model.Rules;
using Model.SignatureSheets;

namespace Service.ServiceModels;

public class DeficiencyResult
{
    public int Id { get; set; }
    public string? ImageUrl { get; set; }
    public Rule Rule { get; set; } = default!;
    public string? UserName { get; set; }
    public int RecordId { get; set; }
    public RecordIdType RecordIdType { get; set; }
    public int OtherRecordId { get; set; }
    public RecordIdType OtherRecordIdType { get; set; }
    public int? SignatureCellId { get; set; }
    public int? SignatureRowId { get; set; }
    public int? SignatureSheetFieldId { get; set; }
    public SignatureSheet? SignatureSheet { get; set; }
    public int? SheetNumber { get; set; }
    public int? RowNumber { get; set; }
    public int PageNumber { get; set; }
    public int? ColumnIndex { get; set; }
    public int? FieldIndex { get; set; }
    public int? WorkId { get; set; }
    public string? Note { get; set; }
    public bool? IsReviewed { get; set; }
}
