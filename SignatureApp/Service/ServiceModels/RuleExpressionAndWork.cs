using Model.ExternalDataSources;
using Model.Workflow;

namespace Service.ServiceModels;

public class RuleExpressionAndWork
{
    public required string Expression { get; set; }
    public Work? Work { get; set; }

    public MatterVariableDTO? MatterVariable { get; set; }
    public BoundaryPointDTO? SignatoryPoint { get; set; }
    public Circulator? Circulator { get; set; }
    public Signatory? Signatory { get; set; }

}