using Model.Interfaces;
using Model.Rules;

namespace Service.ServiceModels;

public class DeficiencyInfo
{
    public IHaveSimpleBounds? Bounds { get; set; } = default!;
    public string BadgeNumber { get; set; } = default!;
    public string BadgeDescription { get;  set; } = default!;
    public int PageNumber { get; set; }
    public int? WorkId { get; set; }
    public RecordIdType RecordIdType { get; set; }
    public int RecordId { get; set; }

}