using Model.Workflow;

namespace Service.ServiceModels;

public enum UnreleasedWorkType
{
    None,
    FirstLastRow,
    OtherRows,
    Fields
}

public class FirstLastRowWork
{
    public int FirstRowNumber { get; set; }
    public int LastRowNumber { get; set; }
    public List<Work> Works { get; set; } = [];
}

public class UnreleasedWork
{
    public UnreleasedWorkType UnreleasedWorkType { get; set; }
    public List<Work> Works { get; set; } = [];
}