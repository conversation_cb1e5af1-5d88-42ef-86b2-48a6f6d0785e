﻿using Microsoft.Extensions.Logging;
using Model.Matters;
using Model.SignatureSheets;
using Model.Templates;
using Service.ServiceModels;
using System.Diagnostics;
using System.Globalization;
using System.Text.RegularExpressions;
using AutoMapper;
using DataInterface.ServiceInterfaces;
using Model;
using Model.Interfaces;

namespace Service;

public class SignatureSheetGeometryService : BaseGeometryService
{
    private readonly ILogger<SignatureSheetGeometryService> _logger;
    private readonly IMapper _mapper;
    private readonly PrefixHandlerService _prefixHandlerService;
    private static Regex regexPrefixContext = new Regex(@"[\d\-]+");

    public SignatureSheetGeometryService(
        ILogger<SignatureSheetGeometryService> logger,
        IMapper mapper,
        PrefixHandlerService prefixHandlerService)
    {
        _logger = logger;
        _mapper = mapper;
        _prefixHandlerService = prefixHandlerService;
    }

    public ServiceResult<SignatureSheetResult> Transform(
        MatterType matterType,
        Dictionary<int, TemplatePage> templatePages,
        IList<TranscribableField> transcribableFields,
        TemplateSignatureTable templateTable,
        FormRecognizerResults result,
        SignatureSheetUpload upload,
        int sheetIndex, int sheetNumber,
        IReadOnlyList<TemplateFormLine>? templateFormLines,
        WhichPartsAreValidDTO? whichPartsAreValid = null)
    {
        var signatureSheet = new SignatureSheet
        {
            SignatureSheetUploadId = upload.Id,
            SheetIndex = sheetIndex,
            SheetNumber = sheetNumber,
            TemplateId = upload.TemplateId,
            MatterId = upload.MatterId,
        };
        if (upload.FileName != null)
        {
            signatureSheet.FileName = upload.FileName;
        }

        var pageServiceResults =
            GetFieldsAndFormLines(matterType, templatePages, signatureSheet, result.Pages, transcribableFields,
                templateTable, templateFormLines, whichPartsAreValid?.Fields);
        if (!pageServiceResults.IsSuccess || pageServiceResults.Value == null)
        {
            return ServiceResult<SignatureSheetResult>.Failed(pageServiceResults.ErrorMessages.ToArray());
        }

        var pageResults = pageServiceResults.Value;
        var sheetResults = new SignatureSheetResult
        {
            SignatureSheet = signatureSheet,
            PageResults = pageResults,
        };

        var shift = pageResults.First().ShiftVector;
        var tableResults = GetSignatureRowsAndColumnsFromResultTables(
            result.SignatureTable, templatePages[1], templateTable, pageResults.First().Page, result.Pages[0].Lines,
            signatureSheet, shift, whichPartsAreValid);

        if (!tableResults.IsSuccess || tableResults.Value == null)
        {
            return ServiceResult<SignatureSheetResult>.Failed(tableResults.ErrorMessages.ToArray());
        }

        var signatureSheetTable = tableResults.Value;
        sheetResults.SignatureTable = signatureSheetTable;
        sheetResults.SignatureRows = signatureSheetTable.Rows;
        sheetResults.SignatureColumns = signatureSheetTable.Columns;
        return ServiceResult<SignatureSheetResult>.Succeeded(sheetResults);
    }


    public ServiceResult<SignatureSheetTable> GetSignatureRowsAndColumnsFromResultTables(
        FormResultsTable table,
        TemplatePage templatePage,
        TemplateSignatureTable templateTable,
        SignatureSheetPage thisPage,
        IList<FormResultsLine> linesForPage,
        SignatureSheet signatureSheet,
        double[] shiftVector,
        WhichPartsAreValidDTO? whichPartsAreValid = null)
    {
        if (table.BoundingPolygon.Count != 4)
        {
            throw new Exception($"NON RECTANGULAR TABLE in sheet {signatureSheet.SheetNumber}");
        }

        bool shouldIgnoreValidation = whichPartsAreValid != null;

        var polygon = table.BoundingPolygon;
        var signatureSheetTable = new SignatureSheetTable
        {
            SignatureSheet = signatureSheet,
            Left = (decimal)Math.Min(polygon[0].X, polygon[3].X),
            Top = (decimal)Math.Min(polygon[0].Y, polygon[1].Y),
            Right = (decimal)Math.Max(polygon[1].X, polygon[2].X),
            Bottom = (decimal)Math.Max(polygon[2].Y, polygon[3].Y),
        };
        var templateLeft = templateTable.Left / (decimal)templatePage.Width;
        var templateTop = templateTable.Top / (decimal)templatePage.Height;
        var templateRight = templateTable.Right / (decimal)templatePage.Width;
        var templateBottom = templateTable.Bottom / (decimal)templatePage.Height;

        var thisLeft = signatureSheetTable.Left / (decimal)thisPage.Width;
        var leftBoundary = templateLeft / 2;
        if (thisLeft < leftBoundary && !shouldIgnoreValidation)
        {
            return ServiceResult<SignatureSheetTable>.Failed(
                $"LEFT EDGE OF TABLE in sheet {signatureSheet.SheetNumber}");
        }

        var thisTop = signatureSheetTable.Top / (decimal)thisPage.Height;
        var topBoundary = templateTop / 2;
        if (thisTop < topBoundary && !shouldIgnoreValidation)
        {
            return ServiceResult<SignatureSheetTable>.Failed(
                $"TOP EDGE OF TABLE in sheet {signatureSheet.SheetNumber}");
        }

        var thisRight = signatureSheetTable.Right / (decimal)thisPage.Width;
        var rightBoundary = templateRight + (1.0m - templateRight / 2.0m);
        if (thisRight > rightBoundary && !shouldIgnoreValidation)
        {
            return ServiceResult<SignatureSheetTable>.Failed(
                $"RIGHT EDGE OF TABLE in sheet {signatureSheet.SheetNumber}");
        }

        var thisBottom = signatureSheetTable.Bottom / (decimal)thisPage.Height;
        var bottomBoundary = templateBottom + (1.0m - templateBottom / 2.0m);
        if (thisBottom > bottomBoundary && !shouldIgnoreValidation)
        {
            return ServiceResult<SignatureSheetTable>.Failed(
                $"BOTTOM EDGE OF TABLE in sheet {signatureSheet.SheetNumber}");
        }

        var expectedRows = templateTable.NumberOfRows + 1; // plus the header row
        if (table.RowCount != expectedRows)
        {
            _logger.LogWarning(
                $"Table Row Count {table.RowCount} does not match Template Row Count {templateTable.NumberOfRows + 1}");
        }

        if (table.RowCount <
            expectedRows / 1.5 &&
            !shouldIgnoreValidation) // if we are expecting 16 and we are getting 10, something is clearly wrong
        {
            return ServiceResult<SignatureSheetTable>.Failed(
                $"NOT ENOUGH ROWS in sheet {signatureSheet.SheetNumber}");
        }

        if (table.ColumnCount != templateTable.NumberOfCols)
        {
            _logger.LogWarning(
                $"Table Column Count {table.ColumnCount} does not match Template Column Count {templateTable.NumberOfCols}");
        }

        if (table.ColumnCount < templateTable.NumberOfCols - 1 &&
            !shouldIgnoreValidation) // if we are off by more than one
        {
            return ServiceResult<SignatureSheetTable>.Failed(
                $"NOT ENOUGH COLUMNS in sheet {signatureSheet.SheetNumber}");
        }

        List<FormResultsCell> columnHeaders =
            GetColumnHeaders(table, templatePage, templateTable, thisPage, signatureSheetTable, shiftVector);
        if (columnHeaders.Count != templateTable.NumberOfCols)
        {
            return ServiceResult<SignatureSheetTable>.Failed("Unable to parse column headers");
        }

        List<SignatureSheetColumn> columns =
            CreateColumnsAlternate(signatureSheetTable, columnHeaders, whichPartsAreValid);

        List<SignatureSheetRow> rows = CreateRowsAndCellsAlternate(templateTable, signatureSheetTable, columnHeaders,
            linesForPage, templatePage, thisPage, shiftVector, whichPartsAreValid);

        signatureSheetTable.Rows = rows;
        signatureSheetTable.Columns = columns;
        signatureSheetTable.Cells = rows.SelectMany(row => row.Cells).ToList();

        if (signatureSheetTable.Cells.Count != templateTable.NumberOfRows * templateTable.NumberOfCols
            && !shouldIgnoreValidation)
        {
            return ServiceResult<SignatureSheetTable>.Failed(
                $"NUMBER OF CELLS in table {signatureSheetTable.Cells.Count} does not match " +
                $"NUMBER OF CELLS in template {templateTable.NumberOfRows * templateTable.NumberOfCols} " +
                $"on sheet {signatureSheet.SheetNumber}");
        }

        return ServiceResult<SignatureSheetTable>.Succeeded(signatureSheetTable);
    }

    private List<FormResultsCell> GetColumnHeaders(FormResultsTable table, TemplatePage templatePage,
        TemplateSignatureTable templateTable, SignatureSheetPage thisPage, SignatureSheetTable signatureSheetTable,
        double[] shiftVector)
    {
        var potentialColumnHeaders = table.Cells.Where(c => c.Kind == CellKind.ColumnHeader).ToList();
        var columnHeaders = potentialColumnHeaders;

        // if the number of column Headers does not equal the number of columns in the table
        var numberOfColumnsEqual = potentialColumnHeaders.Count == templateTable.NumberOfCols;
        var areColumnWidthsOk = numberOfColumnsEqual && ArePotentialColumnHeaderWidthsOk(
            potentialColumnHeaders, templateTable.Columns, templatePage.Width, thisPage.Width, shiftVector);
        if (!areColumnWidthsOk)
        {
            columnHeaders = CreateColumnHeaders(table, templatePage, templateTable, thisPage, signatureSheetTable,
                potentialColumnHeaders, shiftVector);
        }

        return columnHeaders;
    }

    private bool ArePotentialColumnHeaderWidthsOk(List<FormResultsCell> potentialColumnHeaders,
        IList<TemplateSignatureColumn> templateTableColumns, float templatePageWidth, float thisPageWidth,
        double[] shiftVector)
    {
        for (int index = 0; index < templateTableColumns.Count; index++)
        {
            var col = potentialColumnHeaders[index];
            // Compute the ideal width of the column header
            var idealColumn =
                GeometryService.HorizontallyTransformBounds(templateTableColumns[index], (decimal)thisPageWidth,
                    shiftVector);
            var allowableDifference = (float)(idealColumn.Right - idealColumn.Left) / 10.0f; // less than 10%
            if (Math.Abs(col.BoundingPolygon[1].X - col.BoundingPolygon[0].X) >= allowableDifference)
            {
                return false;
            }
        }

        return true;
    }

    private List<SignatureSheetColumn> CreateColumnsAlternate(
        SignatureSheetTable signatureSheetTable,
        List<FormResultsCell> columnHeaders,
        WhichPartsAreValidDTO? whichPartsAreValid = null)
    {
        var columns = new List<SignatureSheetColumn>();
        foreach (var columnHeader in columnHeaders)
        {
            var isPresent = whichPartsAreValid?.Columns[columnHeader.ColumnIndex] ?? true;
            var signatureSheetColumn = new SignatureSheetColumn
            {
                SignatureSheetTableId = signatureSheetTable.Id,
                ColumnIndex = columnHeader.ColumnIndex,
                Left = (decimal)columnHeader.BoundingPolygon[0].X,
                Top = signatureSheetTable.Top,
                Right = (decimal)columnHeader.BoundingPolygon[1].X,
                Bottom = signatureSheetTable.Bottom,
                IsMissing = !isPresent,
            };
            columns.Add(signatureSheetColumn);
        }

        return columns;
    }

    private List<SignatureSheetRow> CreateRowsAndCellsAlternate(
        TemplateSignatureTable templateTable,
        SignatureSheetTable signatureSheetTable,
        List<FormResultsCell> columnHeaders,
        IList<FormResultsLine> linesForPage,
        IHaveDimensions templatePage, IHaveDimensions thisPage,
        double[] shiftVector,
        WhichPartsAreValidDTO? whichPartsAreValid = null)
    {
        var rows = new List<SignatureSheetRow>();
        var tableLeft = signatureSheetTable.Left;
        var tableRight = signatureSheetTable.Right;
        for (int rowIndex = 0; rowIndex < templateTable.NumberOfRows; rowIndex++)
        {
            var thisRow = GeometryService.TransformBounds(
                templateTable.Rows[rowIndex], thisPage, shiftVector);

            var isRowPresent = whichPartsAreValid?.Rows[rowIndex] ?? true;

            var signatureSheetRow = new SignatureSheetRow
            {
                SignatureSheet = signatureSheetTable.SignatureSheet,
                SignatureSheetTableId = signatureSheetTable.Id,
                TemplateSignatureTableId = templateTable.Id,
                RowIndex = rowIndex,
                RowNumber = rowIndex + 1,
                Left = tableLeft,
                Top = thisRow.Top,
                Right = tableRight,
                Bottom = thisRow.Bottom,
                IsMissing = !isRowPresent,
            };
            rows.Add(signatureSheetRow);

            CreateCellsForRowAlternate(templateTable, columnHeaders, rowIndex, signatureSheetRow, linesForPage,
                thisPage.Width, shiftVector, whichPartsAreValid);
        }

        return rows;
    }

    private void CreateCellsForRowAlternate(
        TemplateSignatureTable templateTable,
        List<FormResultsCell> columnHeaders,
        int outputRowIndex,
        SignatureSheetRow signatureSheetRow,
        IList<FormResultsLine> linesForPage,
        float thisPageWidth,
        double[] shiftVector,
        WhichPartsAreValidDTO? whichPartsAreValid = null
    )
    {
        var orderedColumnHeaders = columnHeaders.OrderBy(ch => ch.BoundingPolygon[0].X).ToList();
        for (int tableHeaderIndex = 0, templateHeaderIndex = 0;
             tableHeaderIndex < columnHeaders.Count && templateHeaderIndex < templateTable.Columns.Count;)
        {
            var templateHeader = templateTable.Columns[templateHeaderIndex];

            // We want to take these template bounds and make them look like sheet bounds so that we can compare them
            var templateToSheetBounds = GeometryService.HorizontallyTransformBounds(
                templateHeader, (decimal)thisPageWidth, shiftVector);

            var isSkipped = templateHeader.IsHandwritten == false || templateHeader.Name == "SKIPPED";
            var tableHeader = orderedColumnHeaders[tableHeaderIndex];
            var tableBounds = GeometryService.GetBoundsFromPolygon(tableHeader.BoundingPolygon.AsReadOnly());

            var overlapPercent =
                GeometryService.WidthPercentOverlap(templateToSheetBounds.Left, templateToSheetBounds.Right,
                    tableBounds.Left, tableBounds.Right);

            var isRowPresent = whichPartsAreValid?.Rows[outputRowIndex] ?? true;
            var isColPresent = whichPartsAreValid?.Columns[templateHeader.ColumnIndex] ?? true;
            var isPresent = isRowPresent && isColPresent;

            if (overlapPercent > 0.8m)
            {
                AddSignatureSheetCell(outputRowIndex, signatureSheetRow, isSkipped, templateHeader,
                    templateHeader.ColumnIndex,
                    tableBounds.Left, tableBounds.Right, isMissing: !isPresent);
            }
            else
            {
                AddSignatureSheetCell(outputRowIndex, signatureSheetRow, isSkipped, templateHeader,
                    templateHeader.ColumnIndex,
                    templateToSheetBounds.Left, templateToSheetBounds.Right, isMissing: !isPresent);
            }

            tableHeaderIndex++;
            templateHeaderIndex++;
        }

        AssignLinesToCellValues(signatureSheetRow, linesForPage);
    }

    public void AssignLinesToCellValues(SignatureSheetRow signatureSheetRow, IList<FormResultsLine> linesForPage)
    {
        var linesForRow = linesForPage.Where(line =>
            line.BoundingPolygon[3].Y > (float)signatureSheetRow.Top
            && line.BoundingPolygon[0].Y < (float)signatureSheetRow.Bottom);
        //Debug.WriteLine($"Potential lines for row {outputRowIndex} {JsonSerializer.Serialize(linesForRow.Select(l => l.Content))}");

        foreach (var line in linesForRow)
        {
            //Debug.WriteLine($"Searching for a home for '{line.Content}'");
            //bool foundHome = false;
            //var percentages = new List<double>();
            foreach (var tableCell in signatureSheetRow.Cells)
            {
                if (tableCell.IsReviewed)
                {
                    continue;
                }

                var cellBounds = GeometryService.GetCellBounds(tableCell);
                // We don't want to expand the row number cell, or the signature cell
                if (tableCell.ColumnIndex > 1)
                {
                    cellBounds = GeometryService.ExpandBounds(cellBounds, .1m, .2m);
                }

                var lineBounds = GeometryService.GetBoundsFromPolygon(line.BoundingPolygon);
                bool isContainedWithin = GeometryService.IsContainedWithin(cellBounds, lineBounds);
                var overlapPercent = GeometryService.PercentOverlap2D(cellBounds, lineBounds);
                //percentages.Add(overlapPercent);
                if (isContainedWithin || overlapPercent > .5m)
                {
                    //foundHome = true;
                    tableCell.Value += line.Content;
                    if (tableCell.Value.Length > StringLengthConstants.CellValue)
                    {
                        tableCell.Value = tableCell.Value.Substring(0, StringLengthConstants.CellValue);
                    }
                }
            }
            //if (!foundHome)
            //{
            //    Debug.WriteLine($"No home for '{line.Content}' {JsonSerializer.Serialize(percentages)}");
            //}
        }
    }

    private static void AddSignatureSheetCell(int outputRowIndex, SignatureSheetRow signatureSheetRow, bool isSkipped,
        TemplateSignatureColumn templateColumn, int columnIndex,
        decimal left, decimal right, bool isMissing)
    {
        var tableCell = new SignatureSheetCell
        {
            IsReviewed = isSkipped,
            // TemplateSignatureTableId = signatureSheetTable.Id,
            SignatureSheetRowId = signatureSheetRow.Id,
            TemplateSignatureColumnId = templateColumn.Id, // TODO: Why don't we have this?
            ColumnIndex = columnIndex,
            RowIndex = outputRowIndex,
            Value = string.Empty,
            Left = left,
            Top = signatureSheetRow.Top,
            Right = right,
            Bottom = signatureSheetRow.Bottom,
            IsMissing = isMissing,
        };
        signatureSheetRow.Cells.Add(tableCell);
    }

    private Dictionary<int, SignatureSheetPage> GetSignatureSheetPagesFromResultPages(SignatureSheet signatureSheet,
        IReadOnlyList<FormResultsPage> pages)
    {
        return pages.ToDictionary(page => page.PageNumber, page => new SignatureSheetPage
        {
            SignatureSheet = signatureSheet,
            Height = page.Height,
            Width = page.Width,
            PageNumber = page.PageNumber,
        });
    }

    private List<FormResultsCell> CreateColumnHeaders(FormResultsTable table, TemplatePage templatePage,
        TemplateSignatureTable templateTable, SignatureSheetPage thisPage, SignatureSheetTable signatureTable,
        List<FormResultsCell> potentialColumnHeaders, double[] shiftVector)
    {
        var columnHeaders = new List<FormResultsCell>();
        var maxRowSpan = 1;
        if (potentialColumnHeaders.Any())
        {
            maxRowSpan = potentialColumnHeaders.Max(c => c.RowSpan);
        }

        var firstRowCells = table.Cells.Where(c => c.RowIndex == 0 && c.RowSpan == maxRowSpan).ToList();
        if (!firstRowCells.Any())
        {
            firstRowCells = table.Cells.Where(c => c.RowIndex == 0).ToList();
        }

        // TODO: if there are no firstRowCells how do we compute the top and bottom? using template values?
        var averageTop = firstRowCells.Average(c => c.BoundingPolygon[0].Y); // 0 and 1
        var averageBottom = firstRowCells.Average(c => c.BoundingPolygon[2].Y); // 2 and 3

        foreach (var templateColumn in templateTable.Columns)
        {
            var templateColumnBounds = GeometryService.HorizontallyTransformBounds(
                templateColumn, (decimal)thisPage.Width, shiftVector);
            templateColumnBounds.Top = (decimal)averageTop;
            templateColumnBounds.Bottom = (decimal)averageBottom;
            var columnHeader = CreateColumnHeaderFromResultsCell(table, templateColumn.ColumnIndex,
                templateColumnBounds, templateColumn.Name);
            columnHeaders.Add(columnHeader);
        }

        return columnHeaders;
    }

    public ServiceResult<List<SignatureSheetPageResult>> GetFieldsAndFormLines(
        MatterType matterType,
        Dictionary<int, TemplatePage> templatePages,
        SignatureSheet signatureSheet,
        IReadOnlyList<FormResultsPage> formResultPages,
        IList<TranscribableField> transcribableFields,
        TemplateSignatureTable templateSignatureTable,
        IReadOnlyList<TemplateFormLine>? templateFormLines,
        bool[]? whichFieldsAreValid = null)
    {
        if (matterType == MatterType.Candidate && templateFormLines == null)
        {
            throw new Exception("Candidate matters need template form lines to match fields");
        }

        var pagesResults = new List<SignatureSheetPageResult>();
        var sheetPages = GetSignatureSheetPagesFromResultPages(signatureSheet, formResultPages);

        foreach (var page in formResultPages)
        {
            var pageNumber = page.PageNumber;
            var templatePage = templatePages[pageNumber];
            var thisPage = sheetPages[pageNumber];
            var pageResults = new SignatureSheetPageResult { Page = thisPage };

            var (bucketsAndLines, pageFormLines) = GetSignatureSheetFormLines(matterType, templatePages, page);

            var shiftVector =
                ComputeTemplateToSheetShift(templatePage, thisPage, templatePage.FormLines, pageFormLines);
            pageResults.ShiftVector = shiftVector;
            var shiftedSignatureTable = GeometryService.TransformBounds(templateSignatureTable,
                thisPage, shiftVector);

            IList<SignatureSheetField> signatureSheetFields;
            if (matterType == MatterType.Candidate && templateFormLines != null && templatePage.PageNumber == 1)
            {
                var templatePageFormLines = templateFormLines.Where(l => l.TemplatePageId == templatePage.Id).ToList();
                var matchInfo = MapLinesInTemplateToLinesInSheet(matterType, templatePage, thisPage,
                    templatePageFormLines, pageFormLines, templateSignatureTable, shiftedSignatureTable);

                signatureSheetFields = transcribableFields
                    .Where(tf => tf.PageNumber == templatePage.PageNumber)
                    .Select(tf => CreateSignatureSheetFieldUsingLineMatching(
                        templatePage, thisPage, thisPage.SignatureSheet, templateSignatureTable,
                        matchInfo, tf, shiftVector, whichFieldsAreValid))
                    .ToList();
            }
            else
            {
                var results = transcribableFields
                    .Where(tf => tf.PageNumber == templatePage.PageNumber)
                    .Select(tf => CreateSignatureSheetFieldUsingPosition(
                        templatePage, thisPage, thisPage.SignatureSheet,
                        pageFormLines, tf, shiftVector, whichFieldsAreValid))
                    .ToList();
                var failed = results.Where(result => !result.IsSuccess).ToList();
                if (failed.Any())
                {
                    var errors = failed.SelectMany(r => r.ErrorMessages).ToArray();
                    return ServiceResult<List<SignatureSheetPageResult>>.Failed(errors);
                }

                signatureSheetFields = results
                    .Where(r => r.Value != null)
                    .Select(r => r.Value)
                    .Cast<SignatureSheetField>()
                    .ToList();
            }

            pageResults.FormLines = pageFormLines;
            pageResults.Fields = signatureSheetFields;
            pageResults.BucketAndLines = bucketsAndLines;

            pagesResults.Add(pageResults);
        }

        return ServiceResult<List<SignatureSheetPageResult>>.Succeeded(pagesResults);
    }

    public (BucketAndLine[] bucketsAndLines, List<SignatureSheetFormLine> formLines) GetSignatureSheetFormLines(
        MatterType matterType,
        Dictionary<int, TemplatePage> templatePages,
        FormResultsPage page)
    {
        var pageFormLines = new List<SignatureSheetFormLine>();
        var pageTranscribableFields = new List<TranscribableField>();
        var templatePage = templatePages[page.PageNumber];

        var ignoredWords = matterType == MatterType.Candidate
            ? Constants.CandidateTemplateIgnoredWords
            : Constants.InitiativeTemplateIgnoredWords;

        var combinedLinesAndFields = page.Lines
            .Select(line => new ContentArea { Object = line, BoundingPolygon = line.BoundingPolygon })
            .ToList();
        var bucketsAndLines = OrderLines(combinedLinesAndFields.AsReadOnly()).ToArray();

        bool recording = false;
        for (int index = 0; index < bucketsAndLines.Length; index++)
        {
            var bucketAndLine = bucketsAndLines[index];
            var line = bucketAndLine.LineOrField as FormResultsLine;
            var field = bucketAndLine.LineOrField as TranscribableField;
            if (field != null)
            {
                field.TemplatePage = templatePage;
            }

            var fieldName = field != null ? $"[{field.Name}]" : "";

            if (line?.Content != null)
            {
                line.Content = matterType == MatterType.Candidate
                    ? FixCandidateMisspellings(line.Content)
                    : FixInitiativeMisspellings(line.Content);
                bool shouldStartRecording = ShouldStartRecording(matterType, line.Content);
                if (!recording && shouldStartRecording)
                {
                    Debug.WriteLine($"Started recording on {line.Content}");
                    recording = true;
                }

                bool shouldStopRecording = line.Content == "Signature"; // TODO: This is dangerous
                if (recording && shouldStopRecording)
                {
                    Debug.WriteLine($"Stopped recording on {line.Content}");
                    recording = false;
                }

                if (ignoredWords.Contains(line.Content))
                {
                    continue;
                }
            }

            if (recording)
            {
                Debug.WriteLine($"{bucketAndLine.LineNumber} {bucketAndLine.AverageX} {line?.Content}{fieldName}");
            }

            var previousBucketAndLine = index > 0 ? bucketsAndLines[index - 1] : null;
            var previousLine = previousBucketAndLine?.LineOrField as FormResultsLine;
            var previousField = previousBucketAndLine?.LineOrField as TranscribableField;

            if (line != null)
            {
                var formLineValue = line.Content;
                if (recording && matterType == MatterType.Candidate && formLineValue != null)
                {
                    formLineValue = RemoveParenthesesFromFormLineValue(formLineValue);
                }

                var formLine = new SignatureSheetFormLine
                {
                    //SignatureSheetPage = templatePage,
                    Left = (decimal)Math.Min(line.BoundingPolygon[0].X, line.BoundingPolygon[3].X),
                    Top = (decimal)Math.Min(line.BoundingPolygon[0].Y, line.BoundingPolygon[1].Y),
                    Right = (decimal)Math.Max(line.BoundingPolygon[1].X, line.BoundingPolygon[2].X),
                    Bottom = (decimal)Math.Max(line.BoundingPolygon[2].Y, line.BoundingPolygon[3].Y),
                    Value = formLineValue,
                };

                if (recording)
                {
                    if (previousLine != null && pageFormLines.Count > 0)
                    {
                        var previousFormLine = pageFormLines.Last();
                        formLine.LeftLine = previousFormLine;
                        previousFormLine.RightLine = formLine;
                    }
                    else if (previousField != null && pageTranscribableFields.Count > 0)
                    {
                        //previousField.RightLine = formLine;
                        formLine.LeftField = previousField;
                    }
                }

                pageFormLines.Add(formLine);
            }
            else if (field != null)
            {
                if (recording)
                {
                    if (previousLine != null && pageFormLines.Count > 0)
                    {
                        //field.LeftLine = pageFormLines.Last();
                    }
                }

                pageTranscribableFields.Add(field);
            }
        }

        return (bucketsAndLines, pageFormLines);
    }

    private bool ShouldStartRecording(MatterType matterType, string lineContent)
    {
        if (matterType == MatterType.Candidate)
        {
            return lineContent.StartsWith("I,");
        }

        if (matterType == MatterType.Initiative)
        {
            return lineContent.StartsWith("Initiative Description:")
                   || lineContent.StartsWith("STATEWIDE ONLY")
                   || lineContent.StartsWith("COUNTY")
                   || lineContent.StartsWith("Instructions for Circulators");
        }

        return false;
    }

    private string RemoveParenthesesFromFormLineValue(string formLineValue)
    {
        var leftParenIndex = formLineValue.IndexOf('(');
        var rightParenIndex = formLineValue.IndexOf(')');
        // is there a parenthetical remark?
        if (leftParenIndex >= 0 && rightParenIndex >= 0)
        {
            var before = "";
            if (leftParenIndex > 0)
            {
                before = formLineValue.Substring(0, leftParenIndex);
            }

            var after = "";
            if (rightParenIndex < formLineValue.Length)
            {
                var start = rightParenIndex + 1;
                after = formLineValue.Substring(start, formLineValue.Length - start);
            }

            formLineValue = before + after;
            // TODO: if there are sections before and after the parens, it should be two lines
            // TODO: technically we should shrink the geometry as well
        }

        return formLineValue;
    }

    private string FixInitiativeMisspellings(string lineContent)
    {
        return lineContent;
    }

    private string FixCandidateMisspellings(string line)
    {
        line = line.Replace("ofice", "office");
        line = line.Replace("1,", "I,");
        line = line.Replace("!,", "I,");
        line = line.Replace("Circulater", "Circulator");
        line = line.Replace("fille", "file");
        line = line.Replace("qualifled", "qualified");
        return line;
    }

    public double[] ComputeTemplateToSheetShift(
        TemplatePage templatePage, SignatureSheetPage thisPage,
        IList<TemplateFormLine> templateFormLines, IList<SignatureSheetFormLine> sheetFormLines)
    {
        var matches = MatchTemplateLinesToSheetLines(
            templateFormLines, sheetFormLines);
        _logger.LogInformation(
            $"Found {matches.Count} matches on sheet {thisPage.SignatureSheet?.SheetNumber}({thisPage.PageNumber})");
        foreach (var match in matches)
        {
            _logger.LogInformation($"{match.templateLine.Value} " +
                                   $"({match.templateLine.Left / (decimal)templatePage.Width:F4},{match.templateLine.Top / (decimal)templatePage.Height:F4} -> " +
                                   $"({match.singatureSheetLine.Left / (decimal)thisPage.Width:F4},{match.singatureSheetLine.Top / (decimal)thisPage.Height:F4})");
        }

        _logger.LogInformation(
            $"End matches on sheet {thisPage.SignatureSheet?.SheetNumber}({thisPage.PageNumber})");

        var srcPoints = matches.Select(m => new[]
        {
            new PointD((double)m.templateLine.Left, (double)m.templateLine.Top),
            new PointD((double)m.templateLine.Right, (double)m.templateLine.Bottom)
        }).SelectMany(p => p).ToArray();

        var dstPoints = matches.Select(m => new[]
        {
            new PointD((double)m.singatureSheetLine.Left, (double)m.singatureSheetLine.Top),
            new PointD((double)m.singatureSheetLine.Right, (double)m.singatureSheetLine.Bottom)
        }).SelectMany(p => p).ToArray();

        var matrix = AffineTransformationService.ComputeBestFitAffineTransform(srcPoints, dstPoints);
        return matrix;
    }

    private List<(TemplateFormLine templateLine, SignatureSheetFormLine singatureSheetLine)>
        MatchTemplateLinesToSheetLines(
            IList<TemplateFormLine> templateFormLines, IList<SignatureSheetFormLine> sheetFormLines)
    {
        var matches = (
            from tl in templateFormLines
            where tl.IsShiftable == false
            join ssl in sheetFormLines on tl.Value equals ssl.Value
            select (tl, ssl)
        ).ToList();
        return matches.ToList();
    }

    private MatchInformation MapLinesInTemplateToLinesInSheet(
        MatterType matterType,
        TemplatePage templatePage,
        SignatureSheetPage thisPage,
        IList<TemplateFormLine> templateFormLines,
        IList<SignatureSheetFormLine> sheetFormLines,
        IHaveSimpleBounds templateTableBounds,
        IHaveSimpleBounds sheetTableBounds,
        int numberOfLinesSheetCanBeAheadOfTemplate = 2
    )
    {
        var matchInfo = new MatchInformation { LinesMatched = true };

        int templateIndex = 0;
        int thisIndex = 0;
        var lastMatch = (templateIndex: -1, thisIndex: -1);
        bool refreshTemplateValue = true;
        bool refreshThisValue = true;
        TemplateFormLine templateLine;
        SignatureSheetFormLine thisLine;
        string? templateValue = null;
        string? thisValue = null;
        var templateIgnoredWords = matterType == MatterType.Candidate
            ? Constants.CandidateTemplateIgnoredWords.Select(w => w.ToLower()).ToArray()
            : Constants.InitiativeTemplateIgnoredWords.Select(w => w.ToLower()).ToArray();
        var sheetIgnoredWords = matterType == MatterType.Candidate
            ? Constants.CandidateSheetIgnoredWords.Select(w => w.ToLower()).ToArray()
            : Constants.InitiativeSheetIgnoredWords.Select(w => w.ToLower()).ToArray();

        var theseFormLines = sheetFormLines.Select(fl => new SignatureSheetFormLine
        {
            SignatureSheetPage = thisPage,
            Left = fl.Left,
            Top = fl.Top,
            Right = fl.Right,
            Bottom = fl.Bottom,
            Value = fl.Value,
        }).ToList();
        matchInfo.SignatureSheetFormLines = theseFormLines;
        while (templateIndex < templateFormLines.Count && thisIndex < theseFormLines.Count)
        {
            templateLine = templateFormLines[templateIndex];
            thisLine = theseFormLines[thisIndex];
            Debug.WriteLine(
                $"template[{templateIndex}]={templateLine.Value}\t|\tthis[{thisIndex}]={thisLine.Value}");

            // Stop comparing at the table
            if (thisLine.SignatureSheetPage?.PageNumber == 1)
            {
                bool templateLineBelowTable = templateLine.Top > templateTableBounds.Top;
                if (templateLineBelowTable)
                {
                    Debug.WriteLine("TEMPLATE BELOW TABLE");
                    templateIndex++;
                }

                bool thisLineBelowTable = thisLine.Top > sheetTableBounds.Top;
                if (thisLineBelowTable)
                {
                    Debug.WriteLine("THIS BELOW TABLE");
                    thisIndex++;
                }

                if (templateLineBelowTable || thisLineBelowTable)
                {
                    Debug.WriteLine("SKIPPING");
                    continue;
                }
            }

            if (string.IsNullOrEmpty(templateLine.Value)) // || !templateLine.Value.Any(ch => char.IsLetter(ch)))
            {
                templateIndex++;
                continue;
            }

            if (string.IsNullOrEmpty(thisLine.Value)) // || !thisLine.Value.Any(ch => char.IsLetter(ch)))
            {
                thisIndex++;
                continue;
            }

            if (refreshTemplateValue)
            {
                templateValue = templateLine.Value!
                    .StripPunctuation()
                    .RemoveParentheticalRemarks() // Hack apparently parens are optional
                    .Trim()
                    .ToLower();
                Debug.WriteLine($"templateValue = {templateValue}");
                if (string.IsNullOrEmpty(templateValue))
                {
                    templateIndex++;
                    continue;
                }
            }
            else
            {
                refreshTemplateValue = true;
            }

            if (refreshThisValue)
            {
                thisValue = theseFormLines[thisIndex].Value!
                    .StripPunctuation()
                    .RemoveExtraneousCapitalI() // Hack too close to box
                    .Trim()
                    .ToLower();
                Debug.WriteLine($"thisValue = {thisValue}");
                if (string.IsNullOrEmpty(thisValue))
                {
                    thisIndex++;
                    continue;
                }
            }
            else
            {
                refreshThisValue = true;
            }

            if (templateValue == thisValue)
            {
                Debug.WriteLine("MATCHED");
                lastMatch = (templateIndex, thisIndex);
                thisLine.TemplateFormLineId = templateLine.Id;
                UpdateValuesInDictionary(matchInfo, templateLine, thisLine);
                templateIndex++;
                thisIndex++;
            }

            // Hack - ignore one word items in the template ('Put', 'optional', 'photo', 'here')
            else if (templateValue?.Split(' ').Length == 1 && templateIgnoredWords.Contains(templateValue))
            {
                Debug.WriteLine("IGNORED TEMPLATE");
                templateIndex++;
                thisIndex = lastMatch.thisIndex + 1;
            }
            else if (thisValue?.Split(' ').Length == 1 && sheetIgnoredWords.Contains(thisValue))
            {
                Debug.WriteLine("IGNORED SHEET");
                thisIndex++;
                templateIndex = lastMatch.templateIndex + 1;
            }
            else if (templateValue != null && thisValue != null && thisValue.StartsWith(templateValue))
            {
                Debug.WriteLine("THIS starts with TEMPLATE");
                lastMatch = (templateIndex, thisIndex);
                thisValue = thisValue.Substring(templateValue!.Length).Trim();
                Debug.WriteLine($"New this value: {thisValue}");
                UpdateValuesInDictionary(matchInfo, templateLine, thisLine);
                refreshThisValue = false;
                templateIndex++;
            }
            else if (templateValue != null && thisValue != null && templateValue.StartsWith(thisValue))
            {
                Debug.WriteLine("TEMPLATE starts with THIS");
                lastMatch = (templateIndex, thisIndex);
                templateValue = templateValue.Substring(thisValue.Length).Trim();
                Debug.WriteLine($"New template value: {templateValue}");
                UpdateValuesInDictionary(matchInfo, templateLine, thisLine);
                refreshTemplateValue = false;
                thisIndex++;
            }
            else if (thisIndex - lastMatch.thisIndex <= numberOfLinesSheetCanBeAheadOfTemplate)
                // the actual sheet can be off by some items at the top
            {
                Debug.WriteLine(
                    $"THIS <= {numberOfLinesSheetCanBeAheadOfTemplate} AHEAD of TEMPLATE (adding {thisLine.Value}) {thisValue}");
                //TODO: This still isn't right, but it is close
                if (templateIndex > 0)
                {
                    thisLine.LeftLineId = templateFormLines[templateIndex - 1].Id;
                }

                matchInfo.InSheetButNotInTemplate.Add(thisLine);
                thisIndex++;
            }
            else if (templateIndex - lastMatch.templateIndex <= 1) // the template can be ahead by one item only
            {
                Debug.WriteLine($"TEMPLATE <=1 ahead of THIS (adding {templateLine.Value}) {templateValue}");
                matchInfo.InTemplateButNotInSheet.Add(templateLine);
                templateIndex++;
                thisIndex = lastMatch.thisIndex + 1;
            }
            else
            {
                Debug.WriteLine("OFF THE RAILS");
                thisIndex++;
                _logger.LogWarning(
                    $"Sheet verbiage does not match: {templateValue} != {thisValue} and ({thisIndex} - {lastMatch.thisIndex} > 2), and ({templateIndex} - {lastMatch.Item1} > 1)");
                // Mark the sheet as invalid because the verbiage doesn't match?
                matchInfo.LinesMatched = false;
            }
        }

        return matchInfo;
    }

    private static void UpdateValuesInDictionary(MatchInformation matchInfo,
        //Dictionary<string, SignatureSheetFormLine> templateFormLineIdToReferencingLine,
        TemplateFormLine templateLine, SignatureSheetFormLine thisLine)
    {
        matchInfo.Matches.Add((templateLine, thisLine));

        // last left should win
        matchInfo.TemplateFormLineIdToReferencingLine[templateLine.Id + "Left"] = thisLine;

        // first right should win
        matchInfo.TemplateFormLineIdToReferencingLine.TryAdd(templateLine.Id + "Right", thisLine);
    }

    private SignatureSheetField CreateSignatureSheetFieldUsingLineMatching(
        TemplatePage templatePage,
        SignatureSheetPage thisPage,
        SignatureSheet signatureSheet,
        TemplateSignatureTable templateSignatureTable,
        MatchInformation matchInfo,
        TranscribableField templateField,
        double[] shiftVector,
        bool[]? whichFieldsAreValid = null)
    {
        // TODO: Technically any code using these values should change to use Transform instead
        var widthRatio = (decimal)(thisPage.Width / templatePage.Width);
        var heightRatio = (decimal)(thisPage.Height / templatePage.Height);
        var thisFieldHeight = (templateField.Bottom - templateField.Top) * heightRatio;
        var isPresent = whichFieldsAreValid?[templateField.FieldNumber - 1] ?? true;
        var signatureSheetField = CreateSignatureSheetField(signatureSheet, templateField, isMissing: !isPresent);
        SignatureSheetFormLine? leftLine = null;
        if (templateField.LeftLineId != null)
        {
            Debug.WriteLine($"Matching values for {templateField.Name}");
            if (matchInfo.TemplateFormLineIdToReferencingLine.TryGetValue(templateField.LeftLineId.Value + "Left",
                    out leftLine))
            {
                Debug.WriteLine(
                    $"Found Left Line {templateField.LeftLineId.Value + "Left"} {leftLine.Id} {leftLine.Value}");
                var possibleFields = matchInfo.InSheetButNotInTemplate
                    .Where(fl => fl.LeftLineId == templateField.LeftLineId).ToArray();
                if (possibleFields.Length == 1)
                {
                    Debug.WriteLine(
                        $"Found one field with the same left line {possibleFields[0].Id} {possibleFields[0].Value}");
                    signatureSheetField.Value = possibleFields[0].Value;
                }
                else
                {
                    Debug.WriteLine($"Issue: {possibleFields.Length} fields with the same left line");
                    if (possibleFields.Length > 1)
                    {
                        signatureSheetField.Value = string.Join(' ', possibleFields.Select(p => p.Value));
                    }
                    else
                    {
                        _logger.LogWarning(
                            $"Unable to match the SignatureSheetField to the TemplateField LineId={templateField.LeftLineId.Value}");
                    }
                }
            }
        }

        SignatureSheetFormLine? rightLine = null;
        if (templateField.RightLineId != null)
        {
            matchInfo.TemplateFormLineIdToReferencingLine.TryGetValue(templateField.RightLineId.Value + "Right",
                out rightLine);
        }

        if (thisPage.PageNumber == 1 && leftLine != null && rightLine != null)
        {
            bool areSame = GeometryService.AreOnSameLine(leftLine, rightLine);
            if (!areSame && rightLine.Left < leftLine.Right)
            {
                var rightEdge = templateSignatureTable.Right * widthRatio;
                var leftEdge = templateSignatureTable.Left * widthRatio;

                var leftLineRightToRightEdge = rightEdge - leftLine.Right;
                var rightLineLeftToLeftEdge = rightLine.Left - leftEdge;
                if (leftLineRightToRightEdge > rightLineLeftToLeftEdge)
                {
                    signatureSheetField.Left = leftLine.Right;
                    signatureSheetField.Top = leftLine.Bottom - thisFieldHeight;
                    signatureSheetField.Right = rightEdge;
                    signatureSheetField.Bottom = leftLine.Bottom;
                }
                else
                {
                    signatureSheetField.Left = leftEdge;
                    signatureSheetField.Top = rightLine.Bottom - thisFieldHeight;
                    signatureSheetField.Right = rightLine.Left;
                    signatureSheetField.Bottom = rightLine.Bottom;
                }
            }
            else
            {
                signatureSheetField.Left = leftLine.Right;
                signatureSheetField.Top = leftLine.Bottom - thisFieldHeight;
                signatureSheetField.Right = rightLine.Left;
                signatureSheetField.Bottom = rightLine.Bottom;
            }
        }
        else
        {
            GeometryService.TransformBoundsInto(templateField, thisPage, shiftVector, signatureSheetField);
        }

        return signatureSheetField;
    }

    private static SignatureSheetField CreateSignatureSheetField(SignatureSheet signatureSheet,
        TranscribableField templateField, bool isMissing)
    {
        return new SignatureSheetField
        {
            SignatureSheet = signatureSheet,
            TranscribableField = templateField,
            TranscribableFieldId = templateField.Id,
            Page = templateField.PageNumber,
            IsMissing = isMissing,
        };
    }

    private ServiceResult<SignatureSheetField> CreateSignatureSheetFieldUsingPosition(
        TemplatePage templatePage,
        SignatureSheetPage thisPage,
        SignatureSheet signatureSheet,
        IList<SignatureSheetFormLine> linesForPage,
        TranscribableField templateField,
        double[] shiftVector,
        bool[]? whichFieldsAreValid = null)
    {
        var isPresent = whichFieldsAreValid?[templateField.FieldNumber - 1] ?? true;
        var signatureSheetField = CreateSignatureSheetField(signatureSheet, templateField, isMissing: !isPresent);

        if (isPresent)
        {
            var newShiftedBounds = GeometryService.RawTransformBounds(templateField, thisPage, shiftVector);
            if (GeometryService.IsOutOfBounds(newShiftedBounds, thisPage))
            {
                var errorMessage = $"{templateField.Name} is no longer on the page";
                _logger.LogError(errorMessage);
                return ServiceResult<SignatureSheetField>.Failed(errorMessage);
            }
        }

        GeometryService.TransformBoundsInto(templateField, thisPage, shiftVector, signatureSheetField);

        // get the possible lines where
        // the bottom of the line is below the top of the field.
        // the top of the line is above the bottom of the field.
        // the left of the line less than the right of the field
        // the right of the line is greater than the left of the field
        var possibleLines = linesForPage.Where(line =>
            line.Bottom > signatureSheetField.Top
            && line.Top < signatureSheetField.Bottom
            && line.Left < signatureSheetField.Right
            && line.Right > signatureSheetField.Left);

        foreach (var line in possibleLines)
        {
            if (line.Value is null)
            {
                continue;
            }

            var lineBounds = line as IHaveSimpleBounds;
            var fieldBounds = signatureSheetField as IHaveSimpleBounds;
            fieldBounds = GeometryService.ExpandBounds(fieldBounds, .1m, .25m); //TODO: don't hardcode

            bool isContainedWithin = GeometryService.IsContainedWithin(fieldBounds, lineBounds);
            string? overlappingText = null;
            if (!isContainedWithin)
            {
                var verticalOverlap = GeometryService.PercentOverlap1D(
                    fieldBounds.Top, fieldBounds.Bottom,
                    lineBounds.Top, lineBounds.Bottom);
                if (verticalOverlap > 0.5m)
                {
                    if (line.TryGetOverlapPercent(fieldBounds, out decimal start, out decimal stop))
                    {
                        int total = line.Value.Length;
                        int startIndex = (int)Math.Floor(start * total);
                        int endIndex = (int)Math.Ceiling(stop * total);
                        overlappingText = line.Value[startIndex .. endIndex];
                    }
                }
            }

            if (isContainedWithin || overlappingText != null)
            {
                string lineValue = overlappingText ?? line.Value;
                Console.WriteLine($"{signatureSheetField.TranscribableField.Name} contains {lineValue}");
                if (signatureSheetField.TranscribableField.IsDate)
                {
                    lineValue = FixDateContent(lineValue);
                }

                if (signatureSheetField.TranscribableField.IsPrefixed &&
                    !string.IsNullOrWhiteSpace(signatureSheetField.TranscribableField.Prefix))
                {
                    var handlerFunction =
                        _prefixHandlerService.GetHandler(signatureSheetField.TranscribableField.Prefix);
                    lineValue = handlerFunction(lineValue);
                }

                if (!string.IsNullOrEmpty(signatureSheetField.Value))
                {
                    if (signatureSheetField.TranscribableField.InputType == InputType.TextArea)
                    {
                        signatureSheetField.Value += "\n";
                    }
                    else
                    {
                        signatureSheetField.Value += " ";
                    }
                }

                signatureSheetField.Value += lineValue;
            }
        }

        if (signatureSheetField.Value?.Length > StringLengthConstants.FieldValue)
        {
            signatureSheetField.Value = signatureSheetField.Value.Substring(0, StringLengthConstants.FieldValue);
        }

        return ServiceResult<SignatureSheetField>.Succeeded(signatureSheetField);
    }

    private static string FixDateContent(string lineValue)
    {
        lineValue = lineValue.Replace('.', '-');
        if (lineValue.Contains("Expires "))
        {
            var parts = lineValue.Split("Expires ");
            if (parts.Length > 1)
            {
                lineValue = parts[1];
            }
        }

        if (!DateTime.TryParse(lineValue, CultureInfo.CurrentCulture, out var dateTime))
        {
            Console.WriteLine($"Line content {lineValue} did not contain a date, ignoring");
            return string.Empty;
        }

        return lineValue;
    }
}