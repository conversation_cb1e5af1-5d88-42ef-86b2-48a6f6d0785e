using System.Text.RegularExpressions;
using DataInterface.RepositoryInterfaces;
using Model.SignatureSheets;
using Model.Workflow;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Service.ServiceModels;

namespace Service;

public class RuleExpressionHelper
{
    private readonly JsonSerializer _serializer = new() { ReferenceLoopHandling = ReferenceLoopHandling.Ignore };
    public static readonly Regex RegexSheetFields = new Regex(@"\.Fields\[['""]{1,2}(?<fieldName>[^'""]+)['""]{1,2}\]");
    public static readonly Regex RegexRowCells = new Regex(@"\.Cells\[['""]{1,2}(?<columnName>[^'""]+)['""]{1,2}\]");
    public static readonly Regex RegexMatterVariable =
        new Regex(@"\.Variables\[['""]{1,2}(?<variableName>[^'""]+)['""]{1,2}\]");

    private readonly IWorkRepository _workRepository;

    public RuleExpressionHelper(IWorkRepository workRepository)
    {
        _workRepository = workRepository;
    }

    public async Task<Work?> GetFieldWorkFromRuleExpressionAsync(
        RuleContext ruleContext,
        int signatureSheetId,
        string ruleExpression)
    {
        Work? work = null;
        JToken token = JToken.FromObject(ruleContext, _serializer);
        var valueSuffix = ".Value";
        if (ruleExpression.EndsWith(valueSuffix))
        {
            ruleExpression = ruleExpression.Substring(0,
                ruleExpression.Length - valueSuffix.Length);
        }

        ruleExpression = ExpandShorthandNotation(ruleExpression);
        var expressionValue = token.SelectToken(ruleExpression);
        if (expressionValue != null)
        {
            var signatureSheetField =
                JsonConvert.DeserializeObject<SignatureSheetField>(expressionValue.ToString());
            if (signatureSheetField != null)
            {
                work = await _workRepository.GetBySheetIdAndFieldNumber(signatureSheetId,
                    signatureSheetField.TranscribableField.FieldNumber);
            }
        }

        return work;
    }

    //.Cells['SignatureDate'] => .Cells[?(@..TemplateSignatureColumn.Name == 'SignatureDate')]
    //.Fields['CandidateCounty'] => .Fields[?(@..TranscribableField.Name == 'CandidateCounty')]
    public string ExpandShorthandNotation(string expression)
    {
        // Most of the time ${} in C# is bad, but here because it is JSON path, it is OK
        expression =
            RegexRowCells.Replace(expression, ".Cells[?(@..TemplateSignatureColumn.Name == '${columnName}')]");
        expression =
            RegexSheetFields.Replace(expression, ".Fields[?(@..TranscribableField.Name == '${fieldName}')]");
        expression =
            IRuleRepository.regexMatterVariables.Replace(expression, ".Variables[?(@..Key == '${variableName}')]");

        return expression;
    }

    public string ExtractFieldName(string expression)
    {
        var match = RegexSheetFields.Match(expression);
        return match.Success ? match.Groups["fieldName"].Value : string.Empty;
    }

    public string ExtractColumnName(string expression)
    {
        var match = RegexRowCells.Match(expression);
        return match.Success ? match.Groups["columnName"].Value : string.Empty;
    }

    public string ExtractVariableName(string expression)
    {
        var match = RegexMatterVariable.Match(expression);
        return match.Success ? match.Groups["variableName"].Value : string.Empty;
    }

}