using DataInterface.RepositoryInterfaces;
using DataInterface.ServiceInterfaces;
using Microsoft.Extensions.Logging;
using Model.Authorization;
using Model.DTOs;
using Model.Interfaces;
using Model.Matters;
using Model.Rules;
using Model.SignatureSheets;
using Model.Templates;
using Model.Workflow;
using Service.ServiceModels;
using Task = System.Threading.Tasks.Task;

namespace Service;

public class WorkService
{
    private readonly CirculatorService _circulatorService;
    private readonly DeficiencyService _deficiencyService;
    private readonly ImageCreationService _imageCreationService;
    private readonly IDeficiencyRepository _deficiencyRepository;
    private readonly IMatterRepository _matterRepository;
    private readonly ISignatureSheetCellRepository _signatureSheetCellRepository;
    private readonly ISignatureSheetFieldRepository _signatureSheetFieldRepository;
    private readonly ISignatureSheetRepository _signatureSheetRepository;
    private readonly ISignatureSheetRowRepository _signatureSheetRowRepository;
    private readonly IRuleRepository _ruleRepository;
    private readonly ITaskRepository _taskRepository;
    private readonly IWorkRepository _workRepository;
    private readonly IWorkFieldRepository _workFieldRepository;
    private readonly RulesEngine _rulesEngine;
    private readonly SignatoryService _signatoryService;
    private readonly ISignatureSheetUploadRepository _signatureSheetUploadRepository;
    private readonly ILogger<WorkService> _logger;

    private const int SystemReviewerId = -4; // This is the hardcoded ID for the system reviewer;


    public WorkService(
        CirculatorService circulatorService,
        DeficiencyService deficiencyService,
        ImageCreationService imageCreationService,
        IDeficiencyRepository deficiencyRepository,
        IMatterRepository matterRepository,
        IRuleRepository ruleRepository,
        ISignatureSheetCellRepository signatureSheetCellRepository,
        ISignatureSheetFieldRepository signatureSheetFieldRepository,
        ISignatureSheetRowRepository signatureSheetRowRepository,
        ITaskRepository taskRepository,
        IWorkRepository workRepository,
        IWorkFieldRepository workFieldRepository,
        RulesEngine rulesEngine,
        SignatoryService signatoryService, ISignatureSheetRepository signatureSheetRepository,
        ISignatureSheetUploadRepository signatureSheetUploadRepository,
        ILogger<WorkService> logger)
    {
        _circulatorService = circulatorService;
        _deficiencyService = deficiencyService;
        _imageCreationService = imageCreationService;
        _deficiencyRepository = deficiencyRepository;
        _rulesEngine = rulesEngine;
        _signatoryService = signatoryService;
        _signatureSheetRepository = signatureSheetRepository;
        _signatureSheetUploadRepository = signatureSheetUploadRepository;
        _logger = logger;
        _matterRepository = matterRepository;
        _ruleRepository = ruleRepository;
        _signatureSheetCellRepository = signatureSheetCellRepository;
        _signatureSheetFieldRepository = signatureSheetFieldRepository;
        _signatureSheetRowRepository = signatureSheetRowRepository;
        _taskRepository = taskRepository;
        _workRepository = workRepository;
        _workFieldRepository = workFieldRepository;
    }

    public void AddActiveMattersWithNoWork(Dictionary<int, Matter> activeMatters, List<MatterWorkStatusDTO> dtos)
    {
        var dtosByMatterId = dtos.Select(dto => dto.MatterId).Distinct().ToList();
        foreach (var activeMatter in activeMatters)
        {
            if (!dtosByMatterId.Contains(activeMatter.Key))
            {
                var newDto = new MatterWorkStatusDTO
                {
                    MatterId = activeMatter.Key,
                    MatterName = activeMatter.Value.Name,
                    MatterDueDate = activeMatter.Value.DueDate,
                    MatterCreatedOn = activeMatter.Value.CreatedOn,
                    Count = 0,
                    WorkStatus = WorkStatus.None
                };
                dtos.Add(newDto);
            }
        }
    }

    public async Task<Work?> CheckForNullOrPreviousStrikethrough(User user, Work nextUnassignedWork)
    {
        var field = nextUnassignedWork.WorkFields.FirstOrDefault();
        if (field == null)
        {
            await UpdateWorkInternal(new FinishWorkDTO
            {
                WorkStatusId = WorkStatus.Completed,
            }, user, nextUnassignedWork);
            await _workRepository.SaveChangesAsync();
            return null;
        }

        if (field.FieldType == FieldType.SignatureSheetCell)
        {
            // Load the cell and row for this work
            var cell = await _signatureSheetCellRepository.GetByIdIncludingSignatureSheetAsync(field.FieldId);
            if (cell == null)
            {
                return null;
            }

            var row = await _signatureSheetRowRepository.GetByIdIncludingSheetAndCellsAsync(cell.SignatureSheetRowId);
            if (row == null)
            {
                return null;
            }

            // if any of the cells are marked as Strikethrough,
            if (row.Cells.Any(c => c.Validity == Validity.Strikethrough))
            {
                cell.Validity = Validity.Strikethrough;
                cell.IsReviewed = true;
                cell.ReviewedBy = user.Email;
                cell.ReviewedOn = DateTime.UtcNow;
                // then Mark this one as Strikethrough
                var workFieldDTO = CreateWorkFieldDTO(
                    cell.Id, cell.TemplateSignatureColumn.Name, cell.Value,
                    cell.TemplateSignatureColumn.CanBeInvalid,
                    cell.Validity,
                    cell.TemplateSignatureColumn.IsSignature == true, null, null, null);
                await UpdateWorkInternal(new FinishWorkDTO
                {
                    ExternalDataRecordId = cell.Id,
                    Fields = new List<WorkFieldDTO> { workFieldDTO },
                    RegisteredVoterFlags = RegisteredVoterFlags.Strikethrough,
                    WorkStatusId = WorkStatus.Completed,
                }, user, nextUnassignedWork);

                // and load another
                return null;
            }
        }

        return nextUnassignedWork;
    }

    public async Task<bool> UpdateWorkInternal(FinishWorkDTO dto, User user, Work work)
    {
        var stopTime = DateTime.UtcNow;
        work.WorkStatus = dto.WorkStatusId;
        if (dto.WorkStatusId == WorkStatus.Completed)
        {
            work.LastStopDateTime = stopTime;
        }

        var seconds = (stopTime - work.LastStartDateTime).TotalSeconds;
        if (seconds > 0)
        {
            work.SecondsWorked += seconds;
        }

        var wasSuccessful = await UpdateSignatureFieldsAndCellsAsync(dto, work, isReviewed: true, user);
        return wasSuccessful;
    }

    private async Task<bool> UpdateSignatureFieldsAndCellsAsync(FinishWorkDTO dto, Work work, bool isReviewed,
        User user)
    {
        if (work.Task.TaskType == TaskType.SheetReview)
        {
            await _signatureSheetCellRepository.SaveChangesAsync();
            return true;
        }

        if (dto.Fields is null || dto.Fields.Count == 0)
        {
            return false;
        }

        var needsExternalDataSourceVerification = (work.Task.TaskType & TaskType.ExternalDataSourceVerification) != 0;

        SignatureSheetCell? signatureCell = null;
        var previousCellValidity = Validity.Unknown;
        foreach (var field in dto.Fields)
        {
            IHaveId record;
            RecordIdType recordIdType;
            Validity previousValidState;
            int matterId;
            if (work.Task.TaskType == TaskType.TranscribableField)
            {
                var signatureSheetField =
                    await _signatureSheetFieldRepository.GetByIdIncludingSignatureSheetAsync(field.Id);
                if (signatureSheetField is null) return false;
                previousCellValidity = signatureSheetField.Validity;
                previousValidState = signatureSheetField.Validity;
                signatureSheetField.Value = field.Value;
                signatureSheetField.IsReviewed = isReviewed;
                signatureSheetField.ReviewedBy = user.Email;
                signatureSheetField.ReviewedOn = DateTime.UtcNow;
                signatureSheetField.Validity = field.Validity;
                await UpdateSignatureSheetReviewedAsync(signatureSheetField.SignatureSheet);
                record = signatureSheetField;
                recordIdType = RecordIdType.SignatureSheetField;
                matterId = signatureSheetField.SignatureSheet.MatterId;
            }
            else
            {
                signatureCell = await _signatureSheetCellRepository.GetByIdIncludingSignatureSheetAsync(field.Id);
                if (signatureCell is null) return false;
                previousCellValidity = signatureCell.Validity;
                previousValidState = signatureCell.Validity;
                signatureCell.Value = field.Value;
                signatureCell.IsReviewed = isReviewed;
                signatureCell.ReviewedBy = user.Email;
                signatureCell.ReviewedOn = DateTime.UtcNow;
                signatureCell.Validity = field.Validity;
                if (!needsExternalDataSourceVerification)
                {
                    await UpdateRowReviewedAsync(signatureCell.SignatureSheetRow, user);
                }

                record = signatureCell;
                recordIdType = RecordIdType.SignatureSheetCell;
                matterId = signatureCell.SignatureSheetRow.SignatureSheet.MatterId;
            }

            if (previousValidState != field.Validity)
            {
                AddOrRemove addOrRemove = AddOrRemove.None;
                if (field.Validity == Validity.Invalid)
                    addOrRemove = AddOrRemove.Add;
                else if (previousValidState == Validity.Invalid)
                    addOrRemove = AddOrRemove.Remove;
                if (addOrRemove != AddOrRemove.None)
                {
                    var rule = field.ViolationId != null
                        ? await _ruleRepository.GetByIdAsync((int)field.ViolationId)
                        : null;
                    await _deficiencyService.AddOrRemoveTranscriptionDeficiencyAsync(addOrRemove, record, recordIdType,
                        rule, matterId, work.Id, user.Id, field.ViolationNote);
                }
            }
        }

        if (needsExternalDataSourceVerification)
        {
            var row = signatureCell?.SignatureSheetRow;
            if (row is null) return false;
            row.RegisteredVoterId = dto.RegisteredVoterFlags == RegisteredVoterFlags.Strikethrough
                ? null
                : dto.ExternalDataRecordId;
            var previousVoterFlags = row.RegisteredVoterFlags;
            if (dto.RegisteredVoterFlags != RegisteredVoterFlags.Unknown)
            {
                row.RegisteredVoterFlags = dto.RegisteredVoterFlags;
            }

            await UpdateRowReviewedAsync(row, user);

            if (previousVoterFlags != row.RegisteredVoterFlags)
            {
                var rules = await _ruleRepository.GetAllByTaskAndMatterIdAsync(work.TaskId,
                    row.SignatureSheet.MatterId);
                var violation = GetViolationFromRegisteredVoterFlags(rules, row.RegisteredVoterFlags);
                AddOrRemove addOrRemove = AddOrRemove.None;
                if (AreRegisteredVoterFlagsInvalid(row.RegisteredVoterFlags))
                    addOrRemove = AddOrRemove.Add;
                else if (AreRegisteredVoterFlagsInvalid(previousVoterFlags))
                    addOrRemove = AddOrRemove.Remove;
                if (addOrRemove != AddOrRemove.None)
                {
                    await _deficiencyService.AddOrRemoveTranscriptionDeficiencyAsync(addOrRemove, row,
                        RecordIdType.SignatureSheetRow, violation, row.SignatureSheet.MatterId, work.Id, user.Id, "");
                }
            }
        }

        if (signatureCell?.Validity == Validity.Strikethrough)
        {
            // If the cell is strikethrough, we need to mark all the cells in the row as strikethrough
            var rowCells = await _signatureSheetCellRepository.GetAllByRowIdAsync(signatureCell.SignatureSheetRowId);
            var works = await _workRepository.GetAllForMatterSheetAndRowNumberAsync(work.MatterId, work.SheetNumber,
                signatureCell.SignatureSheetRow.RowNumber);
            foreach (var cell in rowCells.Where(cell => cell.Validity != Validity.Strikethrough))
            {
                if (cell.Validity == Validity.Invalid)
                {
                    await _deficiencyService.AddOrRemoveTranscriptionDeficiencyAsync(AddOrRemove.Remove, cell,
                        RecordIdType.SignatureSheetCell, null, cell.SignatureSheetRow.SignatureSheet.MatterId);
                }

                cell.Validity = Validity.Strikethrough;
                cell.IsReviewed = true;
                cell.ReviewedBy = user.Email;
                cell.ReviewedOn = DateTime.UtcNow;
            }

            foreach (var w in works)
            {
                _logger.LogInformation($"Updating work {w.Id} for strikethrough");
                w.UserId = w.Id == work.Id ? work.UserId : SystemReviewerId;
                w.WorkStatus = WorkStatus.Completed;
                w.LastStartDateTime = DateTime.UtcNow;
                w.LastStopDateTime = DateTime.UtcNow;
            }
        }
        else if (previousCellValidity == Validity.Strikethrough && signatureCell?.Validity != Validity.Strikethrough &&
                 signatureCell != null)
        {
            // If the cell no longer struckthrough, we need to update validity to unknown
            var rowCells = await _signatureSheetCellRepository.GetAllByRowIdAsync(signatureCell.SignatureSheetRowId);
            foreach (var cell in rowCells.Where(rc => rc.Validity == Validity.Strikethrough))
            {
                cell.Validity = Validity.Unknown;
                cell.IsReviewed = false;
                cell.ReviewedBy = null;
                cell.ReviewedOn = null;
            }

            work.UserId = user.Id;
            work.WorkStatus = WorkStatus.Assigned;
        }

        await _signatureSheetCellRepository.SaveChangesAsync();
        return true;
    }

    private Rule? GetViolationFromRegisteredVoterFlags(List<Rule> rules, RegisteredVoterFlags registeredVoterFlags)
    {
        if (!rules.Any()) return null;
        /*
         * ({matterId}, {voterRegistrationTask.Id}, 'Registered Voter Name Mismatch', 1, '', '', ''),
         * ({ matterId}, { voterRegistrationTask.Id}, 'Registered Voter Address Mismatch', 1, '', '', ''),
         * ({ matterId}, { voterRegistrationTask.Id}, 'Voter Not Registered', 1, '', '', ''),";" +
         */
        if (registeredVoterFlags == RegisteredVoterFlags.MismatchedName)
        {
            return rules.Single(r => r.Name.Contains("Name Mismatch"));
        }

        if (registeredVoterFlags == RegisteredVoterFlags.MismatchedAddress)
        {
            return rules.Single(r => r.Name.Contains("Address Mismatch"));
        }

        if (registeredVoterFlags == RegisteredVoterFlags.NotRegistered)
        {
            return rules.Single(r => r.Name.Contains("Not Registered"));
        }

        return null;
    }

    private static bool AreRegisteredVoterFlagsInvalid(RegisteredVoterFlags voterFlags)
    {
        return voterFlags == RegisteredVoterFlags.MismatchedName
            || voterFlags == RegisteredVoterFlags.MismatchedAddress
            || voterFlags == RegisteredVoterFlags.NotRegistered;
    }

    private async Task UpdateSignatureSheetReviewedAsync(SignatureSheet signatureSheet)
    {
        if (!signatureSheet.IsTableReviewed)
        {
            if (!signatureSheet.Rows.Any())
            {
                signatureSheet.Rows =
                    await _signatureSheetRowRepository.GetRowsAndCellsBySignatureSheetIdAsync(signatureSheet.Id);
            }

            var allRowsReviewed = signatureSheet.Rows.All(r => r.IsReviewed || r.Validity == Validity.Strikethrough);
            if (allRowsReviewed)
            {
                signatureSheet.IsTableReviewed = true;
                signatureSheet.TableReviewedOn = DateTime.UtcNow;
                await _signatureSheetRepository.SaveChangesAsync();
            }
        }

        if (!signatureSheet.AreFieldsReviewed)
        {
            signatureSheet.Fields =
                await _signatureSheetFieldRepository.GetAllBySignatureSheetIdAsync(signatureSheet.Id);
            var transcribableFields =
                signatureSheet.Fields.Select(f => f.TranscribableField)
                    .ToList();

            var allFieldsReviewed =
                signatureSheet.Fields.All(f => f.IsReviewed); // track separately from table is reviewed
            if (allFieldsReviewed)
            {
                signatureSheet.AreFieldsReviewed = true;
                signatureSheet.FieldsReviewedOn = DateTime.UtcNow;
                await _signatureSheetRepository.SaveChangesAsync();
            }

            await _circulatorService.UpdateCirculatorFromSheetAsync(signatureSheet, transcribableFields);
        }
    }

    public async Task<ServiceResult<string>> CheckWorkCompleteThenRunRulesAsync(int matterId, int signatureSheetId)
    {
        var isSheetWorkComplete = await _workRepository.IsAllWorkBySheetIdCompleteAsync(signatureSheetId);
        if (!isSheetWorkComplete)
        {
            return ServiceResult<string>.Succeeded("Rules not run; sheet work is not complete");
        }

        var allWorkComplete = await _workRepository.IsAllWorkCompleteAsync(matterId);
        if (allWorkComplete)
        {
            await _rulesEngine.EvaluateAllAsync(matterId);
            return ServiceResult<string>.Succeeded("All rules evaluated");
        }

        if (isSheetWorkComplete)
        {
            var matter = await _matterRepository.GetMatterIncludingVariablesAsync(matterId);
            if (matter == null)
            {
                return ServiceResult<string>.Failed($"Matter {matterId} not found");
            }

            var signatureSheet = await _signatureSheetRepository.GetByIdAsync(signatureSheetId);
            if (signatureSheet == null)
            {
                return ServiceResult<string>.Failed($"Signature sheet {signatureSheetId} not found");
            }

            var rules = await _ruleRepository.GetAllByMatterIdAsync(matterId);
            var uploads = await _signatureSheetUploadRepository.GetAllByMatterIdAsync(matterId);
            var templateIds = uploads.Select(u => u.TemplateId).Distinct().ToList();
            await _rulesEngine.EvaluateSheetRulesAsync(matter, signatureSheet, rules);
            return ServiceResult<string>.Succeeded($"Rules for {signatureSheetId} evaluated");
        }

        return ServiceResult<string>.Succeeded("Rules not run; sheet work is not complete");
    }

    public async Task<bool> DtoIsFlaggedOrBreakAsync(Work work, FinishWorkDTO dto, User user)
    {
        if (dto.WorkStatusId == WorkStatus.Break)
        {
            work.UserId = null;
            work.WorkStatus = WorkStatus.None;
            work.SecondsWorked = 0;
            work.AssignmentDate = null;
            var updateResults = await UpdateSignatureFieldsAndCellsAsync(dto, work, false, user);

            _workRepository.SetModified(work);
            await _workRepository.SaveChangesAsync();
            return true;
        }

        if (dto.WorkStatusId == WorkStatus.Flagged)
        {
            var stopTime = DateTime.UtcNow;
            work.LastStopDateTime = stopTime;

            var seconds = (stopTime - work.LastStartDateTime).TotalSeconds;
            if (seconds > 0)
            {
                work.SecondsWorked += seconds;
            }

            var updateResults = await UpdateSignatureFieldsAndCellsAsync(dto, work, false, user);

            work.UserId = null;
            work.WorkStatus = WorkStatus.Flagged;

            _workRepository.SetModified(work);
            await _workRepository.SaveChangesAsync();
            return true;
        }

        return false;
    }

    public async Task<WorkDTO> GetWorkDTOByWorkAsync(Work work)
    {
        var response = new WorkDTO { Fields = new List<WorkFieldDTO>() };
        IHaveSimpleBounds? boundsToCrop = null;
        IHaveSimpleBounds? boundsToHighlight = null;
        IHaveSimpleBounds? amountToStretch = null;

        var task = await _taskRepository.GetByIdIncludeRulesAsync(work.TaskId);
        if (task is null)
        {
            return MarkResponseNotFound(response, "Task not found");
        }

        work.LastStartDateTime = DateTime.UtcNow;
        var sigSheetId = work.SignatureSheetId;
        int pageNumber = 1; // assuming table is on the front

        _workRepository.SetModified(work);
        await _workRepository.SaveChangesAsync();

        response.SheetNumber = work.SheetNumber;
        response.RowNumber = work.RowNumber;
        response.FieldNumber = work.FieldNumber;
        response.MatterId = work.MatterId;
        response.MatterName = work.Matter.Name;
        response.WorkId = work.Id;
        response.TaskDescription = task.Description;
        response.TaskTypeId = task.TaskType;
        response.TaskName = task.Name;
        response.CanBeInvalid = task.CanBeInvalid;

        if (task.TaskType == TaskType.ExternalDataSourceVerification)
        {
            response.ViolationCriteria = GetFieldViolationCriteriaFromTaskRules(task.Rules);
        }

        response.ShowWholeColumn = task.ShowWholeColumn ?? false;

        List<WorkField>? workFields = [];
        if (task.TaskType == TaskType.SheetReview)
        {
            response.ExecutionStatus = ExecutionStatus.Succeeded;
            return response;
        }

        workFields = await _workFieldRepository.GetByWorkIdAsync(work.Id);
        if (workFields is null || !workFields.Any())
        {
            return MarkResponseNotFound(response, "No WorkFields Found");
        }

        var fieldIds = workFields.Select(wf => wf.FieldId).ToArray();
        if (task.TaskType == TaskType.TranscribableField)
        {
            var sigSheetFields =
                await _signatureSheetFieldRepository.GetByIdsWithTranscribableFieldAsync(fieldIds);
            foreach (var sigSheetField in sigSheetFields)
            {
                var fieldId = sigSheetField.Id;
                if (sigSheetField is null) return MarkResponseNotFound(response, "Signature Sheet Not found");

                int? violationId = null;
                string? violationNote = null;
                if (sigSheetField.Validity == Validity.Invalid)
                {
                    var deficiency =
                        (await _deficiencyRepository.GetDeficienciesByRecordAsync(sigSheetField.Id,
                            RecordIdType.SignatureSheetField))
                        .Where(d => d.Rule.RuleContextType == RuleContextType.Transcription).SingleOrDefault();
                    violationId = deficiency?.RuleId;
                    violationNote = deficiency?.Note;
                }

                var inputType = sigSheetField.TranscribableField.InputType;
                var fieldDto = CreateWorkFieldDTO(
                    fieldId,
                    sigSheetField.TranscribableField.Name,
                    sigSheetField.Value,
                    task.CanBeInvalid,
                    sigSheetField.Validity,
                    sigSheetField.TranscribableField.IsSignature,
                    GetFieldViolationCriteriaFromTaskRules(task.Rules),
                    violationId, violationNote,
                    inputType);
                response.Fields.Add(fieldDto);

                sigSheetId = sigSheetField.SignatureSheetId;
                pageNumber = sigSheetField.Page;
            }

            amountToStretch = new CellBounds { Left = 0.1m, Right = 0.1m, Top = 0.2m, Bottom = 0.2m };
            boundsToHighlight = GetHighlightBounds(sigSheetFields.Cast<IHaveSimpleBounds>().ToList());
            boundsToCrop = boundsToHighlight;
        }
        else
        {
            var signatureRows = await _signatureSheetRowRepository.GetByCellIdsWithSignatureTableColumnAsync(fieldIds);
            if (signatureRows is null || signatureRows.Count != 1)
                return MarkResponseNotFound(response, "SignatureRow with Cells Not Found");

            var signatureSheetRow = signatureRows.First();
            sigSheetId = signatureSheetRow.SignatureSheetId;
            response.ExternalDataRecordId = signatureSheetRow.RegisteredVoterId;
            response.RegisteredVoterFlags = signatureSheetRow.RegisteredVoterFlags;
            var signatureCells = signatureSheetRow.Cells.Where(c => fieldIds.Contains(c.Id)).ToList();

            var allTasks = await _taskRepository.GetAllByTemplateAndMatterId(task.TemplateId, work.MatterId);
            foreach (var signatureCell in signatureCells)
            {
                var cellTask = allTasks.SingleOrDefault(t => t.TaskType == TaskType.SignatureTableColumn
                    && signatureCell.ColumnIndex >= t.FirstColumnIndex &&
                    signatureCell.ColumnIndex <= t.LastColumnIndex);

                int? violationId = null;
                string? violationNote = null;
                if (signatureCell.Validity == Validity.Invalid)
                {
                    var deficiency =
                        (await _deficiencyRepository.GetDeficienciesByRecordAsync(signatureCell.Id,
                            RecordIdType.SignatureSheetCell))
                        .Where(d => d.Rule.RuleContextType == RuleContextType.Transcription).SingleOrDefault();
                    violationId = deficiency?.RuleId;
                    violationNote = deficiency?.Note;
                }

                var fieldDto = CreateWorkFieldDTO(
                    signatureCell.Id,
                    signatureCell.TemplateSignatureColumn.Name,
                    signatureCell.Value,
                    signatureCell.TemplateSignatureColumn.CanBeInvalid,
                    signatureCell.Validity,
                    signatureCell.TemplateSignatureColumn.IsSignature == true,
                    GetCellViolationCriteriaFromTask(cellTask, signatureCell.TemplateSignatureColumn),
                    violationId, violationNote);
                response.Fields.Add(fieldDto);
            }

            (boundsToCrop, amountToStretch) = await GetCropBoundsFromTaskAsync(task, signatureSheetRow);
            boundsToHighlight = GetHighlightBounds(signatureCells.Cast<IHaveSimpleBounds>().ToList());
        }

        var imageResponse =
            await _imageCreationService.CreateImage(sigSheetId, pageNumber, boundsToCrop, boundsToHighlight,
                amountToStretch);
        if (imageResponse.ExecutionStatus == ExecutionStatus.Failed)
        {
            return MarkResponseNotFound(response, imageResponse.Message);
        }

        response.ExecutionStatus = ExecutionStatus.Succeeded;
        response.ImageBytes = imageResponse.ImageBytes;
        return response;
    }

    private static WorkDTO MarkResponseNotFound(WorkDTO response, string message)
    {
        response.ExecutionStatus = ExecutionStatus.Failed;
        response.Message = message;
        return response;
    }

    private static List<ViolationCriterion> GetFieldViolationCriteriaFromTaskRules(IList<Rule>? rules)
    {
        if (rules == null)
        {
            return new List<ViolationCriterion>();
        }

        return rules
            .Where(r => r.RuleContextType == RuleContextType.Transcription)
            .Select(x => new ViolationCriterion { Name = x.Name, RuleId = x.Id }).ToList();
    }

    private static List<ViolationCriterion> GetCellViolationCriteriaFromTask(Model.Workflow.Task? task,
        TemplateSignatureColumn column)
    {
        if (task == null || !task.Rules.Any() || column.IsSkipped || column.CanBeInvalid == false ||
            column.Name == null)
        {
            return new List<ViolationCriterion>();
        }

        var rulesToUse = task.Rules.Where(rule =>
            rule.Name.Contains(column.Name, StringComparison.InvariantCultureIgnoreCase));
        return rulesToUse
            .Where(r => r.RuleContextType == RuleContextType.Transcription)
            .Select(x => new ViolationCriterion { Name = x.Name, RuleId = x.Id }).ToList();
    }

    public WorkFieldDTO CreateWorkFieldDTO(int id, string? name, string? value,
        bool? canBeInvalid,
        Validity validity, bool isSignature,
        IList<ViolationCriterion>? violationCriteria,
        int? violationId, string? violationNote,
        InputType inputType = InputType.Text)
    {
        if (isSignature)
        {
            return new WorkFieldDTO
            {
                Id = id, Name = null, Value = null,
                CanBeInvalid = canBeInvalid == true,
                Validity = validity,
                ViolationCriteria = violationCriteria,
                ViolationId = violationId, ViolationNote = violationNote
            };
        }

        return new WorkFieldDTO
        {
            Id = id, Name = name ?? "", Value = value ?? "",
            CanBeInvalid = canBeInvalid == true,
            Validity = validity,
            ViolationCriteria = violationCriteria,
            ViolationId = violationId, ViolationNote = violationNote,
            InputType = inputType
        };
    }

    public async Task<(CellBounds cropBounds, CellBounds stretchAmount)> GetCropBoundsFromTaskAsync(
        Model.Workflow.Task task, SignatureSheetRow row)
    {
        var cells = row.Cells
            .Where(c => c.ColumnIndex >= task.ShowFirstColumnIndex && c.ColumnIndex <= task.ShowLastColumnIndex)
            .ToList();
        var stretchAmount = new CellBounds { Left = 0.1m, Right = 0.1m, Top = 0.2m, Bottom = 0.2m };

        if (task.ShowWholeColumn == true && task.ShowFirstColumnIndex is not null)
        {
            var columnCells =
                await _signatureSheetCellRepository.GetWholeColumnFromSheetAsync(row.SignatureSheetId,
                    task.ShowFirstColumnIndex.Value);
            var cropBounds = GeometryService.ComputeCellBoundsFromCells(columnCells.Cast<IHaveSimpleBounds>().ToList());
            stretchAmount = new CellBounds { Left = 0.1m, Right = 0.33m, Top = 0.05m, Bottom = 0.05m };
            return (cropBounds, stretchAmount);
        }

        var cellBounds = cells.Cast<IHaveSimpleBounds>().ToList();
        var cellsBoundary = GeometryService.ComputeCellBoundsFromCells(cellBounds);

        if (task.ShowSurroundingRows == true)
        {
            var cropBounds = GeometryService.ComputeSurroundingRowCellBounds(cellsBoundary);
            stretchAmount = new CellBounds { Left = 0.1m, Right = 0.1m, Top = 0.1m, Bottom = 0.1m };
            return (cropBounds, stretchAmount);
        }

        return (cellsBoundary, stretchAmount);
    }

    public CellBounds GetHighlightBounds(List<IHaveSimpleBounds> bounds)
    {
        var cellsBoundary = GeometryService.ComputeCellBoundsFromCells(bounds);
        return cellsBoundary;
    }

    private async Task UpdateRowReviewedAsync(SignatureSheetRow row, User user)
    {
        if (row.Cells.Any(x => x.Validity == Validity.Strikethrough))
        {
            row.Validity = Validity.Strikethrough;
        }

        if (row.Cells.All(x => x.IsReviewed) && row.RegisteredVoterFlags != RegisteredVoterFlags.Unknown)
        {
            row.IsReviewed = true;
            row.ReviewedOn = DateTime.UtcNow;
            row.ReviewedBy = user.Email;
            await _deficiencyService.RecomputeRowValidityAsync(row);
            await UpdateSignatureSheetReviewedAsync(row.SignatureSheet);
            if (row.Validity != Validity.Strikethrough)
            {
                await _signatoryService.UpdateSignatoryForRowAsync(row);
            }
        }
    }

    public async Task<ServiceResult<UnreleasedWork>> GetNextUnavailableWorkAsync(int matterId, int sheetNumber)
    {
        var allWorks = await _workRepository.GetAllForMatterAndSheetNumberAsync(
            matterId, sheetNumber);
        var unavailableWorks = allWorks.Where(w => w.WorkStatus == WorkStatus.Unavailable).ToList();

        var serviceResult = await GetFirstAndLastRowsWorkAsync(matterId, sheetNumber, allWorks);
        if (!serviceResult.IsSuccess || serviceResult.Value == null)
        {
            return ServiceResult<UnreleasedWork>.Failed(serviceResult.ErrorMessages.ToArray());
        }

        var firstAndLastRowWork = serviceResult.Value;
        // Is there only unavailable work in the first and last rows, then that is what we need to return
        if (firstAndLastRowWork.Works.All(w => w.WorkStatus == WorkStatus.Unavailable))
        {
            return ServiceResult<UnreleasedWork>.Succeeded(new UnreleasedWork
            {
                UnreleasedWorkType = UnreleasedWorkType.FirstLastRow,
                Works = firstAndLastRowWork.Works.OrderBy(w => w.RowNumber)
                    .ThenBy(w => w.Task.FirstColumnIndex).ToList()
            });
        }

        var firstRowNumber = firstAndLastRowWork.FirstRowNumber;
        var lastRowNumber = firstAndLastRowWork.LastRowNumber;
        var otherRowWork = allWorks
            .Where(w => w.RowNumber > firstRowNumber
                && w.RowNumber < lastRowNumber
                && w.Task.TaskType == TaskType.SignatureTableColumn)
            .ToList();
        if (otherRowWork.All(w => w.WorkStatus == WorkStatus.Unavailable))
        {
            return ServiceResult<UnreleasedWork>.Succeeded(new UnreleasedWork
            {
                UnreleasedWorkType = UnreleasedWorkType.OtherRows,
                Works = otherRowWork.OrderBy(w => w.RowNumber)
                    .ThenBy(w => w.Task.FirstColumnIndex).ToList()
            });
        }

        var releasedFieldWork = await _workRepository.GetAllForMatterAndSheetNumberAsync(
            matterId, sheetNumber, w => w.FieldNumber > 0 && w.WorkStatus == WorkStatus.None);

        if (!releasedFieldWork.Any())
        {
            var unavailableFieldWork = unavailableWorks.Where(w => w.FieldNumber > 0);
            return ServiceResult<UnreleasedWork>.Succeeded(new UnreleasedWork
            {
                UnreleasedWorkType = UnreleasedWorkType.Fields,
                Works = unavailableFieldWork.OrderBy(w => w.FieldNumber).ToList()
            });
        }

        return ServiceResult<UnreleasedWork>.Succeeded(new UnreleasedWork
        {
            UnreleasedWorkType = UnreleasedWorkType.None,
            Works = []
        });
    }

    public async Task<ServiceResult<FirstLastRowWork>> GetFirstAndLastRowsWorkAsync(int matterId, int sheetNumber,
        List<Work> unavailableWorks)
    {
        var signatureSheet = await _signatureSheetRepository.GetByMatterAndSheetNumberAsync(matterId, sheetNumber);
        if (signatureSheet == null)
        {
            return ServiceResult<FirstLastRowWork>.Failed(
                $"Signature sheet not found for matter {matterId} and sheet number {sheetNumber}");
        }

        var nonMissingRows = signatureSheet.Rows.Where(r => !r.IsMissing).ToList();
        if (!nonMissingRows.Any())
        {
            return ServiceResult<FirstLastRowWork>.Failed(
                $"No non-missing rows found for matter {matterId} and sheet number {sheetNumber}");
        }

        var firstRowNumber = nonMissingRows.Min(r => r.RowNumber);
        var lastRowNumber = nonMissingRows.Max(r => r.RowNumber);

        var unavailableFirstRowWork = unavailableWorks.Where(w => w.RowNumber == firstRowNumber
            && w.Task.TaskType == TaskType.SignatureTableColumn);
        var unavailableLastRowWork = unavailableWorks.Where(w => w.RowNumber == lastRowNumber
            && w.Task.TaskType == TaskType.SignatureTableColumn);
        var firstAndLastRowWork = unavailableFirstRowWork.Union(unavailableLastRowWork).ToList();
        return ServiceResult<FirstLastRowWork>.Succeeded(new FirstLastRowWork
        {
            FirstRowNumber = firstRowNumber,
            LastRowNumber = lastRowNumber,
            Works = firstAndLastRowWork
        });
    }
}