using DataInterface.RepositoryInterfaces;
using DataInterface.ServiceInterfaces;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Model.Rules;
using Model.SignatureSheets;
using Model.Templates;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Service.RuleOperations;
using Service.ServiceModels;
using System.Collections.Concurrent;
using System.Diagnostics;
using Model.Deficiencies;
using Model.Matters;
using Task = System.Threading.Tasks.Task;
using Template = Model.Templates.Template;

namespace Service
{
    public class RulesEngine
    {
        private readonly JsonSerializer _serializer = new() { ReferenceLoopHandling = ReferenceLoopHandling.Ignore };

        private readonly Dictionary<string, IRule> _rules;
        private readonly DeficiencyService _deficiencyService;
        private readonly IDeficiencyRepository _deficiencyRepository;
        private readonly IFileService _fileService;
        private readonly ILogger<RulesEngine> _logger;
        private readonly IMatterRepository _matterRepository;
        private readonly IRuleRepository _ruleRepository;
        private readonly IServiceProvider _serviceProvider;
        private readonly ISignatureSheetCellRepository _signatureSheetCellRepository;
        private readonly ISignatureSheetFieldRepository _signatureSheetFieldRepository;
        private readonly ISignatureSheetRepository _signatureSheetRepository;
        private readonly ISignatureSheetRowRepository _signatureSheetRowRepository;
        private readonly ISignatureSheetUploadRepository _signatureSheetUploadRepository;
        private readonly ITemplatePageRepository _templatePageRepository;
        private readonly ITemplateRepository _templateRepository;
        private readonly ITemplateSignatureColumnRepository _templateSignatureColumnRepository;
        private readonly PdfManipulationService _pdfManipulationService;
        private readonly RuleExpressionHelper _ruleExpressionHelper;

        private readonly ConcurrentDictionary<string, Func<RuleContext, object?>> _compiledGetters = new();
        private readonly ConcurrentDictionary<string, IRule?> _ruleOperationCache = new();

        public RulesEngine(
            DeficiencyService deficiencyService,
            IDeficiencyRepository deficiencyRepository,
            IFileService fileService,
            ILogger<RulesEngine> logger,
            IMatterRepository matterRepository,
            IRuleRepository ruleRepository,
            IServiceProvider serviceProvider,
            ISignatureSheetCellRepository signatureSheetCellRepository,
            ISignatureSheetFieldRepository signatureSheetFieldRepository,
            ISignatureSheetRepository signatureSheetRepository,
            ISignatureSheetRowRepository signatureSheetRowRepository,
            ISignatureSheetUploadRepository signatureSheetUploadRepository,
            ITemplatePageRepository templatePageRepository,
            ITemplateRepository templateRepository,
            ITemplateSignatureColumnRepository templateSignatureColumnRepository,
            PdfManipulationService pdfManipulationService,
            RuleExpressionHelper ruleExpressionHelper
        )
        {
            _deficiencyService = deficiencyService;
            _deficiencyRepository = deficiencyRepository;
            _fileService = fileService;
            _logger = logger;
            _matterRepository = matterRepository;
            _ruleRepository = ruleRepository;
            _serviceProvider = serviceProvider;
            _signatureSheetCellRepository = signatureSheetCellRepository;
            _signatureSheetFieldRepository = signatureSheetFieldRepository;
            _signatureSheetRepository = signatureSheetRepository;
            _signatureSheetRowRepository = signatureSheetRowRepository;
            _signatureSheetUploadRepository = signatureSheetUploadRepository;
            _templatePageRepository = templatePageRepository;
            _templateRepository = templateRepository;
            _templateSignatureColumnRepository = templateSignatureColumnRepository;
            _rules = GetRuleOperationMap();
            _pdfManipulationService = pdfManipulationService;
            _ruleExpressionHelper = ruleExpressionHelper;
        }

        public Dictionary<string, IRule> GetRuleOperationMap()
        {
            var ruleOperationType = typeof(IRule);
            var dictOfNamesToTypes = typeof(RulesEngine).Assembly.GetTypes()
                .Where(t => ruleOperationType.IsAssignableFrom(t) && t.IsClass)
                .ToDictionary(t => t.Name, t => (IRule)ActivatorUtilities.CreateInstance(_serviceProvider, t));
            return dictOfNamesToTypes;
        }

        public async Task<RulesResponse> EvaluateAllAsync(int matterId)
        {
            var swEvaluateAll = Stopwatch.StartNew();
            var response = new RulesResponse() { IsSuccess = true };

            var rules = await _ruleRepository.GetAllByMatterIdAsync(matterId);

            // load the matter
            var matter = await _matterRepository.GetMatterIncludingVariablesAsync(matterId);
            if (matter == null)
            {
                response.IsSuccess = false;
                response.ErrorMessage = $"Matter {matterId} Not found.";
                return response;
            }

            var deficienciesBefore = await _deficiencyRepository.GetAllByMatterAsync(matterId);
            var deficiencyResultsBefore =
                await _deficiencyService.TranslateToDtosAsync(deficienciesBefore, matterId, shouldIncludeImages: false);
            var deficienciesBeforeBySheet = deficiencyResultsBefore
                .Where(x => x.SheetNumber != null)
                .GroupBy(x => x.SheetNumber ?? 0)
                .ToDictionary(g => g.Key, g => g.ToList());

            await _deficiencyService.DeleteRulesEngineCreatedDeficienciesAsync(matterId);

            var rulesContext = new RuleContext { Matter = matter, };
            var swMatterRules = Stopwatch.StartNew();
            await RunMatterRulesAsync(rules, rulesContext);
            swMatterRules.Stop();
            _logger.LogInformation(
                "Rules engine evaluated matter rules in {ElapsedMilliseconds} ms",
                swMatterRules.ElapsedMilliseconds);

            await RunCirculatorRulesAsync(rules, rulesContext);

            // get all the SignatureSheetUploads for this matter
            var uploads = await _signatureSheetUploadRepository.GetAllByMatterIdAsync(matterId);
            var templateIds = uploads.Select(u => u.TemplateId).Distinct().ToList();

            // get all the Templates (with their columns) for those SignatureSheetUploads

            var templates = await _templateRepository.GetByIdsAsync(templateIds);
            var templatesById = templates.ToDictionary(t => t.Id, t => t);

            var sheets = (await _signatureSheetRepository.GetAllByMatterIdAsync(matterId))
                .Where(s => s.IsTableReviewed).ToList();

            var swEvaluateAllSheets = Stopwatch.StartNew();
            foreach (var sheet in sheets)
            {
                var swEvaluateSheet = Stopwatch.StartNew();
                await EvaluateSheetRulesAsync(matter, sheet, rules);
                swEvaluateSheet.Stop();
                _logger.LogInformation(
                    "Rules engine evaluated sheet {SheetNumber} rules in {ElapsedMilliseconds} ms",
                    sheet.SheetNumber, swEvaluateSheet.ElapsedMilliseconds);
            }

            swEvaluateAllSheets.Stop();
            _logger.LogInformation(
                "Rules engine evaluated all sheet rules in {ElapsedMilliseconds} ms",
                swEvaluateAllSheets.ElapsedMilliseconds);

            await _ruleRepository.SaveChangesAsync();
            var swDeficiencyUpdate = Stopwatch.StartNew();
            await _deficiencyService.UpdateOrDeleteAlreadyReviewedDeficienciesAsync(matterId);
            swDeficiencyUpdate.Stop();
            _logger.LogInformation(
                "Rules engine updated or deleted deficiencies in {ElapsedMilliseconds} ms",
                swDeficiencyUpdate.ElapsedMilliseconds);
            var swCreateRowDeficiencies = Stopwatch.StartNew();
            await _deficiencyService.CreateRowDeficienciesForSheetDeficiencies(matterId);
            swCreateRowDeficiencies.Stop();
            _logger.LogInformation(
                "Rules engine created row deficiencies in {ElapsedMilliseconds} ms",
                swCreateRowDeficiencies.ElapsedMilliseconds);

            var swGetAllDeficiencies = Stopwatch.StartNew();
            var deficienciesAfter = await _deficiencyRepository.GetAllByMatterIncludeSheetAsync(matterId);
            swGetAllDeficiencies.Stop();
            _logger.LogInformation(
                "Rules engine got all deficiencies after evaluation in {ElapsedMilliseconds} ms",
                swGetAllDeficiencies.ElapsedMilliseconds);
            var deficienciesAfterById = deficienciesAfter.ToDictionary(d => d.Id, d => d);
            var deficiencyResultsAfter =
                await _deficiencyService.TranslateToDtosAsync(deficienciesAfter, matterId, shouldIncludeImages: false);
            var deficienciesAfterBySheet = deficiencyResultsAfter
                .Where(x => x.SheetNumber != null)
                .GroupBy(x => x.SheetNumber ?? 0)
                .ToDictionary(g => g.Key, g => g.ToList());
            var swImageUpdation = Stopwatch.StartNew();
            await UpdateSheetDeficiencyImagesAsync(matterId, sheets, deficienciesBeforeBySheet,
                deficienciesAfterBySheet, templatesById, deficienciesAfterById);
            swImageUpdation.Stop();
            _logger.LogInformation(
                "Rules engine updated deficiency images in {ElapsedMilliseconds} ms",
                swImageUpdation.ElapsedMilliseconds);
            swEvaluateAll.Stop();
            _logger.LogInformation(
                "Rules engine evaluated all rules in {ElapsedMilliseconds} ms",
                swEvaluateAll.ElapsedMilliseconds);
            return response;
        }

        public async Task EvaluateSheetRulesAsync(Matter matter, SignatureSheet sheet, List<Rule> rules)
        {
            try
            {
                var rulesContext = new RuleContext
                {
                    Matter = matter,
                    Sheet = sheet,
                    Template = sheet.Template
                };

                foreach (var page in sheet.Pages)
                {
                    var swEvaluatePage = Stopwatch.StartNew();
                    rulesContext.SheetPage = page;
                    var templatePages = await _templatePageRepository.GetByTemplateIdAsync(sheet.TemplateId);
                    rulesContext.TemplatePage = templatePages.Single(tp => tp.PageNumber == page.PageNumber);
                    var templateColumns =
                        await _templateSignatureColumnRepository.GetAllByTemplateIdAsync(sheet.TemplateId);

                    if (page.PageNumber != 1)
                    {
                        continue;
                    }

                    var fields = await _signatureSheetFieldRepository.GetAllBySignatureSheetIdAsync(sheet.Id);
                    sheet.Fields = fields;

                    var swRunSheetRules = Stopwatch.StartNew();
                    await RunSheetRulesAsync(matter.Id, rules, rulesContext, sheet);
                    swRunSheetRules.Stop();
                    _logger.LogInformation(
                        "Rules engine evaluated page sheet {SheetNumber} rules in {ElapsedMilliseconds} ms",
                        sheet.SheetNumber, swRunSheetRules.ElapsedMilliseconds);

                    var columnRules = rules.Where(x => x.RuleContextType == RuleContextType.SignatureColumn).ToList();
                    var swRunCellRules = Stopwatch.StartNew();
                    foreach (var column in templateColumns)
                    {
                        try
                        {
                            var thisColumnRules = columnRules.Where(r => r.Task!.FirstColumnIndex == column.ColumnIndex)
                                .ToList();
                            if (thisColumnRules.Any())
                            {
                                await RunColumnCellRulesAsync(matter.Id, rulesContext, sheet, column,
                                    thisColumnRules);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning($"Exception during rule engine matter processing {ex}");
                        }
                    }

                    swRunCellRules.Stop();
                    _logger.LogInformation(
                        "Rules engine evaluated page column cell rules in {ElapsedMilliseconds} ms",
                        swRunCellRules.ElapsedMilliseconds);
                    rulesContext.Column = null;

                    var swRunRowRules = Stopwatch.StartNew();
                    await RunRowRulesAsync(matter.Id, rules, rulesContext, sheet, templateColumns);
                    swRunRowRules.Stop();
                    _logger.LogInformation(
                        "Rules engine evaluated page row rules in {ElapsedMilliseconds} ms",
                        swRunRowRules.ElapsedMilliseconds);
                    swEvaluatePage.Stop();
                    _logger.LogInformation(
                        "Rules engine evaluated page {PageNumber} rules in {ElapsedMilliseconds} ms",
                        page.PageNumber, swEvaluatePage.ElapsedMilliseconds);
                }

                rulesContext.SheetPage = null;
            }
            catch (Exception ex)
            {
                // TODO: we need app-insights log here
                Debug.WriteLine($"Exception during rule engine sheet processing {ex}");
            }
        }

        private async Task UpdateSheetDeficiencyImagesAsync(int matterId, List<SignatureSheet> sheets,
            Dictionary<int, List<DeficiencyResult>> deficienciesBySheetStart,
            Dictionary<int, List<DeficiencyResult>> deficienciesBySheetEnd, Dictionary<int, Template> templatesById,
            Dictionary<int, Deficiency> deficienciesById)
        {
            foreach (var sheet in sheets)
            {
                deficienciesBySheetStart.TryGetValue(sheet.SheetNumber, out var deficienciesForSheetAtStart);
                var deficienciesAtStart = deficienciesForSheetAtStart ?? [];
                deficienciesBySheetEnd.TryGetValue(sheet.SheetNumber, out var deficienciesForSheetAtEnd);
                var deficienciesAtEnd = deficienciesForSheetAtEnd ?? [];
                var swGetFileStream = Stopwatch.StartNew();
                var stream = await _fileService.GetFileStreamAsync($"matter{matterId}", sheet.FileName);
                if (stream == null)
                {
                    swGetFileStream.Stop();
                    continue;
                }

                swGetFileStream.Stop();
                _logger.LogInformation(
                    "Rules engine obtained file stream for sheet {SheetNumber} in {ElapsedMilliseconds} ms",
                    sheet.SheetNumber, swGetFileStream.ElapsedMilliseconds);
                var memoryStream = new MemoryStream();
                await stream.CopyToAsync(memoryStream);
                memoryStream.Position = 0;
                var template = templatesById[sheet.TemplateId];
                bool needSheetImage = sheet.DeficiencyImageFilePath == null
                                      && deficienciesAtEnd.Count > 0;
                bool deficienciesHaveChanged = !deficienciesAtStart.SequenceEqual(deficienciesAtEnd);
                if (template.PageSize != null && (needSheetImage || deficienciesHaveChanged))
                {
                    var swCreateImage = Stopwatch.StartNew();
                    await CreateSheetDeficiencyImageAsync(matterId, deficienciesAtEnd, sheet, deficienciesById,
                        memoryStream,
                        template.PageSize.Value);
                    swCreateImage.Stop();
                    _logger.LogInformation(
                        "Rules engine created deficiency image for sheet {SheetNumber} in {ElapsedMilliseconds} ms",
                        sheet.SheetNumber, swCreateImage.ElapsedMilliseconds);
                }
            }
        }

        private async Task RunMatterRulesAsync(List<Rule> rules, RuleContext rulesContext)
        {
            IEnumerable<Rule> matterRules = rules.Where(x => x.RuleContextType == RuleContextType.Matter);
            foreach (var rule in matterRules)
            {
                rulesContext.Rule = rule;
                try
                {
                    var serviceResult = await EvaluateRuleAsync(rulesContext, rule);
                    if (!serviceResult.IsSuccess)
                    {
                        _logger.LogWarning("Error evaluating matter rule {RuleName}: {ErrorMessages}",
                            rule.Name, string.Join(", ", serviceResult.ErrorMessages));
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning($"Exception during rule engine matter processing {ex}");
                }
                finally
                {
                    rulesContext.Rule = null;
                }
            }
        }

        private async Task RunCirculatorRulesAsync(List<Rule> rules, RuleContext rulesContext)
        {
            IEnumerable<Rule> circulatorRules = rules.Where(x => x.RuleContextType == RuleContextType.Circulator);
            foreach (var rule in circulatorRules)
            {
                rulesContext.Rule = rule;
                try
                {
                    var serviceResult = await EvaluateRuleAsync(rulesContext, rule);
                    if (!serviceResult.IsSuccess)
                    {
                        _logger.LogWarning("Error evaluating circulator rule {RuleName}: {ErrorMessages}",
                            rule.Name, string.Join(", ", serviceResult.ErrorMessages));
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning($"Exception during rule engine matter processing {ex}");
                }
                finally
                {
                    rulesContext.Rule = null;
                }
            }
        }


        private async Task RunSheetRulesAsync(int matterId, List<Rule> rules, RuleContext ruleContext,
            SignatureSheet sheet)
        {
            IEnumerable<Rule> sheetRules = rules.Where(x => x.RuleContextType == RuleContextType.SignatureSheet);
            foreach (var rule in sheetRules)
            {
                ruleContext.Rule = rule;
                try
                {
                    var serviceResult = await EvaluateRuleAsync(ruleContext, rule);
                    if (serviceResult.IsSuccess && serviceResult.Value)
                    {
                        string? notes = serviceResult.SuccessMessage;

                        if (rule.Task?.TranscribableFieldIds != null)
                        {
                            var fieldIds = rule.Task.DeserializedFieldIds;
                            foreach (var fieldId in fieldIds)
                            {
                                // TODO: this is not quite right, if we have a group, there should only be one deficiency for the entire group
                                var signatureSheetField =
                                    await _signatureSheetFieldRepository.GetBySheetAndTranscribableFieldId(sheet.Id,
                                        fieldId);
                                if (signatureSheetField != null)
                                {
                                    // var work = await _workRepository.GetBySheetIdAndFieldNumber(sheet.Id,
                                    //     signatureSheetField.TranscribableField.FieldNumber);
                                    var deficiency = await _deficiencyService.CreateRuleDeficiencyAsync(signatureSheetField.Id,
                                        RecordIdType.SignatureSheetField, rule.Id, matterId, sheet.Id,
                                        note: notes);
                                    await _deficiencyService.AddRuleDeficiencyAsync(signatureSheetField,
                                        RecordIdType.SignatureSheetField, deficiency);
                                }
                            }
                        }
                        else
                        {
                            // int? workId = null;
                            // if (RegexSheetFields.IsMatch(rule.LeftHandExpression))
                            // {
                            //     var work = await GetFieldWorkFromRuleExpressionAsync(ruleContext, sheet.Id,
                            //         rule.LeftHandExpression);
                            //     workId = work?.Id;
                            // }

                            var deficiency = await _deficiencyService.CreateRuleDeficiencyAsync(sheet.Id,
                                RecordIdType.SignatureSheet,
                                rule.Id, matterId, sheet.Id, notes, needsReview: rule.NeedsReview);
                            await _deficiencyService.AddRuleDeficiencyAsync(sheet, RecordIdType.SignatureSheet,
                                deficiency);
                        }
                    }
                }
                catch (Exception ex)
                {
                    // TODO: we need app-insights log here
                    Debug.WriteLine($"Exception during rule engine sheet processing {ex}");
                }
                finally
                {
                    ruleContext.Rule = null;
                }
            }
        }

        private async Task RunColumnCellRulesAsync(int matterId, RuleContext rulesContext, SignatureSheet sheet,
            TemplateSignatureColumn column, IEnumerable<Rule> thisColumnRules)
        {
            var cells = await _signatureSheetCellRepository.GetWholeColumnFromSheetAsync(sheet.Id, column.ColumnIndex);
            rulesContext.Column = cells;

            foreach (var rule in thisColumnRules)
            {
                rulesContext.Rule = rule;
                foreach (var cell in cells)
                {
                    try
                    {
                        rulesContext.Cell = cell;
                        var serviceResult = await EvaluateRuleAsync(rulesContext, rule);
                        if (serviceResult.IsSuccess && serviceResult.Value)
                        {
                            string? notes = serviceResult.SuccessMessage;
                            var deficiency = await _deficiencyService.CreateRuleDeficiencyAsync(cell.Id,
                                RecordIdType.SignatureSheetCell,
                                rule.Id, matterId, sheet.Id, note: notes);
                            await _deficiencyService.AddRuleDeficiencyAsync(cell, RecordIdType.SignatureSheetCell,
                                deficiency);
                            cell.SignatureSheetRow.Validity = Validity.Invalid;
                            _signatureSheetCellRepository.SetModified(cell);
                            await _signatureSheetCellRepository.SaveChangesAsync();
                        }
                    }
                    catch (Exception)
                    {
                        // Continue processing other cells
                    }
                }

                rulesContext.Rule = null;
            }
        }

        private async Task RunRowRulesAsync(int matterId, List<Rule> rules, RuleContext rulesContext,
            SignatureSheet sheet, List<TemplateSignatureColumn> templateColumns)
        {
            var rowRulesList = rules.Where(x => x.RuleContextType == RuleContextType.SignatureRow).ToList();
            if (rowRulesList.Any())
            {
                // load the rows with their cells
                var rows = (await _signatureSheetRowRepository.GetRowsAndCellsBySignatureSheetIdAsync(sheet.Id))
                    .Where(sr => sr.IsReviewed && sr.Validity != Validity.Strikethrough);
                foreach (var row in rows)
                {
                    try
                    {
                        rulesContext.Row = row;
                        foreach (var rule in rowRulesList)
                        {
                            rulesContext.Rule = rule;
                            var serviceResult = await EvaluateRuleAsync(rulesContext, rule);
                            if (serviceResult.IsSuccess && serviceResult.Value)
                            {
                                string? notes = serviceResult.SuccessMessage;
                                var deficiency = await _deficiencyService.CreateRuleDeficiencyAsync(row.Id,
                                    RecordIdType.SignatureSheetRow, rule.Id, matterId, sheet.Id, note: notes);
                                // TODO batch this
                                await _deficiencyService.AddRuleDeficiencyAsync(row, RecordIdType.SignatureSheetRow,
                                    deficiency);
                                row.Validity = Validity.Invalid;
                                _signatureSheetRowRepository.SetModified(row);

                            }

                            rulesContext.Rule = null;
                        }

                        await _signatureSheetRowRepository.SaveChangesAsync();
                    }
                    catch (Exception)
                    {
                        // Continue processing other rows
                    }

                    await RunRowCellRulesAsync(matterId, rules, rulesContext, row, templateColumns);

                    await _deficiencyService.RecomputeRowValidityAsync(row);
                    rulesContext.Cell = null;
                }

                rulesContext.Row = null;
            }
        }

        private async Task RunRowCellRulesAsync(int matterId, List<Rule> rules, RuleContext rulesContext,
            SignatureSheetRow row, List<TemplateSignatureColumn> templateColumns)
        {
            foreach (var cell in row.Cells)
            {
                if (cell is { TemplateSignatureColumnId: > 0, TemplateSignatureColumn: null })
                {
                    cell.TemplateSignatureColumn = templateColumns.First(tc => tc.ColumnIndex == cell.ColumnIndex);
                }

                try
                {
                    rulesContext.Cell = cell;

                    foreach (var rule in rules.Where(x => x.RuleContextType == RuleContextType.SignatureCell))
                    {
                        rulesContext.Rule = rule;
                        var serviceResult = await EvaluateRuleAsync(rulesContext, rule);
                        if (serviceResult.IsSuccess && serviceResult.Value) // is deficient
                        {
                            string? notes = serviceResult.ErrorMessages.Any()
                                ? string.Join("\n", serviceResult.ErrorMessages)
                                : null;
                            var deficiency = await _deficiencyService.CreateRuleDeficiencyAsync(cell.Id,
                                RecordIdType.SignatureSheetCell, rule.Id, matterId, rulesContext.Sheet.Id, note: notes);
                            await _deficiencyService.AddRuleDeficiencyAsync(cell, RecordIdType.SignatureSheetCell,
                                deficiency);
                            row.Validity = Validity.Invalid;
                            await _signatureSheetRowRepository.SaveChangesAsync();
                        }

                        rulesContext.Rule = null;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error evaluating rule for cell {CellId}", cell.Id);
                }
            }
        }

        public async Task<ServiceResult<bool>> EvaluateRuleAsync(RuleContext context, Rule rule)
        {
            var swEvaluateRule = Stopwatch.StartNew();
            if (string.IsNullOrEmpty(rule.OperationName))
            {
                // A rule without an operation, does not need to be evaluated by the rules engine
                return ServiceResult<bool>.Succeeded(false);
            }

            // Get cached rule operation
            var ruleOperation = _ruleOperationCache.GetOrAdd(rule.OperationName,
                opName => _rules.GetValueOrDefault(opName));

            if (ruleOperation == null)
            {
                _logger.LogWarning("Rule operation {OperationName} not found", rule.OperationName);
                return ServiceResult<bool>.Succeeded(false);
            }

            // Use compiled getters instead of JSON tokens
            var lhsValue = GetValueFromExpression(context, rule.LeftHandExpression);
            if (lhsValue == null)
            {
                _logger.LogWarning("RULE LEFT HAND SIDE NULL: {Expression}", rule.LeftHandExpression);
            }

            object? rhsValue = null;
            if (!string.IsNullOrEmpty(rule.RightHandExpression))
            {
                rhsValue = GetValueFromExpression(context, rule.RightHandExpression);
            }

            // Convert to JToken for compatibility with existing rule operations
            var lhsToken = ConvertToJToken(lhsValue);
            var rhsToken = ConvertToJToken(rhsValue);
            swEvaluateRule.Stop();
            _logger.LogInformation(
                "Rules engine evaluated row {row} rule {RuleName} in {ElapsedMilliseconds} ms",
                context.Row?.Id ?? 0,
                rule.Name, swEvaluateRule.ElapsedMilliseconds);
            // Execute rule operation
            return ruleOperation switch
            {
                IRuleOperationAsync asyncOp => await asyncOp.EvaluateAsync(context, lhsToken, rhsToken),
                IRuleOperation syncOp => syncOp.Evaluate(context, lhsToken, rhsToken),
                _ => ServiceResult<bool>.Succeeded(false)
            };

        }

        private object? GetValueFromExpression(RuleContext context, string expression)
        {
            var getter = _compiledGetters.GetOrAdd(expression, CompileGetter);
            return getter(context);
        }

        private Func<RuleContext, object?> CompileGetter(string expression)
        {
            // Parse expression and create optimized getter
            if (expression.Contains(".Fields["))
            {
                var fieldName = _ruleExpressionHelper.ExtractFieldName(expression);
                var hasValue = expression.EndsWith(".Value");

                return context =>
                {
                    var field = context.Sheet.Fields.FirstOrDefault(f => f.TranscribableField.Name == fieldName);
                    return hasValue ? field?.Value : field;
                };
            }

            if (expression.Contains(".Cells["))
            {
                var columnName = _ruleExpressionHelper.ExtractColumnName(expression);
                var hasValue = expression.EndsWith(".Value");

                return context =>
                {
                    var cell = context.Row?.Cells.FirstOrDefault(c => c.TemplateSignatureColumn.Name == columnName);
                    return hasValue ? cell?.Value : cell;
                };
            }

            if (expression.Contains(".Variables["))
            {
                var variableName = _ruleExpressionHelper.ExtractVariableName(expression);

                return context =>
                {
                    var variable = context.Matter.Variables.FirstOrDefault(v => v.Key == variableName);
                    return variable?.Value;
                };
            }

            // Handle simple property access
            return CompilePropertyAccess(expression);
        }


        private Func<RuleContext, object?> CompilePropertyAccess(string expression)
        {
            // Simple property chain like "Sheet.SheetNumber" or "Matter.Name"
            var parts = expression.Split('.');

            return context =>
            {
                object? current = context;
                foreach (var part in parts)
                {
                    if (current == null) return null;

                    var prop = current.GetType().GetProperty(part);
                    current = prop?.GetValue(current);
                }

                return current;
            };
        }

        private async Task CreateSheetDeficiencyImageAsync(
            int matterId, List<DeficiencyResult> deficiencyDtos, SignatureSheet sheet,
            Dictionary<int, Deficiency> deficienciesById,
            MemoryStream stream, SupportedPageSize pageSize
        )
        {
            for (var sideNumber = 1; sideNumber <= 2; sideNumber++)
            {
                var localSideNumber = sideNumber;
                var deficienciesPerSheet = deficiencyDtos
                    .Where(x => x.SignatureSheet != null && x.SignatureSheet.Id == sheet.Id &&
                                x.PageNumber == localSideNumber)
                    .OrderBy(x => (x.FieldIndex ?? 0) + (x.RowNumber * 100) + (x.ColumnIndex ?? 9)).ToList();
                if (deficienciesPerSheet.Count == 0)
                {
                    // if there are no backside deficiencies, then skip it
                    continue;
                }

                var deficiencyInfos = new List<DeficiencyInfo>();

                var badgeNumber = 0;
                var swBuildingDeficiencyInfos = Stopwatch.StartNew();
                foreach (var sheetDeficiency in deficienciesPerSheet)
                {
                    badgeNumber++;
                    var deficiency = deficienciesById[sheetDeficiency.Id];
                    var bounds = _deficiencyService.GetBoundsFromDeficiency(deficiency);
                    string badgePrefix = _deficiencyService.GetBadgePrefix(sheetDeficiency);

                    var deficiencyInfo = new DeficiencyInfo
                    {
                        WorkId = deficiency.WorkId,
                        BadgeDescription = $"{badgePrefix} - {sheetDeficiency.Rule.Name}",
                        BadgeNumber = badgeNumber.ToString(),
                        PageNumber = sheetDeficiency.PageNumber,
                        Bounds = bounds,
                        RecordIdType = sheetDeficiency.RecordIdType,
                        RecordId = sheetDeficiency.RecordId,
                    };

                    deficiencyInfos.Add(deficiencyInfo);
                }

                swBuildingDeficiencyInfos.Stop();
                _logger.LogInformation(
                    "Rules engine built deficiencyInfos for sheet {SheetNumber} in {ElapsedMilliseconds} ms",
                    sheet.SheetNumber, swBuildingDeficiencyInfos.ElapsedMilliseconds);

                var widthInPixels = pageSize == SupportedPageSize.Letter ? 11 * 72 : 14 * 72;
                var heightInPixels = (int)(8.5 * 72);
                byte[]? image =
                    _pdfManipulationService.HighlightDeficiencies(stream, deficiencyInfos, sideNumber, true, 72,
                        widthInPixels, heightInPixels);

                var filePath = $"deficiencies/sheet{sheet.SheetNumber}.png";
                if (image != null)
                {
                    await _fileService.DeleteFileStreamAsync($"matter{matterId}", filePath);
                    // genarate a PNG file for each deficiency sheet
                    using (MemoryStream memoryStream = new MemoryStream(image))
                    {
                        CancellationToken ct = new CancellationToken();
                        await _fileService.SaveFileStreamAsync($"matter{matterId}", filePath, memoryStream, true,
                            ct);
                        sheet.DeficiencyImageFilePath = filePath;
                    }
                }
            }

            await _signatureSheetRepository.SaveChangesAsync();
        }

        private JToken ConvertToJToken(object? value)
        {
            if (value == null) return JValue.CreateNull();
            if (value is JToken token) return token;
            return JToken.FromObject(value, _serializer);
        }
    }
}