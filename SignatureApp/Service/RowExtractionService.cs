using DataInterface.ServiceInterfaces;
using Model.Geocoding;
using Model.SignatureSheets;
using Model.Templates;

namespace Service;

public static class RowExtractionService
{
    public static ServiceResult<AddressInput> GetAddressInputFromRow(IList<TemplateSignatureColumn> addressColumns,
        SignatureSheetRow row)
    {
        var addressInput = new AddressInput();
        var addressColumnIndices = addressColumns.Select(col => col.ColumnIndex);

        var addressCells = row.Cells.Where(cell => addressColumnIndices.Contains(cell.ColumnIndex))
            .ToList();
        if (addressCells.Count == 1)
        {
            var fullAddressCell = addressCells.First();
            if (string.IsNullOrWhiteSpace(fullAddressCell.Value))
            {
                return ServiceResult<AddressInput>.Failed("Full address cell value is null or whitespace");
            }

            addressInput = AddressParsingService.ParseFullAddressToInput(fullAddressCell.Value);
            if (addressInput == null)
            {
                return ServiceResult<AddressInput>.Failed("Parsed address input is null");
            }

            return ServiceResult<AddressInput>.Succeeded(addressInput);
        }

        var addressLineColumn = addressColumns.FirstOrDefault(x =>
            x.Name != null && x.Name.Contains("address", StringComparison.CurrentCultureIgnoreCase));
        if (addressLineColumn == null)
        {
            return ServiceResult<AddressInput>.Failed("Address line column is null");
        }

        var cityColumn = addressColumns.FirstOrDefault(x =>
            x.Name != null && x.Name.Contains("city", StringComparison.CurrentCultureIgnoreCase));
        if (cityColumn == null)
        {
            return ServiceResult<AddressInput>.Failed("Address line column is null");
        }

        var zipCodeColumn = addressColumns.FirstOrDefault(x =>
            x.Name != null && (x.Name.Contains("zip", StringComparison.CurrentCultureIgnoreCase)
                               || x.Name.Contains("postal", StringComparison.CurrentCultureIgnoreCase)));
        if (zipCodeColumn == null)
        {
            return ServiceResult<AddressInput>.Failed("Address line column is null");
        }

        var addressLineCell = addressCells.FirstOrDefault(x => x.ColumnIndex == addressLineColumn.ColumnIndex);
        var cityCell = addressCells.FirstOrDefault(x => x.ColumnIndex == cityColumn.ColumnIndex);
        var zipCodeCell = addressCells.FirstOrDefault(x => x.ColumnIndex == zipCodeColumn.ColumnIndex);

        if (string.IsNullOrWhiteSpace(addressLineCell?.Value))
        {
            return ServiceResult<AddressInput>.Failed("Address line cell value is null or whitespace");
        }

        if (string.IsNullOrWhiteSpace(cityCell?.Value))
        {
            return ServiceResult<AddressInput>.Failed("City cell value is null or whitespace");
        }

        if (string.IsNullOrWhiteSpace(zipCodeCell?.Value))
        {
            return ServiceResult<AddressInput>.Failed("Zip code cell value is null or whitespace");
        }

        addressInput.AddressLine = addressLineCell.Value.Trim();
        addressInput.City = cityCell.Value.Trim();
        addressInput.PostalCode = zipCodeCell.Value.Trim();
        return ServiceResult<AddressInput>.Succeeded(addressInput);
    }
}