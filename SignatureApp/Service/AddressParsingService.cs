using Model.Geocoding;
using System.Text.RegularExpressions;
using DataInterface.RepositoryInterfaces;
using Model.Boundaries;

namespace Service;

public static partial class AddressParsingService
{
    private static readonly Regex RegexStreetNumbers = StreetNumbersRegex();
    [GeneratedRegex(@"\d+\.?\d*M?")]
    private static partial Regex StreetNumbersRegex();

    private static readonly Regex RegexZipCode = new ("^\\d{5}(?:[-\\s]\\d{4})?$");
    private static Regex? _regexCities;

    public static string[]? ArizonaCities;

    public static Dictionary<string, string> ArizonaCityAbbreviations = new()
    {
        ["PHX"] = "Phoenix",
        ["TUS"] = "Tucson",
        ["FLG"] = "Flagstaff",
    };

    public static async Task InitializeAsync(IBoundaryRepository boundaryRepository)
    {
        if (ArizonaCities == null)
        {
            ArizonaCities = (await boundaryRepository.GetAllByStateAndTypeAsync(3, BoundaryType.CityTownPoint)).Select(c => c.Name).ToArray();
        }

        var citiesIncludingAbbreviations = ArizonaCities.Union(ArizonaCityAbbreviations.Keys).ToArray();
        var cities = Array.ConvertAll<string, string>(citiesIncludingAbbreviations, input => Regex.Escape(input));
        string pattern = @"\b(" + string.Join("|", cities) + @")\b";
        _regexCities = new Regex(pattern, RegexOptions.IgnoreCase | RegexOptions.Compiled);
    }

    public static ParsedAddress? ParseAddress(string addressLine1, string addressLine2)
    {
        var input = ParseAddressToInput(addressLine1, addressLine2);
        if (input == null) { return null; }

        return ParseInputToAddress(input);
    }

    public static ParsedAddress? ParseInputToAddress(AddressInput input)
    {
        if (input.AddressLine == null) { return null; }
        var parsedAddress = ParseAddressLine1(input.AddressLine);

        var state = UsStates.ParseState(input.State);
        if (state == null)
        {
            return null;
        }

        parsedAddress.City = input.City;
        parsedAddress.PostalCode = input.PostalCode;
        parsedAddress.State = state;
        return parsedAddress;
    }

    public static AddressInput? ParseFullAddressToInput(string fullAddress)
    {
        var addressInput = new AddressInput();
        string? city = FindCity(fullAddress);
        if (city != null)
        {
            var parts = fullAddress.Split(city);
            var addressLine1 = parts[0].Trim();
            if (ArizonaCityAbbreviations.TryGetValue(city, out var realCity))
            {
                city = realCity;
            }
            var addressLine2 = (city + parts[1]).Trim();
            return ParseAddressToInput(addressLine1, addressLine2);
        }

        return addressInput;
    }

    private static string? FindCity(string fullAddress)
    {
        ArgumentNullException.ThrowIfNull(_regexCities);

        var matches = _regexCities.Matches(fullAddress);
        if (matches.Count == 0)
            return null;

        return matches[^1].Value;
    }

    private static AddressInput? ParseAddressToInput(string addressLine1, string addressLine2)
    {
        var addressInput = ParseAddressLine2(addressLine2);
        addressInput.AddressLine = addressLine1;
        return addressInput;
    }

    private static bool CheckIsState(string potentialState)
    {
        return UsStates.Names().Contains(potentialState) || UsStates.Abbreviations().Contains(potentialState.ToUpper());
    }

    private static bool CheckIsPostalCode(string potentialZip)
    {
        var result = RegexZipCode.Match(potentialZip);
        return result.Success;
    }

    public static string CreateAddressLine1(IStreetAddress addr)
    {
        var newLine1 = $"{addr.StreetNumber} {addr.Direction} {addr.StreetName} {addr.StreetType}";
        return newLine1;
    }

    public static (string newLine1, string newLine2) CreateAddressLines(AddressInput newParsedAddress)
    {
        var addr = newParsedAddress;
        var newLine1 = $"{addr.AddressLine}";
        var newLine2 = $"{addr.City}, {addr.State} {addr.PostalCode}";
        return (newLine1, newLine2);
    }

    public static ParsedAddress ParseAddressLine1(string streetAddress)
    {
        var parsedAddress = new ParsedAddress();
        if (string.IsNullOrEmpty(streetAddress)) { return parsedAddress; }
        var separators = new [] { ' ', ',', '.' };
        var addressParts = new List<string>(streetAddress.Split(separators, options: StringSplitOptions.RemoveEmptyEntries));
        if (addressParts.Count == 0) { return parsedAddress; }

        var firstPart = addressParts.FirstOrDefault();
        if (firstPart != null && RegexStreetNumbers.Match(firstPart).Success)
        {
            parsedAddress.StreetNumber = firstPart;
            addressParts.RemoveAt(0);
        }

        firstPart = addressParts.FirstOrDefault();
        if (firstPart != null && Directions.Contains(firstPart.ToUpper()))
        {
            parsedAddress.Direction = firstPart;
            addressParts.RemoveAt(0);
        }

        // if we can identify the streetType, we should ignore things after that (unit numbers and so on)
        var currentCount = addressParts.Count;
        for (int index = currentCount - 1; index >= 0; index--)
        {
            string part = addressParts[index].ToUpper();
            if (StreetTypes.Contains(part))
            {
                parsedAddress.StreetType = part;
                addressParts.RemoveRange(index, currentCount - index);
                break;
            }
        }

        parsedAddress.StreetName = string.Join(" ", addressParts);
        return parsedAddress;
    }

    private static readonly List<string> Directions = ["N", "NE", "E", "SE", "S", "SW", "W", "NW",
        "NORTH", "EAST", "SOUTH", "WEST"];

    private static readonly List<string> StreetTypes =
    [
        "ALY",
        "AVE",
        "AVENUE",
        "BLVD",
        "BL",
        "BLF",
        "BND",
        "CI",
        "CIR",
        "CIRCLE",
        "CLL",
        "CALLE",
        "COR",
        "COURT",
        "CT",
        "CV",
        "CYN",
        "DR",
        "DRIVE",
        "ESTS",
        "FLT",
        "FRST",
        "GRV",
        "HL",
        "HOLW",
        "HTS",
        "HWY",
        "HIGHWAY",
        "ISLE",
        "LANE",
        "LN",
        "LOOP",
        "LP",
        "MTN",
        "PASS",
        "PATH",
        "PARK",
        "PKWY",
        "PL",
        "PLACE",
        "PLZ",
        "PT",
        "PW",
        "RD",
        "ROAD",
        "RDG",
        "ROW",
        "RUN",
        "SHL",
        "SPGS",
        "ST",
        "STRA",
        "STREET",
        "STRM",
        "SV",
        "SQ",
        "TE",
        "TER",
        "TR",
        "TRL",
        "WALK",
        "WAY",
        "WY",
        "VLG",
        "VIZ",
        "VW",
        "VIS",
        "XING"
    ];


    public static bool ArePostalCodesTheSame(string origAddrPostalCode, string newAddrPostalCode)
    {
        if (origAddrPostalCode.Length > 5)
        {
            origAddrPostalCode = origAddrPostalCode.Substring(0, 5);
        }
        if (newAddrPostalCode.Length > 5)
        {
            newAddrPostalCode = newAddrPostalCode.Substring(0, 5);
        }
        return origAddrPostalCode == newAddrPostalCode;
    }

    public static AddressInput ParseAddressLine2(string addressLine2)
    {
        var addressInput = new AddressInput();
        var parts = addressLine2.Split(new[] { ' ', ',' }, StringSplitOptions.RemoveEmptyEntries).ToList();
        if (parts.Count < 2)
        {
            return addressInput;
        }

        addressInput.Country = "US";
        if (CheckIsPostalCode(parts[^1]))
        {
            addressInput.PostalCode = parts[^1];
            parts.Remove(parts[^1]);
        }

        if (CheckIsState(parts[^1]))
        {
            addressInput.State = parts[^1];
            parts.Remove(parts[^1]);
        }
        else
        {
            addressInput.State = "AZ";
        }

        addressInput.City = string.Join(" ", parts);
        return addressInput;
    }

    public static readonly string[] ArizonaCounties =
    [
        "Apache", "Cochise", "Coconino", "Gila", "Graham", "Greenlee", "La Paz", "Maricopa", "Mohave", "Navajo",
        "Pima", "Pinal", "Santa Cruz", "Yavapai", "Yuma"
    ];
}
