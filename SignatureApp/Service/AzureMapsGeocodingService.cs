using Azure;
using Azure.Maps.Search;
using Azure.Maps.Search.Models;
using DataInterface.ServiceInterfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Model.Geocoding;

namespace Service;

public class AzureMapsGeocodingService : IGeocodingService
{
    private const string Key = "Keys:AzureMapsApiKey";

    private readonly ILogger<AzureMapsGeocodingService> _logger;
    private readonly string? _azureMapsApiKey;
    private MapsSearchClient _mapSearchClient;


    public AzureMapsGeocodingService(
        HttpClient httpClient,
        IConfiguration configuration,
        ILogger<AzureMapsGeocodingService> logger)
    {
        _logger = logger;
        _azureMapsApiKey = configuration.GetValue<string>(Key) ?? "AZURE_MAPS_API_KEY";
        var credential = new AzureKeyCredential(_azureMapsApiKey);
        _mapSearchClient = new MapsSearchClient(credential);
    }

    // https://learn.microsoft.com/en-us/azure/azure-maps/how-to-dev-guide-csharp-sdk
    public async Task<ServiceResult<GeocodeResults?>> GeocodeAsync(AddressInput? addressInput)
    {
        if (addressInput == null)
        {
            return ServiceResult<GeocodeResults?>.Succeeded(null, "Address is null");
        }
        var query = new GeocodingQuery
        {
            AddressLine = addressInput.AddressLine,
            CountryRegion = addressInput.Country,
            Locality = addressInput.City ?? string.Empty,
            AdminDistrict = addressInput.State,
            Top = 3
        };
        var sdkResponse = await _mapSearchClient.GetGeocodingAsync(options: query);
        var matchingFeature = sdkResponse.Value.Features.FirstOrDefault(f => f.Properties.Confidence == ConfidenceEnum.High);
        var results = new GeocodeResults();
        if (matchingFeature == null)
        {
            var errorStrings = new List<string> { "Azure Maps Geocoding did not return any high confidence results." };
            if (sdkResponse.Value.Features.Any())
            {
                var mediumFeatures = sdkResponse.Value.Features
                    .Where(f => f.Properties.Confidence == ConfidenceEnum.Medium);
                foreach (var feature in mediumFeatures)
                {
                    var result = GetGeocodeResult(feature);
                    if (result != null)
                    {
                        results.PossibleMatches.Add(result);
                    }

                    errorStrings.Add($"{feature.Properties.Address.FormattedAddress} ({feature.Geometry.Coordinates.Latitude:N6},{feature.Geometry.Coordinates.Longitude:N6})");
                }
            }
            _logger.LogWarning(errorStrings[0]);

            var serviceResult = new ServiceResult<GeocodeResults?>
            {
                Status = ResultStatus.Failure,
                ErrorMessages = errorStrings.ToList(),
                Value = results,
            };
            return serviceResult;
        }

        results.ExactMatch = GetGeocodeResult(matchingFeature);
        return ServiceResult<GeocodeResults?>.Succeeded(results);
    }

    private GeocodeResult? GetGeocodeResult(FeaturesItem featuresItem)
    {
        var input = AddressParsingService.ParseFullAddressToInput(featuresItem.Properties.Address.FormattedAddress);
        if (input == null)
        {
            return null;
        }
        var parseAddress = AddressParsingService.ParseInputToAddress(input);
        var geocodeResult = new GeocodeResult
        {
            NewParsedAddress = parseAddress,
            Latitude = (decimal)featuresItem.Geometry.Coordinates.Latitude,
            Longitude = (decimal)featuresItem.Geometry.Coordinates.Longitude
        };
        return geocodeResult;
    }
}