using DataInterface.ServiceInterfaces;
using Microsoft.Extensions.DependencyInjection;
using Service.RuleOperations;

namespace Service;

public class ServiceRegistrationService
{
    public static void RegisterServices(IServiceCollection services)
    {
        services.AddHttpClient<AzureMapsGeocodingService>();
        services.AddHttpClient<BingMapsGeocodingService>();
        // services.AddHttpClient<GoogleMapsGeocodingService>();

        services.AddHttpClient<GoogleMapsPlacesService>();
        services.AddHttpClient<IGeocodingService, AzureMapsGeocodingService>();
        // services.AddHttpClient<IGeocodingService, BingMapsGeocodingService>();
        services.AddHttpClient<IMapPlacesService, GoogleMapsPlacesService>();

        services.AddScoped<AreAddressLinesInconsistent>();
        services.AddScoped<AreDuplicateVoters>();
        services.AddScoped<IsAddressLineOutOfBoundsOf>();
        services.AddScoped<IsCityOutOfBoundsOf>();
        services.AddScoped<IsFieldInvalid>();
        services.AddScoped<IsNonResidentialCirculatorAddress>();
        services.AddScoped<IsNonResidentialSignatoryAddress>();
        services.AddScoped<IsOutOfStateCirculatorIdNotRegistered>();
        services.AddScoped<IsOutOfStateCirculatorNameNotRegistered>();
        services.AddScoped<IsPaidCirculatorIdNotRegistered>();
        services.AddScoped<IsPaidCirculatorNameNotRegistered>();
        services.AddScoped<IsZipCodeOutOfBoundsOf>();

        services.AddScoped<BoundaryService>();
        services.AddScoped<CandidateRuleGenerationService>();
        services.AddScoped<CirculatorService>();
        services.AddScoped<CsvParser>();
        services.AddScoped<DeficiencyService>();
        services.AddScoped<ExternalDataSourceService>();
        services.AddScoped<ImageCreationService>();
        services.AddScoped<InitiativeRuleGenerationService>();
        services.AddScoped<InvalidSheetService>();
        services.AddScoped<PdfManipulationService>();
        services.AddScoped<PrefixHandlerService>();
        services.AddScoped<QueueService>();
        services.AddScoped<RegisteredVoterService>();
        services.AddScoped<RuleGenerationServiceFactory>();
        services.AddScoped<RuleExpressionHelper>();
        services.AddScoped<RulesEngine>();
        services.AddScoped<SheetNumberService>();
        services.AddScoped<SimpleFormRecognizerService>();
        services.AddScoped<SignatoryService>();
        services.AddScoped<SignatureSheetGeometryService>();
        services.AddScoped<SignatureSheetHierarchyService>();
        services.AddScoped<SignatureSheetUploadService>();
        services.AddScoped<TaskGenerationService>();
        services.AddScoped<TemplateGeometryService>();
        services.AddScoped<TemplateHierarchyService>();
        services.AddScoped<TrainingModelHierarchyService>();
        services.AddScoped<UrlService>();
        services.AddScoped<VoterRegistrationSearchService>();
        services.AddScoped<WordService>();
        services.AddScoped<WorkGenerationService>();
        services.AddScoped<WorkService>();
    }
}