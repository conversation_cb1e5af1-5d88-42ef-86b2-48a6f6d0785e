using Microsoft.Extensions.Logging;
using System.Text.RegularExpressions;

namespace Service;

public class SheetNumberService
{
    private static Regex regexSingleSheetNumber = new Regex(@"\d{5,}");
    private static Regex regexSheetNumberRange = new Regex(@"\d{5,}\s*\-\s*\d{5,}");
    private static Regex regexSheetNum = new Regex(@"(?<range>(?<start>\d+)\-(?<end>\d+))?.*\.(?<sheet>\d+)");

    private readonly ILogger<SheetNumberService> _logger;

    public SheetNumberService(ILogger<SheetNumberService> logger)
    {
        _logger = logger;
    }

    public int? GetSheetNumberFromFilename(string restOfPath)
    {
        var match = regexSheetNum.Match(restOfPath);
        if (match.Success)
        {
            var sheet = int.Parse(match.Groups["sheet"].Value);
            if (match.Groups["range"].Success)
            {
                string startString = match.Groups["start"].Value;
                var start = int.Parse(startString);
                string endString = match.Groups["end"].Value;
                if (startString.Length == endString.Length)
                {
                    return start + sheet;
                }
            }

            return sheet;
        }

        return null;
    }

    public (int, int) GetPageNumberRangeFromFilename(string filename)
    {
        int sheetNumber = 0;
        var matches = regexSheetNumberRange.Matches(filename);
        if (matches.Count == 0)
        {
            _logger.LogWarning($"File {filename} did not match sheet number range regex {regexSheetNumberRange}");
        }

        foreach (var match in matches.OrderByDescending(m => m.Value.Length))
        {
            var parts = match.Value.Split("-");
            if (parts.Length != 2)
            {
                _logger.LogWarning($"File {filename} matched range {match.Value}, but had too many parts");
                continue;
            }

            // Should we make sure both parts are the same length
            if (parts[0].Length != parts[1].Length)
            {
                _logger.LogWarning(
                    $"File {filename} matched range {match.Value}, but range lengths differed {parts[0].Length} != {parts[1].Length}");
                continue;
            }

            bool hasStart = int.TryParse(parts[0], out var rangeStart);
            bool hasEnd = int.TryParse(parts[1], out var rangeEnd);
            if (!hasStart || !hasEnd)
            {
                _logger.LogWarning(
                    $"File {filename} matched range {match.Value}, but no integers {hasStart} {hasEnd}");
                continue;
            }

            // Make sure the second number is greater than the first
            if (rangeStart >= rangeEnd)
            {
                _logger.LogWarning($"File {filename} matched range {match.Value}, but {rangeStart} >= {rangeEnd}");
                continue;
            }

            return (rangeStart, rangeEnd);
        }

        return (sheetNumber, sheetNumber);
    }

    public int GetSingleSheetNumberFromFilename(string filename)
    {
        int? fullPathSheetNumber = GetSheetNumberFromFilename(filename);
        if (fullPathSheetNumber != null)
        {
            return fullPathSheetNumber.Value;
        }

        int sheetNumber = 0;
        var matches = regexSingleSheetNumber.Matches(filename);
        if (matches.Count == 0)
        {
            _logger.LogWarning($"File {filename} did not match singlePageNumber regex {regexSingleSheetNumber}");
            return sheetNumber;
        }

        foreach (var match in matches.OrderByDescending(m => m.Value.Length))
        {
            // we want to choose the longest set of digits that is NOT a date
            if (DateService.IsValidDate(match.Value))
            {
                _logger.LogWarning($"File {filename} matched sheet number {match.Value}, but was a date");
                continue;
            }

            bool isSheetNumber = int.TryParse(match.Value, out sheetNumber);
            if (isSheetNumber)
            {
                _logger.LogInformation($"File {filename} with sheet number {match.Value}");
                break;
            }

            _logger.LogWarning($"File {filename} with sheet number {match.Value} was not parsable");
        }

        return sheetNumber;
    }
}