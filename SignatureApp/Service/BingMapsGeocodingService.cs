﻿using System.Text;
using AutoMapper;
using DataInterface.ServiceInterfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Model.Geocoding;
using Model.Geocoding.Bing;
using Newtonsoft.Json;

namespace Service;

public class BingMapsGeocodingService : IGeocodingService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<BingMapsGeocodingService> _logger;
    private readonly IMapper _mapper;
    private string? _bingMapsApiKey;

    public BingMapsGeocodingService(
        HttpClient httpClient,
        IConfiguration configuration,
        ILogger<BingMapsGeocodingService> logger,
        IMapper mapper)
    {
        _httpClient = httpClient;
        _logger = logger;
        _mapper = mapper;
        _httpClient.BaseAddress = new Uri("http://dev.virtualearth.net/REST/v1/Locations");
        _bingMapsApiKey = configuration.GetValue<string>("Keys:BingMapsApiKey");
    }

    public async Task<ServiceResult<GeocodeResults?>> GeocodeAsync(AddressInput? address)
    {
        if (address == null)
        {
            return ServiceResult<GeocodeResults?>.Succeeded(null, "Address is null");
        }

        var builder = new StringBuilder();
        if (!String.IsNullOrEmpty(address.Country))
        {
            builder.Append(builder.Length == 0 ? "?" : "&");
            builder.Append($"countryRegion={address.Country}");
        }

        if (!String.IsNullOrEmpty(address.State))
        {
            builder.Append(builder.Length == 0 ? "?" : "&");
            builder.Append($"adminDistrict={address.State}");
        }

        if (!String.IsNullOrEmpty(address.City))
        {
            builder.Append(builder.Length == 0 ? "?" : "&");
            builder.Append($"locality={address.City}");
        }

        if (!String.IsNullOrEmpty(address.PostalCode))
        {
            builder.Append(builder.Length == 0 ? "?" : "&");
            builder.Append($"postalCode={address.PostalCode}");
        }

        if (!String.IsNullOrEmpty(address.AddressLine))
        {
            builder.Append(builder.Length == 0 ? "?" : "&");
            builder.Append($"addressLine={address.AddressLine}");
        }

        if (builder.Length == 0)
        {
            return ServiceResult<GeocodeResults?>.Failed("Address has no searchable fields");
        }

        builder.Append($"&key={_bingMapsApiKey}");

        var response = await _httpClient.GetAsync(builder.ToString());
        var content = await response.Content.ReadAsStringAsync();
        var geocodeResponse = JsonConvert.DeserializeObject<BingGeocodeResponse>(content);
        if (geocodeResponse == null)
        {
            var failureMessage = $"Bing Geocoding unable to deserialize response: {content}";
            _logger.LogError(failureMessage);
            return ServiceResult<GeocodeResults?>.Failed(failureMessage);
        }

        var matchingResource = geocodeResponse.ResourceSets
            .SelectMany(rs => rs.Resources)
            .FirstOrDefault(r => r.Confidence == "High");
        var results = new GeocodeResults();
        if (matchingResource == null)
        {
            var errorStrings = new List<string> { "Bing Maps Geocoding did not return any high confidence results." };
            var resources = geocodeResponse.ResourceSets
                .SelectMany(rs => rs.Resources).ToList();
            if (resources.Any())
            {
                var mediumResources = resources
                    .Where(r => r.Confidence == "Medium");
                foreach (var resource in mediumResources)
                {
                    var result = GetGeocodeResult(resource);
                    if (result != null)
                    {
                        results.PossibleMatches.Add(result);
                    }

                    if (resource.Address?.FormattedAddress != null)
                    {
                        errorStrings.Add(resource.Address.FormattedAddress);
                    }
                }
            }
            _logger.LogWarning(errorStrings[0]);

            var serviceResult = new ServiceResult<GeocodeResults?>
            {
                Status = ResultStatus.Failure,
                ErrorMessages = errorStrings.ToList(),
                Value = results,
            };
            return serviceResult;
        }

        results.ExactMatch = GetGeocodeResult(matchingResource);
        return ServiceResult<GeocodeResults?>.Succeeded(results);
    }

    private GeocodeResult? GetGeocodeResult(Resource resource)
    {
        if (resource.Address?.FormattedAddress == null) { return null; }

        var input = AddressParsingService.ParseFullAddressToInput(resource.Address.FormattedAddress);
        if (input == null) { return null; }
        var parseAddress = AddressParsingService.ParseInputToAddress(input);
        var geocodeResult = new GeocodeResult
        {
            NewParsedAddress = parseAddress,
            Latitude = (decimal)resource.Point.Coordinates[0],
            Longitude = (decimal)resource.Point.Coordinates[1]
        };
        return geocodeResult;
    }
}