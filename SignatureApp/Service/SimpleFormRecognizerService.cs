using Azure;
using Azure.AI.FormRecognizer.DocumentAnalysis;
using DataInterface.ServiceInterfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Model.Matters;
using Service.ServiceModels;
using System.Diagnostics;
using System.Text.Json;
using Polly;
using Polly.Retry;

namespace Service;

public class SimpleFormRecognizerService : BaseGeometryService
{
    private readonly string? _endpoint;
    private readonly string? _apiKey;
    private readonly ILogger<SimpleFormRecognizerService> _logger;
    private readonly IFileService _fileService;

    private readonly JsonSerializerOptions _serializerOptions = new()
    {
        WriteIndented = true,
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
    };


    public SimpleFormRecognizerService(
        IConfiguration configuration,
        ILogger<SimpleFormRecognizerService> logger,
        IFileService fileService
    )
    {
        _logger = logger;
        _fileService = fileService;
        _endpoint = configuration.GetValue<string>("FormRecognizer:Endpoint");
        _apiKey = configuration.GetValue<string>("FormRecognizer:ApiKey");
    }

    public async Task<FormRecognizerResults?> RecognizeFormAsync(Matter matter, MemoryStream memoryStream,
        bool isTemplate)
    {
        if (string.IsNullOrEmpty(_endpoint) || string.IsNullOrEmpty(_apiKey))
        {
            throw new Exception("FormRecognizer:Endpoint or FormRecognizer:ApiKey is not set");
        }

        var results = await AnalyzeDocumentAsync(memoryStream);

        // Save the raw results to blob storage
        if (results != null && matter.Id > 0)
        {
            await SaveRawFormRecognizerResult(results, matter.Id);
        }

        return GetFormRecognizerResults(matter.Type, results, isTemplate);
    }

    public async Task<AnalyzeResult?> AnalyzeDocumentAsync(MemoryStream memoryStream)
    {
        var retryOptions = new RetryStrategyOptions
        {
            ShouldHandle = new PredicateBuilder().Handle<RequestFailedException>(),
            UseJitter = true,
            DelayGenerator = static args =>
            {
                var delay = args.AttemptNumber switch
                {
                    0 => TimeSpan.FromSeconds(2),
                    1 => TimeSpan.FromSeconds(5),
                    2 => TimeSpan.FromSeconds(13),
                    _ => TimeSpan.FromSeconds(34)
                };
                return new ValueTask<TimeSpan?>(delay);
            }
        };

        var client = GetDocumentAnalysisClient();
        if (client == null)
        {
            _logger.LogError("FormRecognizer client is not configured properly.");
            return null;
        }

        ResiliencePipeline pipeline = new ResiliencePipelineBuilder()
            .AddRetry(retryOptions) // Add retry using the default options
            .Build(); // Builds the resilience pipeline
        var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(2 + 2 + 5 + 13 + 34 + 34));
        var results = await pipeline.ExecuteAsync(async token =>
        {
            memoryStream.Position = 0;
            AnalyzeDocumentOperation operation =
                await client.AnalyzeDocumentAsync(WaitUntil.Completed, "prebuilt-document", memoryStream,
                    cancellationToken: token);
            await operation.WaitForCompletionAsync(cancellationToken: token);
            return operation.Value;
        }, cancellationTokenSource.Token);
        return results;
    }

    private async Task SaveRawFormRecognizerResult(AnalyzeResult analyzeResult, int matterId)
    {
        try
        {
            var json = JsonSerializer.Serialize(analyzeResult, _serializerOptions);
            var fileName = $"formrecognizer/analyze_result_{DateTime.UtcNow:yyyyMMdd_HHmmss}.json";

            using var stream = new MemoryStream(System.Text.Encoding.UTF8.GetBytes(json));
            var saveResult = await _fileService.SaveFileStreamAsync($"matter{matterId}", fileName, stream,
                overWrite: true,
                CancellationToken.None);

            if (saveResult != null)
            {
                _logger.LogInformation("Successfully saved FormRecognizer results to {FileName}", fileName);
            }
            else
            {
                _logger.LogWarning("Failed to save FormRecognizer results to {FileName}", fileName);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving FormRecognizer results for matter {MatterId}",
                matterId);
        }
    }


    public DocumentAnalysisClient? GetDocumentAnalysisClient()
    {
        if (string.IsNullOrEmpty(_endpoint) || string.IsNullOrEmpty(_apiKey))
        {
            return null;
        }

        DocumentAnalysisClient client =
            new DocumentAnalysisClient(new Uri(_endpoint), new AzureKeyCredential(_apiKey));
        return client;
    }

    private FormRecognizerResults? GetFormRecognizerResults(MatterType matterType, AnalyzeResult? result,
        bool isTemplate)
    {
        if (result == null)
        {
            _logger.LogWarning("GetFormRecognizerResults got a null result");
            return null;
        }

        if (result.Tables.Count == 0)
        {
            _logger.LogWarning("GetFormRecognizerResults got no tables");
            return null;
        }

        if (result.Pages == null || result.Pages.Count == 0)
        {
            _logger.LogWarning("GetFormRecognizerResults got no pages");
            return null;
        }

        var pageDictionary = GetFormResultPagesDictionary(result.Pages);
        // Note this gives back all of the lines, not just the first page
        var formLines = GetFormLinesFromResultPages(matterType, pageDictionary, result.Pages, isTemplate);
        if (formLines == null)
        {
            return null;
        }

        var expectedRows = matterType == MatterType.Initiative ? 16 : 11;
        var expectedColumns = matterType == MatterType.Initiative ? 8 : 5;
        var tableResults = GetTableFromResultTables(result.Tables, expectedRows, expectedColumns);
        var formPages = pageDictionary.Select(kvp => kvp.Value).ToList();
        foreach (var page in formPages)
        {
            page.Lines = formLines.Where(fl => fl.FormPage != null && fl.FormPage.PageNumber == page.PageNumber)
                .ToList();
        }

        return new FormRecognizerResults
        {
            Pages = formPages,
            FormLines = formLines,
            SignatureTable = tableResults,
        };
    }

    private FormResultsTable? GetTableFromResultTables(IReadOnlyList<DocumentTable> tables, int expectedNumberRows,
        int expectedNumberColumns)
    {
        if (tables.Count == 0)
        {
            _logger.LogWarning($"GetSignatureRowsAndColumnsFromResultTables couldn't find a table");
            return null;
        }

        // Choose the one that has the correct number of rows and columns
        var table = tables.MinBy(t =>
            GetDistanceFromExpectedRowsAndColumns(t.RowCount, t.ColumnCount, expectedNumberRows,
                expectedNumberColumns));
        if (table == null || table.BoundingRegions == null || table.BoundingRegions.Count == 0)
        {
            _logger.LogWarning(
                $"GetSignatureRowsAndColumnsFromResultTables couldn't find a table with bounding regions");
            return null;
        }

        if (tables.Count > 1)
        {
            _logger.LogWarning("GetSignatureRowsAndColumnsFromResultTables found more than one table {0}",
                JsonSerializer.Serialize(tables.Select(t => new { t.ColumnCount, t.RowCount, t.BoundingRegions })));
            _logger.LogWarning("GetSignatureRowsAndColumnsFromResultTables chose table {0}",
                JsonSerializer.Serialize(new { table.ColumnCount, table.RowCount, table.BoundingRegions }));
        }

        var region = table.BoundingRegions[0];
        bool tooManyTables = table.Spans.Count > 1 || table.BoundingRegions.Count > 1;
        if (tooManyTables)
        {
            _logger.LogWarning($"GetSignatureRowsAndColumnsFromResultTables found a split table with too many spans");
        }

        if (region.BoundingPolygon.Count > 4)
        {
            _logger.LogWarning($"GetSignatureRowsAndColumnsFromResultTables found a non-rectangular table");
        }

        var formResultsTable = new FormResultsTable
        {
            ColumnCount = table.ColumnCount,
            RowCount = table.RowCount,
            BoundingPolygon = region.BoundingPolygon.ToList(),
        };
        formResultsTable.Cells = table.Cells.Select(c => new FormResultsCell
        {
            Table = formResultsTable,
            BoundingPolygon = c.BoundingRegions[0].BoundingPolygon.ToList(),
            ColumnIndex = c.ColumnIndex,
            ColumnSpan = c.ColumnSpan,
            Kind = GetCellKindFromDocumentTableCellKind(c.Kind),
            RowIndex = c.RowIndex,
            RowSpan = c.RowSpan,
            Content = c.Content,
        }).ToList();

        return formResultsTable;
    }

    private object GetDistanceFromExpectedRowsAndColumns(int rowCount, int colCount, int expectedRowCount,
        int expectedColCount)
    {
        var rowDiff = rowCount - expectedRowCount;
        var colDiff = colCount - expectedColCount;
        return rowDiff * rowDiff + colDiff * colDiff;
    }

    private CellKind GetCellKindFromDocumentTableCellKind(DocumentTableCellKind kind)
    {
        if (kind == DocumentTableCellKind.ColumnHeader)
        {
            return CellKind.ColumnHeader;
        }

        if (kind == DocumentTableCellKind.RowHeader)
        {
            return CellKind.RowHeader;
        }

        return CellKind.Content;
    }

    private Dictionary<int, FormResultsPage> GetFormResultPagesDictionary(IReadOnlyList<DocumentPage> pages)
    {
        return pages.ToDictionary(p => p.PageNumber, CreateFormResultsPage);
    }

    private List<FormResultsLine>? GetFormLinesFromResultPages(
        MatterType matterType,
        Dictionary<int, FormResultsPage> formResultsPages,
        IReadOnlyList<DocumentPage> pages,
        bool isTemplate)
    {
        var formLines = new List<FormResultsLine>();
        foreach (var page in pages)
        {
            var formResultsPage = formResultsPages[page.PageNumber];
            var ignoredWords = isTemplate
                ? matterType == MatterType.Candidate
                    ? Constants.CandidateTemplateIgnoredWords
                    : Constants.InitiativeTemplateIgnoredWords
                : matterType == MatterType.Candidate
                    ? Constants.CandidateSheetIgnoredWords
                    : Constants.InitiativeSheetIgnoredWords;


            var filteredLines =
                from line in page.Lines
                where !ignoredWords.Contains(line.Content)
                where line.Content.Trim().Length >= 2
                select line;

            var lines = filteredLines
                .Select(line => new ContentArea { Object = line, BoundingPolygon = line.BoundingPolygon }).ToList();
            var bucketAndLines = OrderLines(lines);
            foreach (var bucketAndLine in bucketAndLines)
            {
                if (bucketAndLine.LineOrField is not DocumentLine line)
                {
                    _logger.LogWarning($"BucketAndLine.LineOrField {bucketAndLine.LineNumber} is not a DocumentLine");
                    return null;
                }

                Debug.WriteLine($"{bucketAndLine.LineNumber} {bucketAndLine.AverageX} {line.Content}");
                var formLine = new FormResultsLine
                {
                    FormPage = formResultsPage,
                    BoundingPolygon = line.BoundingPolygon,
                    Content = line.Content,
                };
                formLines.Add(formLine);
            }
        }

        return formLines;
    }

    private static FormResultsPage CreateFormResultsPage(DocumentPage page)
    {
        var formResultsPage = new FormResultsPage
        {
            Height = page.Height ?? float.NaN,
            Width = page.Width ?? float.NaN,
            PageNumber = page.PageNumber,
        };
        return formResultsPage;
    }

    public static void WriteOutHtmlTable(StreamWriter writer, FormResultsTable table)
    {
        FormResultsCell[,] cells = new FormResultsCell[table.RowCount, table.ColumnCount];
        foreach (var cell in table.Cells)
        {
            cells[cell.RowIndex, cell.ColumnIndex] = cell;
        }

        WriteOutHtmlTableCells(writer, cells);
    }

    private static void WriteOutHtmlTableCells(StreamWriter writer, FormResultsCell[,] cells)
    {
        writer.WriteLine("<table border=\"1\">");
        for (int rowIndex = 0; rowIndex < cells.GetLength(0); rowIndex++)
        {
            writer.Write("<tr>");
            for (int colIndex = 0; colIndex < cells.GetLength(1); colIndex++)
            {
                var cell = cells[rowIndex, colIndex];
                if (cell.Content != null)
                {
                    var cellLeft = (decimal)Math.Min(cell.BoundingPolygon[0].X, cell.BoundingPolygon[3].X);
                    var cellTop = (decimal)Math.Min(cell.BoundingPolygon[0].Y, cell.BoundingPolygon[1].Y);
                    var cellRight = (decimal)Math.Max(cell.BoundingPolygon[1].X, cell.BoundingPolygon[2].X);
                    var cellBottom = (decimal)Math.Max(cell.BoundingPolygon[2].Y, cell.BoundingPolygon[3].Y);

                    writer.Write($"<td height={(cellBottom - cellTop) * 50} width={(cellRight - cellLeft) * 50}");
                    if (cell.ColumnSpan > 1)
                    {
                        writer.Write($" colspan=\"{cell.ColumnSpan}\"");
                    }

                    if (cell.RowSpan > 1)
                    {
                        writer.Write($" rowspan=\"{cell.RowSpan}\"");
                    }

                    if (colIndex == 0)
                    {
                        writer.Write(
                            $">{GetHeaderKindInfo(cell.Kind)}{GetSpanInfo(cell)}{cell.Content} H({cellTop}, {cellBottom}) = {cellBottom - cellTop}</td>");
                    }
                    else
                    {
                        writer.Write(
                            $">{GetHeaderKindInfo(cell.Kind)}{GetSpanInfo(cell)}{cell.Content} W({cellLeft}, {cellRight}) = {cellRight - cellLeft}</td>");
                    }
                }
                else
                {
                    writer.WriteLine("<td>NULL</td>");
                }
            }

            writer.WriteLine("</tr>");
        }

        writer.WriteLine("</table>");
    }

    private static string GetSpanInfo(FormResultsCell cell)
    {
        if (cell.RowSpan > 1)
        {
            return $"RS={cell.RowSpan} ";
        }

        if (cell.ColumnSpan > 1)
        {
            return $"CS={cell.ColumnSpan} ";
        }

        return "";
    }

    public static string GetHeaderKindInfo(CellKind cellKind)
    {
        if (cellKind == CellKind.ColumnHeader)
        {
            return "CH ";
        }

        if (cellKind == CellKind.RowHeader)
        {
            return "RH ";
        }

        if (cellKind == CellKind.StubHead)
        {
            return "SH ";
        }

        return "";
    }
}
