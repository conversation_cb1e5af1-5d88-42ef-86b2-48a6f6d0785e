using DataInterface.RepositoryInterfaces;
using Model.Rules;
using Model.Templates;
using Model.Workflow;
using Task = Model.Workflow.Task;

namespace Service;

public class InitiativeRuleGenerationService : BaseRuleGenerationService, IRuleGenerationService
{
    public InitiativeRuleGenerationService(IMatterRepository matterRepository,
        IRuleRepository ruleRepository,
        ITaskRepository taskRepository,
        ITemplateSignatureColumnRepository templateSignatureColumnRepository,
        ITranscribableFieldRepository transcribableFieldRepository
    ) : base(matterRepository, ruleRepository, taskRepository, templateSignatureColumnRepository,
        transcribableFieldRepository)
    {
    }

    internal override List<Rule> GetNonTaskSignatureRowRules(int matterId)
    {
        var rules = base.GetNonTaskSignatureRowRules(matterId);
        rules.AddRange(
        [
            Rule.CreateNonTaskRule(matterId, "Duplicate Signature",
                RuleContextType.Matter, "", "AreDuplicateVoters", "", needsReview: true),
        ]);
        return rules;
    }

    internal override List<Rule> GetNonTaskMatterRules(int matterId)
    {
        var rules = base.GetNonTaskMatterRules(matterId);
        rules.AddRange(
        [
            Rule.CreateNonTaskRule(matterId, "More Than 15 Signatures On Page",
                RuleContextType.Matter, "", "", ""),
        ]);
        return rules;
    }

    protected override List<Rule> GetRulesForColumnTask(int matterId, TemplateSignatureColumn column, List<Task> tasks)
    {
        var rules = base.GetRulesForColumnTask(matterId, column, tasks);

        var task = tasks.SingleOrDefault(task => task.FirstColumnIndex <= column.ColumnIndex
             && column.ColumnIndex <= task.LastColumnIndex && task.TaskType == TaskType.SignatureTableColumn);
        if (column.IsSkipped || column.CanBeInvalid == false || task == null)
        {
            return rules;
        }

        if (column.IsSignature == true)
        {
            rules.Add(Rule.CreateTranscriptionRule(matterId, task.Id, $"{column.Name} Is Printed"));
            rules.Add(Rule.CreateTranscriptionRule(matterId, task.Id, $"{column.Name} Does Not Match Known Signature"));
            rules.Add(Rule.CreateTranscriptionRule(matterId, task.Id, $"{column.Name} Not By Signer"));
        }
        else
        {
            rules.Add(Rule.CreateTranscriptionRule(matterId, task.Id, $"{column.Name} Not Printed By Signer"));
        }

        return rules;
    }

    protected override List<Rule> GetDateColumnSpecificRules(int matterId, TemplateSignatureColumn dateColumn)
    {
        var rules = base.GetDateColumnSpecificRules(matterId, dateColumn);
        rules.Add(Rule.CreateNonTaskRule(matterId, "Signature Collected Before Serial Number Issued",
            RuleContextType.SignatureRow, $"Row.Cells[''{dateColumn.Name}'']", "IsCellDateLessThanString",
            "Matter.Variables[''SerialNumberIssuedDate''].Value"));

        rules.Add(Rule.CreateNonTaskRule(matterId, "Signature Collected Before Statement of Organization Filed",
            RuleContextType.SignatureRow, $"Row.Cells[''{dateColumn.Name}'']", "IsCellDateLessThanString",
            "Matter.Variables[''DateStatementOfOrganizationFiled''].Value"));
        return rules;
    }


    protected override List<Rule> GetCrossReferenceRules(int matterId, List<TemplateSignatureColumn> columns,
        List<Task> fieldTasks, List<TranscribableField> fields)
    {
        var rules = base.GetCrossReferenceRules(matterId, columns, fieldTasks, fields);
        TemplateSignatureColumn? dateColumn = GetDateColumn(columns);

        var paidOrVolunteerCirculatorFieldTask = fieldTasks.SingleOrDefault(ft =>
                ft.Name.Contains("Paid", StringComparison.InvariantCultureIgnoreCase)
                && ft.Name.Contains("Volunteer", StringComparison.InvariantCultureIgnoreCase)
            //&& ft.Name.Contains("Circulator", StringComparison.InvariantCultureIgnoreCase)
        );

        var frontCirculatorIdFieldTask = fieldTasks.SingleOrDefault(ft =>
            ft.Name.Contains("Circulator", StringComparison.InvariantCultureIgnoreCase)
            && ft.Name.Contains("ID", StringComparison.InvariantCultureIgnoreCase)
            && ft.Name.Contains("Front", StringComparison.InvariantCultureIgnoreCase));
        var frontCirculatorIdField = frontCirculatorIdFieldTask != null
            ? fields.First(field => frontCirculatorIdFieldTask.DeserializedFieldIds.Contains(field.Id))
            : null;

        Task? circulatorNameFieldTask = null;
        var circulatorNameFieldTasks = fieldTasks.Where(ft =>
            ft.Name.Contains("Circulator", StringComparison.InvariantCultureIgnoreCase)
            && ft.Name.Contains("Name", StringComparison.InvariantCultureIgnoreCase)).ToList();
        if (circulatorNameFieldTasks.Count == 1)
        {
            circulatorNameFieldTask = circulatorNameFieldTasks.First();
        }
        else if (circulatorNameFieldTasks.Count > 1)
        {
            circulatorNameFieldTask = circulatorNameFieldTasks.SingleOrDefault(t =>
                t.Name.Contains("Printed", StringComparison.InvariantCultureIgnoreCase));
        }
        var circulatorNameField = circulatorNameFieldTask != null
            ? fields.First(field => circulatorNameFieldTask.DeserializedFieldIds.Contains(field.Id))
            : null;

        var backCirculatorIdFieldTask = fieldTasks.SingleOrDefault(ft =>
            ft.Name.Contains("Circulator", StringComparison.InvariantCultureIgnoreCase)
            && ft.Name.Contains("ID", StringComparison.InvariantCultureIgnoreCase)
            && ft.Name.Contains("Back", StringComparison.InvariantCultureIgnoreCase));
        var backCirculatorIdField = backCirculatorIdFieldTask != null
            ? fields.First(field => backCirculatorIdFieldTask.DeserializedFieldIds.Contains(field.Id))
            : null;

        if (paidOrVolunteerCirculatorFieldTask != null)
        {
            var paidOrVolunteerFields = fields.Where(field =>
                paidOrVolunteerCirculatorFieldTask.DeserializedFieldIds.Contains(field.Id)).ToList();
            var paidField = paidOrVolunteerFields.SingleOrDefault(field =>
                field.Name != null && field.Name.Contains("Paid", StringComparison.InvariantCultureIgnoreCase));
            var volunteerField = paidOrVolunteerFields.SingleOrDefault(field =>
                field.Name != null && field.Name.Contains("Volunteer", StringComparison.InvariantCultureIgnoreCase));
            if (paidField != null && volunteerField != null)
            {
                // It is a deficiency if the Paid and Volunteer fields are equal (i.e. both are checked or unchecked)
                rules.Add(Rule.CreateNonTaskRule(matterId,
                    $"Neither or both {paidOrVolunteerCirculatorFieldTask.Name} is checked",
                    RuleContextType.SignatureSheet, $"Sheet.Fields[''{paidField.Name}'']",
                    "AreFieldsEqual", $"Sheet.Fields[''{volunteerField.Name}'']"));
            }

            // If paid is checked the front circulator ID must be filled in
            if (paidField != null && frontCirculatorIdField != null)
            {
                // It is a deficiency if the Paid box is checked and the front circulator ID is blank
                rules.Add(Rule.CreateNonTaskRule(matterId,
                    $"{paidOrVolunteerCirculatorFieldTask.Name} is checked and front Circulator ID not filled",
                    RuleContextType.SignatureSheet, $"Sheet.Fields[''{paidField.Name}'']",
                    "IsCheckedAndBlank", $"Sheet.Fields[''{frontCirculatorIdField.Name}'']"));
            }

            // If paid is checked the back circulator ID must be filled in
            if (paidField != null && backCirculatorIdField != null)
            {
                // It is a deficiency if the Paid box is checked and the back circulator ID is blank
                rules.Add(Rule.CreateNonTaskRule(matterId,
                    $"{paidOrVolunteerCirculatorFieldTask.Name} is checked and back Circulator ID not filled",
                    RuleContextType.SignatureSheet, $"Sheet.Fields[''{paidField.Name}'']",
                    "IsCheckedAndBlank", $"Sheet.Fields[''{backCirculatorIdField.Name}'']"));
            }

            // If Paid is checked, should be able to find the circulator ID in the registered circulator DB
            if (paidField != null && frontCirculatorIdField != null)
            {
                // It is a deficiency if the circulator ID is not registered
                rules.Add(Rule.CreateNonTaskRule(matterId,
                    $"Paid Circulator ID Failure To Register With SOS",
                    RuleContextType.SignatureSheet, $"Sheet.Fields[''{frontCirculatorIdField.Name}'']",
                    "IsPaidCirculatorIdNotRegistered", $"Sheet.Fields[''{paidField.Name}'']"));
            }

            // If Paid is checked, should be able to find the circulator name in the registered circulator DB
            if (paidField != null && circulatorNameField != null)
            {
                // It is a deficiency if the circulator name is not registered
                rules.Add(Rule.CreateNonTaskRule(matterId,
                    $"Paid Circulator Name Failure To Register With SOS",
                    RuleContextType.SignatureSheet, $"Sheet.Fields[''{circulatorNameField.Name}'']",
                    "IsPaidCirculatorNameNotRegistered", $"Sheet.Fields[''{paidField.Name}'']"));
            }

            if (paidField != null && circulatorNameField != null)
            {
                // It is a deficiency if the circulator name or ID do not match the ballot measure
                rules.Add(Rule.CreateNonTaskRule(matterId,
                    $"Paid Circulator Not Registered for Correct Ballot Measure",
                    RuleContextType.SignatureSheet, $"Sheet.Circulator",
                    "IsPaidCirculatorNotRegisteredForCorrectBallotMeasure", $"Sheet.Fields[''{paidField.Name}'']"));
            }

        }

        // Front should match the back
        if (frontCirculatorIdFieldTask != null && frontCirculatorIdField != null
            && backCirculatorIdFieldTask != null && backCirculatorIdField != null)
        {
            // It is a deficiency if the front circulator ID is not equal to the back circulator ID
            rules.Add(Rule.CreateNonTaskRule(matterId,
                $"{frontCirculatorIdFieldTask.Name} does not equal {backCirculatorIdFieldTask.Name}",
                RuleContextType.SignatureSheet, $"Sheet.Fields[''{frontCirculatorIdField.Name}'']",
                "AreFieldsNotEqual", $"Sheet.Fields[''{backCirculatorIdField.Name}'']"));
        }

        var initiativeSerialNumberFieldTasks = fieldTasks.Where(ft =>
            (ft.Name.Contains("Initiative", StringComparison.InvariantCultureIgnoreCase)
             || ft.Name.Contains("Serial", StringComparison.InvariantCultureIgnoreCase))
            && ft.Name.Contains("Number", StringComparison.InvariantCultureIgnoreCase)).ToList();
        var frontInitiativeSerialNumberTask = initiativeSerialNumberFieldTasks
            .SingleOrDefault(ft => ft.Name.Contains("Front", StringComparison.InvariantCultureIgnoreCase));
        var backInitiativeSerialNumberTask = initiativeSerialNumberFieldTasks
            .SingleOrDefault(ft => ft.Name.Contains("Back", StringComparison.InvariantCultureIgnoreCase));
        var frontInitiativeSerialNumberField = frontInitiativeSerialNumberTask != null
            ? fields.First(field => frontInitiativeSerialNumberTask.DeserializedFieldIds.Contains(field.Id))
            : null;
        var backInitiativeSerialNumberField = backInitiativeSerialNumberTask != null
            ? fields.First(field => backInitiativeSerialNumberTask.DeserializedFieldIds.Contains(field.Id))
            : null;

        foreach (var initiativeSerialNumberFieldTask in initiativeSerialNumberFieldTasks)
        {
            var initiativeSerialNumberField = fields.First(field =>
                initiativeSerialNumberFieldTask.DeserializedFieldIds.Contains(field.Id));
            rules.Add(Rule.CreateNonTaskRule(matterId, $"{initiativeSerialNumberFieldTask.Name} Incorrect",
                RuleContextType.SignatureSheet, $"Sheet.Fields[''{initiativeSerialNumberField.Name}'']",
                "IsFieldNotEqualToString", "Matter.Variables[''InitiativeSerialNumber''].Value"));
        }

        if (frontInitiativeSerialNumberTask != null && frontInitiativeSerialNumberField != null
            && backInitiativeSerialNumberTask != null && backInitiativeSerialNumberField != null)
        {
            // It is a deficiency if the front initiative serial number is not equal to the back initiative serial number
            rules.Add(Rule.CreateNonTaskRule(matterId,
                $"{frontInitiativeSerialNumberTask.Name} does not equal {backInitiativeSerialNumberTask.Name}",
                RuleContextType.SignatureSheet, $"Sheet.Fields[''{frontInitiativeSerialNumberField.Name}'']",
                "AreFieldsNotEqual", $"Sheet.Fields[''{backInitiativeSerialNumberField.Name}'']"));
        }

        var initiativeTypeFieldTask = fieldTasks.SingleOrDefault(ft =>
            ft.Name.Contains("Initiative", StringComparison.InvariantCultureIgnoreCase)
            && ft.Name.Contains("Type", StringComparison.InvariantCultureIgnoreCase));
        if (initiativeTypeFieldTask != null)
        {
            var initiativeTypeField =
                fields.First(field => initiativeTypeFieldTask.DeserializedFieldIds.Contains(field.Id));
            rules.Add(Rule.CreateNonTaskRule(matterId, $"{initiativeTypeFieldTask.Name} Incorrect",
                RuleContextType.SignatureSheet, $"Sheet.Fields[''{initiativeTypeField.Name}'']",
                "IsFieldNotEqualToString", "Matter.Variables[''LawOrConstitutionalAmendment''].Value"));
        }

        var notarizationDateFieldTask = fieldTasks.SingleOrDefault(ft =>
            ft.Name.Contains("Notarization", StringComparison.InvariantCultureIgnoreCase)
            && ft.Name.Contains("Date", StringComparison.InvariantCultureIgnoreCase));

        if (dateColumn != null && notarizationDateFieldTask?.Name != null)
        {
            var notarizationDateField =
                fields.First(field => notarizationDateFieldTask.DeserializedFieldIds.Contains(field.Id));
            rules.Add(Rule.CreateNonTaskRule(matterId, "Notarization Date Prior To Petition Signature Date",
                RuleContextType.SignatureRow,
                $"Row.Cells[''{dateColumn.Name}'']", "IsCellDateGreaterThanField",
                $"Sheet.Fields[''{notarizationDateField.Name}'']"));
        }

        var notarySealExpirationFieldTask = fieldTasks.SingleOrDefault(ft =>
            ft.Name.Contains("Notary", StringComparison.InvariantCultureIgnoreCase)
            && ft.Name.Contains("Seal", StringComparison.InvariantCultureIgnoreCase));

        if (dateColumn != null && notarySealExpirationFieldTask?.Name != null)
        {
            var notarySealExpirationField =
                fields.First(field => notarySealExpirationFieldTask.DeserializedFieldIds.Contains(field.Id));
            rules.Add(Rule.CreateNonTaskRule(matterId, "Notary Seal Expired Before Petition Signature Date",
                RuleContextType.SignatureRow,
                $"Row.Cells[''{dateColumn.Name}'']", "IsCellDateGreaterThanField",
                $"Sheet.Fields[''{notarySealExpirationField.Name}'']"));
        }

        return rules;
    }
}