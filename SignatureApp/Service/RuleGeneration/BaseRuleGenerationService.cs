using DataInterface.RepositoryInterfaces;
using Model.Rules;
using Model.Templates;
using ModelTaskType = Model.Workflow.TaskType;
using Task = Model.Workflow.Task;

namespace Service
{
    public class BaseRuleGenerationService
    {
        private readonly IMatterRepository _matterRepository;
        private readonly IRuleRepository _ruleRepository;
        private readonly ITaskRepository _taskRepository;
        private readonly ITemplateSignatureColumnRepository _templateSignatureColumnRepository;
        private readonly ITranscribableFieldRepository _transcribableFieldRepository;

        public BaseRuleGenerationService(IMatterRepository matterRepository,
            IRuleRepository ruleRepository,
            ITaskRepository taskRepository,
            ITemplateSignatureColumnRepository templateSignatureColumnRepository,
            ITranscribableFieldRepository transcribableFieldRepository
        )
        {
            _matterRepository = matterRepository;
            _ruleRepository = ruleRepository;
            _taskRepository = taskRepository;
            _templateSignatureColumnRepository = templateSignatureColumnRepository;
            _transcribableFieldRepository = transcribableFieldRepository;
        }

        public async Task<bool> CreateIfNecessaryAsync(int matterId, int templateId)
        {
            var matter = await _matterRepository.GetByIdAsync(matterId);
            if (matter == null)
            {
                return false;
            }

            var rules = new List<Rule>();
            // get the existing Non-Task rules
            var nonTaskRules = await _ruleRepository.GetAllNonTaskByMatterAsync(matterId);
            if (nonTaskRules.Count == 0)
            {
                rules.AddRange(await CreateNonTaskRelatedRulesAsync(matterId, templateId));
            }

            // Check to see if there are already rules for these tasks
            var rulesForTasks = await
                _ruleRepository.GetAllByMatterAndTemplateAsync(matterId, templateId);
            if (rulesForTasks.Count == 0)
            {
                rules.AddRange(await CreateTaskRelatedRules(matterId, templateId));
            }

            bool areAnyRulesToCreate = rules.Any();
            if (areAnyRulesToCreate)
            {
                await _ruleRepository.CreateAsync(rules);
            }

            return areAnyRulesToCreate;
        }

        internal virtual List<Rule> GetNonTaskSignatureRowRules(int matterId)
        {
            return
            [
                Rule.CreateNonTaskRule(matterId, "Signer Ineligible - Convicted Felon",
                    RuleContextType.SignatureRow, "", "", ""),

                Rule.CreateNonTaskRule(matterId, "Signer Ineligible - Federal Only Voter",
                    RuleContextType.SignatureRow, "Row.RegisteredVoter.FedIdOnly", "IsTrue", ""),

                Rule.CreateNonTaskRule(matterId, "Signer Ineligible - Non-Citizen",
                    RuleContextType.SignatureRow, "", "", ""),
            ];
        }

        internal virtual async Task<List<Rule>> GetNonTaskSignatureSheetRulesAsync(int matterId, int templateId)
        {
            var transcribableFields = await _transcribableFieldRepository.GetAllByTemplateIdAsync(templateId);
            var notNullFields = transcribableFields.Where(tf => tf.Name != null).ToList();

            var circulatorAddressField = notNullFields.SingleOrDefault(tf =>
                tf.Name != null
                && tf.Name.Contains("Circulator", StringComparison.InvariantCultureIgnoreCase)
                && tf.Name.Contains("Address", StringComparison.InvariantCultureIgnoreCase));

            var circulatorCityField = notNullFields.SingleOrDefault(tf =>
                tf.Name != null
                && tf.Name.Contains("Circulator", StringComparison.InvariantCultureIgnoreCase)
                && tf.Name.Contains("City", StringComparison.InvariantCultureIgnoreCase));

            var rules = new List<Rule>();
            if (circulatorAddressField != null && circulatorCityField != null)
            {
                rules.Add(Rule.CreateNonTaskRule(matterId, "Inconsistent Circulator Address",
                    RuleContextType.SignatureSheet,
                    $"Sheet.Fields[''{circulatorAddressField.Name}'']", "AreAddressLinesInconsistent",
                    $"Sheet.Fields[''{circulatorCityField.Name}'']", needsReview:true));

                rules.Add(Rule.CreateNonTaskRule(matterId, "Non-residential Circulator Address",
                    RuleContextType.SignatureSheet,
                    $"Sheet.Fields[''{circulatorAddressField.Name}'']", "IsNonResidentialCirculatorAddress",
                    $"Sheet.Fields[''{circulatorCityField.Name}'']", needsReview:true));
            }

            rules.Add(Rule.CreateNonTaskRule(matterId, "Petition Header Affidavit Altered",
                RuleContextType.SignatureSheet, "TemplatePage.FormLines",
                "IsFrontTextDifferent", "SheetPage.FormLines"));

            rules.Add(Rule.CreateNonTaskRule(matterId, "Title & Text Not Attached During Circulation",
                RuleContextType.SignatureSheet, "", "", ""));

            rules.Add(Rule.CreateNonTaskRule(matterId, "Circulator Affidavit Altered",
                RuleContextType.SignatureSheet, "TemplatePage.FormLines",
                "IsBackTextDifferent", "SheetPage.FormLines"));

            rules.Add(Rule.CreateNonTaskRule(matterId, "Circulator Affidavit Not Attached During Circulation",
                RuleContextType.SignatureSheet, "", "", ""));

            rules.Add(Rule.CreateNonTaskRule(matterId, "Fraudulent Circulator Avowal",
                RuleContextType.SignatureSheet,
                "", "", ""));

            rules.Add(Rule.CreateNonTaskRule(matterId, "Miscellaneous Objection on Sheet",
                RuleContextType.SignatureSheet, "", "", ""));
            return rules;
        }

        internal virtual List<Rule> GetNonTaskCirculatorRules(int matterId)
        {
            return
            [
                Rule.CreateNonTaskRule(matterId, "Circulator Ineligible - Convicted Felon",
                    RuleContextType.Circulator, "", "AreCirculatorsInvalid", ""),
                Rule.CreateNonTaskRule(matterId, "Circulator Ineligible - Federal Only Voter",
                    RuleContextType.Circulator, "", "AreCirculatorsInvalid", ""),
                Rule.CreateNonTaskRule(matterId, "Circulator Ineligible - Non-Citizen",
                    RuleContextType.Circulator, "", "AreCirculatorsInvalid", ""),
                Rule.CreateNonTaskRule(matterId, "Circulator Ineligible - Underage",
                    RuleContextType.Circulator, "", "AreCirculatorsInvalid", ""),

                Rule.CreateNonTaskRule(matterId, "Nonresident Circulator Failure To Register With SOS",
                    RuleContextType.Circulator, "", "AreCirculatorsInvalid", ""),

                Rule.CreateNonTaskRule(matterId, "Ineligible Circulator",
                    RuleContextType.Circulator, "", "AreCirculatorsInvalid", ""),
            ];
        }


        internal virtual List<Rule> GetNonTaskMatterRules(int matterId)
        {
            return
            [
                Rule.CreateNonTaskRule(matterId, "Pervasive Signature Fraud",
                    RuleContextType.Matter, "",
                    "AreMultipleSheetsTheSame", ""),

                Rule.CreateNonTaskRule(matterId, "Failure to Identify Expiration of Term for Vacant Office",
                    RuleContextType.Matter, "", "", ""),

                Rule.CreateNonTaskRule(matterId, "Wrong Petition Form Utilized",
                    RuleContextType.Matter, "", "", ""),

                Rule.CreateNonTaskRule(matterId, "Wrong Petition Margins",
                    RuleContextType.Matter, "", "", ""),

                Rule.CreateNonTaskRule(matterId, "Fraud in Nomination Paper",
                    RuleContextType.Matter, "", "", ""),

                Rule.CreateNonTaskRule(matterId, "Miscellaneous Objection on Matter",
                    RuleContextType.Matter, "", "", ""),
            ];
        }

        internal virtual async Task<List<Rule>> CreateNonTaskRelatedRulesAsync(int matterId, int templateId)
        {
            var rules = new List<Rule>();
            rules.AddRange(GetNonTaskSignatureRowRules(matterId));
            rules.AddRange(await GetNonTaskSignatureSheetRulesAsync(matterId, templateId));
            rules.AddRange(GetNonTaskCirculatorRules(matterId));
            rules.AddRange(GetNonTaskMatterRules(matterId));
            return rules;
        }


        internal virtual async Task<List<Rule>> CreateTaskRelatedRules(int matterId, int templateId)
        {
            var rules = new List<Rule>();

            var transcribableFields = await _transcribableFieldRepository.GetAllByTemplateIdAsync(templateId);

            var columns = await _templateSignatureColumnRepository.GetAllByTemplateIdAsync(templateId);
            rules.AddRange(GetColumnRelatedRules(matterId, columns));

            var tasks = await _taskRepository.GetAllByTemplateAndMatterId(templateId, matterId);
            foreach (var column in columns)
            {
                rules.AddRange(GetRulesForColumnTask(matterId, column, tasks));
            }

            // Actual name right now is // Name = $"Match Name and Address to Registered Voter",
            var voterRegistrationTask = tasks.FirstOrDefault(x =>
                // Registered or Registration
                x.Name.Contains("Regist", StringComparison.InvariantCultureIgnoreCase)
                && x.Name.Contains("Voter", StringComparison.InvariantCultureIgnoreCase));
            if (voterRegistrationTask != null)
            {
                rules.AddRange([
                    Rule.CreateTranscriptionRule(matterId, voterRegistrationTask.Id, "Registered Voter Name Mismatch"),
                    Rule.CreateTranscriptionRule(matterId, voterRegistrationTask.Id,
                        "Registered Voter Address Mismatch"),
                    Rule.CreateTranscriptionRule(matterId, voterRegistrationTask.Id, "Signer Not Registered"),
                ]);
            }

            var fieldTasks = tasks
                .Where(x => !string.IsNullOrEmpty(x.TranscribableFieldIds))
                .ToList();
            foreach (var fieldTask in fieldTasks)
            {
                var fieldIdsForTask = fieldTask.DeserializedFieldIds;
                var fields = transcribableFields.Where(tf => fieldIdsForTask.Contains(tf.Id)).ToList();

                if (fields.Any(f => f.CanBeInvalid))
                {
                    var firstField = fields.First();
                    var fieldName = firstField.Name;

                    rules.Add(Rule.CreateTranscriptionRule(matterId, fieldTask.Id, $"{fieldTask.Name} is Missing"));

                    if (firstField.IsSignature)
                    {
                        rules.AddRange([
                            Rule.CreateTranscriptionRule(matterId, fieldTask.Id, $"{fieldTask.Name} is Invalid"),
                            Rule.CreateTranscriptionRule(matterId, fieldTask.Id, $"{fieldTask.Name} is Printed"),
                            Rule.CreateTranscriptionRule(matterId, fieldTask.Id, $"{fieldTask.Name} is Not By Signer"),
                        ]);
                    }
                    else
                    {
                        rules.AddRange([
                            Rule.CreateTranscriptionRule(matterId, fieldTask.Id, $"{fieldTask.Name} is Incorrect"),
                            Rule.CreateTranscriptionRule(matterId, fieldTask.Id, $"{fieldTask.Name} is Illegible"),
                        ]);
                    }

                    rules.Add(
                        Rule.CreateTranscriptionRule(matterId, fieldTask.Id, $"Other {fieldTask.Name} Deficiency"));

                    rules.AddRange(GetFieldSpecificRules(matterId, fieldTask, fieldName));

                    // Any field deficiency makes the whole sheet deficient
                    rules.Add(Rule.CreateTaskRule(matterId, fieldTask.Id, $"Incorrect {fieldTask.Name} on sheet",
                        RuleContextType.SignatureSheet, $"Sheet.Fields[''{fieldName}'']", "IsFieldInvalid", ""));
                }
            }

            rules.AddRange(GetNonTaskColumnRules(matterId, columns));

            rules.AddRange(GetCrossReferenceRules(matterId, columns, fieldTasks, transcribableFields));
            return rules;
        }

        protected virtual List<Rule> GetRulesForColumnTask(int matterId, TemplateSignatureColumn column,
            List<Task> tasks)
        {
            var task = tasks.SingleOrDefault(task =>
                column.ColumnIndex >= task.FirstColumnIndex
                && column.ColumnIndex <= task.LastColumnIndex
                && task.TaskType == ModelTaskType.SignatureTableColumn);
            if (column.IsSkipped || column.CanBeInvalid == false || column.Name == null || task == null)
            {
                return new List<Rule>();
            }

            return GetRulesForTask(matterId, task, column.Name, column.IsSignature == true);
        }

        protected virtual List<Rule> GetRulesForTask(int matterId, Task task, string name, bool isSignature = false)
        {
            var rules = new List<Rule>();
            rules.Add(Rule.CreateTranscriptionRule(matterId, task.Id, $"Missing {name}"));
            if (!isSignature)
            {
                rules.Add(Rule.CreateTranscriptionRule(matterId, task.Id, $"Illegible {name}"));
            }

            rules.Add(Rule.CreateTranscriptionRule(matterId, task.Id, $"Invalid {name}"));
            rules.Add(Rule.CreateTranscriptionRule(matterId, task.Id, $"Other {name} Deficiency"));
            return rules;
        }

        protected virtual List<Rule> GetNonTaskColumnRules(int matterId, List<TemplateSignatureColumn> columns)
        {
            TemplateSignatureColumn? addressLineColumn;
            TemplateSignatureColumn? cityColumn;
            TemplateSignatureColumn? zipCodeColumn;

            // Get the address columns
            var addressColumns = columns.Where(x => x.IsAddress == true).ToList();
            // if there is just one column then assign all three columns to that one
            if (addressColumns.Count == 1)
            {
                addressLineColumn = cityColumn = zipCodeColumn = addressColumns.First();
            }
            else
            {
                addressLineColumn = addressColumns.FirstOrDefault(x => x.Name != null && x.Name.Contains("address", StringComparison.CurrentCultureIgnoreCase));
                cityColumn = addressColumns.FirstOrDefault(x => x.Name != null && x.Name.Contains("city", StringComparison.CurrentCultureIgnoreCase));
                zipCodeColumn = addressColumns.FirstOrDefault(x => x.Name != null && (x.Name.Contains("zip", StringComparison.CurrentCultureIgnoreCase)
                    || x.Name.Contains("postal", StringComparison.Ordinal)));
            }

            var rules = new List<Rule>();
            rules.AddRange([
                Rule.CreateNonTaskRule(matterId, "Bad Voter Address",
                    RuleContextType.SignatureRow, $"Row.Signatory", "IsBadAddress",
                    "", needsReview:true),

                Rule.CreateNonTaskRule(matterId, "Non-residential Voter Address",
                    RuleContextType.SignatureRow, $"Row.Signatory", "IsNonResidentialSignatoryAddress",
                    "", needsReview:true),

                Rule.CreateNonTaskRule(matterId, "Signature Address Line Out of County",
                    RuleContextType.SignatureRow, $"Row.Signatory", "IsAddressLineOutOfBoundsOf",
                    "Matter.Variables[''County''].Value"),

                Rule.CreateNonTaskRule(matterId, "Signature City Out of County",
                    RuleContextType.SignatureRow, $"Row.Cells[''{cityColumn?.Name}'']", "IsCityOutOfBoundsOf",
                    "Matter.Variables[''County''].Value"),

                Rule.CreateNonTaskRule(matterId, "Signature ZipCode Out of County",
                    RuleContextType.SignatureRow, $"Row.Cells[''{zipCodeColumn?.Name}'']", "IsZipCodeOutOfBoundsOf",
                    "Matter.Variables[''County''].Value"),

                Rule.CreateNonTaskRule(matterId, "Signature Address Line Out of District",
                    RuleContextType.SignatureRow, $"Row.Signatory", "IsAddressLineOutOfBoundsOf",
                    "Matter.Variables[''District''].Value"),

                Rule.CreateNonTaskRule(matterId, "Signature City Out of District",
                    RuleContextType.SignatureRow, $"Row.Cells[''{cityColumn?.Name}'']", "IsCityOutOfBoundsOf",
                    "Matter.Variables[''District''].Value"),

                Rule.CreateNonTaskRule(matterId, "Signature ZipCode Out of District",
                    RuleContextType.SignatureRow, $"Row.Cells[''{zipCodeColumn?.Name}'']", "IsZipCodeOutOfBoundsOf",
                    "Matter.Variables[''District''].Value"),
            ]);
            return rules;
        }

        protected virtual List<Rule> GetCrossReferenceRules(int matterId, List<TemplateSignatureColumn> columns,
            List<Task> fieldTasks, List<TranscribableField> fields)
        {
            var rules = new List<Rule>();

            var circulatorNameFieldTasks = fieldTasks.Where(ft =>
                ft.Name.Contains("Circulator", StringComparison.InvariantCultureIgnoreCase)
                && ft.Name.Contains("Name", StringComparison.InvariantCultureIgnoreCase)).ToList();
            Task? circulatorNameFieldTask;
            if (circulatorNameFieldTasks.Count > 1)
            {
                circulatorNameFieldTask = circulatorNameFieldTasks.SingleOrDefault(ft =>
                    ft.Name.Contains("Printed", StringComparison.InvariantCultureIgnoreCase));
            }
            else
            {
                circulatorNameFieldTask = circulatorNameFieldTasks.SingleOrDefault();
            }
            var circulatorNameField = circulatorNameFieldTask != null
                ? fields.First(field => circulatorNameFieldTask.DeserializedFieldIds.Contains(field.Id))
                : null;

            var backCirculatorIdFieldTask = fieldTasks.SingleOrDefault(ft =>
                ft.Name.Contains("Circulator", StringComparison.InvariantCultureIgnoreCase)
                && ft.Name.Contains("ID", StringComparison.InvariantCultureIgnoreCase)
                && ft.Name.Contains("Back", StringComparison.InvariantCultureIgnoreCase));
            var backCirculatorIdField = backCirculatorIdFieldTask != null
                ? fields.First(field => backCirculatorIdFieldTask.DeserializedFieldIds.Contains(field.Id))
                : null;

            var backCirculatorStateFieldTask = fieldTasks.SingleOrDefault(ft =>
                ft.Name.Contains("Circulator", StringComparison.InvariantCultureIgnoreCase)
                && ft.Name.Contains("State", StringComparison.InvariantCultureIgnoreCase));
            var backCirculatorStateField =backCirculatorStateFieldTask != null
                ? fields.First(field => backCirculatorStateFieldTask.DeserializedFieldIds.Contains(field.Id))
                : null;

            // If the circulator is out of state, should be able to find the circulator Id in the registered circulator DB
            if (backCirculatorIdField != null && backCirculatorStateField != null)
            {
                // It is a deficiency if the circulator ID is not registered
                rules.Add(Rule.CreateNonTaskRule(matterId,
                    $"Out of State Circulator ID Failure To Register With SOS",
                    RuleContextType.SignatureSheet, $"Sheet.Fields[''{backCirculatorIdField.Name}'']",
                    "IsOutOfStateCirculatorIdNotRegistered", $"Sheet.Fields[''{backCirculatorStateField.Name}'']"));
            }

            // If the circulator is out of state, should be able to find the circulator Id in the registered circulator DB
            if (circulatorNameField != null && backCirculatorStateField != null)
            {
                // It is a deficiency if the circulator ID is not registered
                rules.Add(Rule.CreateNonTaskRule(matterId,
                    $"Out of State Circulator Name Failure To Register With SOS",
                    RuleContextType.SignatureSheet, $"Sheet.Fields[''{circulatorNameField.Name}'']",
                    "IsOutOfStateCirculatorNameNotRegistered", $"Sheet.Fields[''{backCirculatorStateField.Name}'']"));
            }

            return rules;
        }

        protected virtual List<Rule> GetFieldSpecificRules(int matterId, Task fieldTask, string? fieldName)
        {
            return new List<Rule>();
        }


        protected virtual List<Rule> GetColumnRelatedRules(int matterId, List<TemplateSignatureColumn> columns)
        {
            var rules = new List<Rule>();
            TemplateSignatureColumn? dateColumn = GetDateColumn(columns);

            if (dateColumn == null)
            {
                return rules;
            }

            rules.AddRange(GetDateColumnSpecificRules(matterId, dateColumn));

            rules.AddRange([
                Rule.CreateNonTaskRule(matterId, "Signer Registered After Date Of Signing",
                    RuleContextType.SignatureRow,
                    $"Row.Cells[''{dateColumn.Name}'']", "IsCellDateLessThanString",
                    "Row.RegisteredVoter.RegistrationDate"),
                Rule.CreateNonTaskRule(matterId, "Signer Ineligible - Underage", RuleContextType.SignatureRow,
                    "Row.RegisteredVoter.BirthYear", "YearPlus18GreaterThan",
                    $"Row.Cells[''{dateColumn.Name}''].Value"),
            ]);

            return rules;
        }

        protected virtual List<Rule> GetDateColumnSpecificRules(int matterId, TemplateSignatureColumn dateColumn)
        {
            var rules = new List<Rule>
            {
                Rule.CreateNonTaskRule(matterId, "Out of Sequence Date",
                    RuleContextType.SignatureColumn, $"Row.Cells[''{dateColumn.Name}'']", "IsDateOutOfSequenceWithin",
                    "Column", needsReview:true)
            };
            return rules;
        }

        protected static TemplateSignatureColumn? GetDateColumn(List<TemplateSignatureColumn> columns)
        {
            return columns.FirstOrDefault(x => x.IsDate == true);
        }
    }
}