using DataInterface.RepositoryInterfaces;
using Model.Rules;
using Model.Templates;

namespace Service;

public class CandidateRuleGenerationService : BaseRuleGenerationService, IRuleGenerationService
{
    public CandidateRuleGenerationService(IMatterRepository matterRepository,
        IRuleRepository ruleRepository,
        ITaskRepository taskRepository,
        ITemplateSignatureColumnRepository templateSignatureColumnRepository,
        ITranscribableFieldRepository transcribableFieldRepository
    ) : base(matterRepository, ruleRepository, taskRepository, templateSignatureColumnRepository,
        transcribableFieldRepository)
    {
    }

    internal override List<Rule> GetNonTaskSignatureRowRules(int matterId)
    {
        var rules = base.GetNonTaskSignatureRowRules(matterId);
        rules.AddRange(
        [
            Rule.CreateNonTaskRule(matterId, "Duplicate Signature (Same Candidate)",
                RuleContextType.Matter, "", "AreDuplicateVoters", "", needsReview: true),

            Rule.CreateNonTaskRule(matterId, "Duplicate Signature (Different Candidate)",
                RuleContextType.Matter, "", "AreDuplicateCrossCandidateVoters", "", needsReview: true),
        ]);
        return rules;
    }

    internal override List<Rule> GetNonTaskMatterRules(int matterId)
    {
        var rules = base.GetNonTaskMatterRules(matterId);
        rules.AddRange(
        [
            Rule.CreateNonTaskRule(matterId, "Candidate Not Qualified",
                RuleContextType.Matter, "", "", ""),

            Rule.CreateNonTaskRule(matterId, "Picture Implies Current Officeholder",
                RuleContextType.Matter, "", "", ""),
        ]);
        return rules;
    }

    internal override async Task<List<Rule>> CreateNonTaskRelatedRulesAsync(int matterId, int templateId)
    {
        var rules = await base.CreateNonTaskRelatedRulesAsync(matterId, templateId);
        rules.AddRange([
            Rule.CreateNonTaskRule(matterId, "Signer Registered With Wrong Party", RuleContextType.SignatureRow,
                "Row.RegisteredVoter.Party", "ArePartiesNotEqual", "Sheet.Fields[''Party''].Value")
        ]);
        return rules;
    }

    protected override List<Rule> GetFieldSpecificRules(int matterId, Model.Workflow.Task fieldTask, string? fieldName)
    {
        var rules = base.GetFieldSpecificRules(matterId, fieldTask, fieldName);

        if (fieldTask?.Name != null &&
            fieldTask.Name.Contains("Election", StringComparison.InvariantCultureIgnoreCase) &&
            fieldTask.Name.Contains("Date", StringComparison.InvariantCultureIgnoreCase))
        {
            rules.Add(Rule.CreateTaskRule(matterId, fieldTask.Id, $"{fieldTask.Name} Does Not Match",
                RuleContextType.SignatureSheet, $"Sheet.Fields[''{fieldName}''].Value", "AreDatesDifferent",
                "Matter.Variables[''ElectionDate''].Value"));
        }

        return rules;
    }

    protected override List<Rule> GetDateColumnSpecificRules(int matterId, TemplateSignatureColumn dateColumn)
    {
        var rules = base.GetDateColumnSpecificRules(matterId, dateColumn);
        rules.AddRange([
            Rule.CreateNonTaskRule(matterId, "Signature Collected Before Statement of Interest Filed",
                RuleContextType.SignatureRow, $"Row.Cells[''{dateColumn.Name}'']", "IsCellDateLessThanString",
                "Matter.Variables[''StatementOfInterestFiledDate''].Value")
        ]);
        return rules;
    }
}