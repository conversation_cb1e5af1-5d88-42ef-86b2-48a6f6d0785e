﻿//#define ADD_DATA_LOGS

using DataInterface.RepositoryInterfaces;
using Microsoft.Extensions.Logging;
using Model.SignatureSheets;
using Model.Templates;
using Service.ServiceModels;
using System.Diagnostics;
using System.util;
using DataInterface.ServiceInterfaces;


namespace Service;

public class SignatureSheetHierarchyService
{
    //static Regex regexValidLines = new Regex(@"accepted: (?<validLines>\d+)|(?<validLines>\d+) valid lines");

    private readonly ILogger<SignatureSheetHierarchyService> _logger;
    private readonly ITemplatePageRepository _templatePageRepository;
    private readonly ITranscribableFieldRepository _transcribableFieldRepository;
    private readonly IDataProcessingLogRepository _dataProcessingLogRepository;
    private readonly ITemplateFormLineRepository _templateFormLineRepository;
    private readonly ITemplateSignatureTableRepository _templateSignatureTableRepository;

    private readonly ISignatureSheetRepository _signatureSheetRepository;
    private readonly ISignatureSheetFieldRepository _signatureSheetFieldRepository;
    private readonly ISignatureSheetPageRepository _signatureSheetPageRepository;
    private readonly ISignatureSheetTableRepository _signatureSheetTableRepository;
    private readonly SheetNumberService _sheetNumberService;
    private readonly PdfManipulationService _pdfManipulationService;
    private readonly SimpleFormRecognizerService _formRecognizerService;
    private readonly SignatureSheetGeometryService _signatureSheetGeometryService;

    public SignatureSheetHierarchyService(
        IDataProcessingLogRepository dataProcessingLogRepository,
        ILogger<SignatureSheetHierarchyService> logger,
        ITemplateFormLineRepository templateFormLineRepository,
        ITemplatePageRepository templatePageRepository,
        ITranscribableFieldRepository transcribableFieldRepository,
        ITemplateSignatureTableRepository templateSignatureTableRepository,
        ISignatureSheetRepository signatureSheetRepository,
        ISignatureSheetFieldRepository signatureSheetFieldRepository,
        ISignatureSheetPageRepository signatureSheetPageRepository,
        ISignatureSheetTableRepository signatureSheetTableRepository,
        SheetNumberService sheetNumberService,
        PdfManipulationService pdfManipulationService,
        SimpleFormRecognizerService formRecognizerService,
        SignatureSheetGeometryService signatureSheetGeometryService
    )
    {
        _logger = logger;
        _templatePageRepository = templatePageRepository;
        _transcribableFieldRepository = transcribableFieldRepository;
        _dataProcessingLogRepository = dataProcessingLogRepository;
        _templateFormLineRepository = templateFormLineRepository;
        _templateSignatureTableRepository = templateSignatureTableRepository;
        _signatureSheetRepository = signatureSheetRepository;
        _signatureSheetFieldRepository = signatureSheetFieldRepository;
        _signatureSheetPageRepository = signatureSheetPageRepository;
        _signatureSheetTableRepository = signatureSheetTableRepository;
        _sheetNumberService = sheetNumberService;
        _pdfManipulationService = pdfManipulationService;
        _formRecognizerService = formRecognizerService;
        _signatureSheetGeometryService = signatureSheetGeometryService;
    }

    public async Task<ServiceResult<List<SignatureSheet>>> SaveSignatureSheetHierarchyAsync(Stream fileStream,
        string fileName,
        SignatureSheetUpload upload,
        WhichPartsAreValidDTO? whichPartsAreValid = null)
    {
        var result = new ServiceResult<List<SignatureSheet>> { Value = new List<SignatureSheet>() };
        await AddDataLogAsync(upload.Id, "Still Starting Save SignatureSheet Hierarchy");

        var templatePages = await _templatePageRepository.GetByTemplateIdAsync(upload.TemplateId);
        var transcribableFields =
            await _transcribableFieldRepository.GetNamedByTemplateIdAsync(upload.TemplateId);
        await AddDataLogAsync(upload.Id, $"Got Template and TranscribableFields for SignatureSheet");

        var templateSignatureTable =
            await _templateSignatureTableRepository.GetByTemplateIdAsync(upload.TemplateId);
        await AddDataLogAsync(upload.Id,
            $"SignatureTable {templateSignatureTable?.Id} for SignatureSheet");
        if (templateSignatureTable == null)
        {
            return ServiceResult<List<SignatureSheet>>.Failed(
                $"No Table found for template {upload.TemplateId}");
        }

        await AddDataLogAsync(upload.Id, $"Gathered required information");
        var sheetNumber = _sheetNumberService.GetSingleSheetNumberFromFilename(fileName);

        int count = 0;
        try
        {
            await _pdfManipulationService.SplitIntoDifferentStreamsAsync(fileStream, async (stream, index) =>
            {
                count++;
                // These correspond to the three substeps of Document Intelligence
                // Step 1 - Recognize the information on the page
                FormRecognizerResults? formResults;
                using (var memoryStream = new MemoryStream())
                {
                    await stream.CopyToAsync(memoryStream);
                    memoryStream.Position = 0;
                    formResults = await _formRecognizerService.RecognizeFormAsync(
                        upload.Matter, memoryStream, isTemplate: false);
                }

                if (formResults == null)
                {
                    result.ErrorMessages.Add($"Unable to recognize form for stream {index + 1} of {upload.FileName}");
                    return;
                }

                // Step 2 - Transform the results
                var templateFormLines = await _templateFormLineRepository.GetAllByTemplateIdAsync(upload.TemplateId);
                var transformResult = _signatureSheetGeometryService.Transform(
                    upload.Matter.Type,
                    templatePages.ToDictionary(tp => tp.PageNumber),
                    transcribableFields,
                    templateSignatureTable, formResults, upload,
                    index, sheetNumber, templateFormLines, whichPartsAreValid);
                if (!transformResult.IsSuccess || transformResult.Value == null)
                {
                    result.ErrorMessages.AddRange(transformResult.ErrorMessages);
                    return;
                }

                transformResult.Value.SignatureSheet.FileName = fileName;

                // Step 3 - Save the results
                var signatureSheet = await SaveSingleSheetHierarchyAsync(
                    transformResult.Value,
                    templatePages,
                    upload);
                result.Value.Add(signatureSheet);
            });

            await AddDataLogAsync(upload.Id,
                $"Done processing {count} streams");
            if (result.Value.Count > 0)
            {
                result.Status = ResultStatus.Success;
            }
        }
        catch (Exception ex)
        {
            result.Status = ResultStatus.Failure;
            await AddDataLogAsync(upload.Id,
                $"Exception processing streams: {ex}");
        }

        return result;
    }


    [Conditional("ADD_DATA_LOGS")]
    public void AddDataLog(ref Task task, int signatureSheetUploadId, string message)
    {
        Func<Task> impl = async () =>
        {
            await _dataProcessingLogRepository.AddSignatureSheetOperationAsync(signatureSheetUploadId, message);
        };
        task = impl();
    }

    public async Task AddDataLogAsync(int signatureSheetUploadId, string message)
    {
        var task = Task.CompletedTask;
        AddDataLog(ref task, signatureSheetUploadId, message);
        await task;
    }

    public async Task<SignatureSheet> SaveSingleSheetHierarchyAsync(
        SignatureSheetResult results,
        IList<TemplatePage> templatePages,
        SignatureSheetUpload signatureSheetUpload
    )
    {
        _signatureSheetRepository.Add(results.SignatureSheet);

        foreach (var templatePage in templatePages)
        {
            var thisPageResults = results.PageResults[templatePage.PageNumber - 1];
            var thisPage = thisPageResults.Page;

            var signatureSheetPage = new SignatureSheetPage
            {
                SignatureSheet = results.SignatureSheet,
                PageNumber = thisPage.PageNumber,
                Height = thisPage.Height,
                Width = thisPage.Width,
            };

            _signatureSheetPageRepository.Add(signatureSheetPage);
            signatureSheetPage.FormLines.AddRange(thisPageResults.FormLines);
            await _signatureSheetFieldRepository.AddRangeAsync(thisPageResults.Fields);
            await _signatureSheetFieldRepository.SaveChangesAsync();

            if (templatePage.PageNumber != 1)
            {
                continue;
            }

            try
            {
                await SaveSignatureSheetCells(results);
                await AddDataLogAsync(signatureSheetUpload.Id, $"Saved SignatureSheetRows/Cells for sheet");
            }
            catch (Exception ex)
            {
                await AddDataLogAsync(signatureSheetUpload.Id,
                    $"Error saving SignatureSheetRows/Cells for sheet: {ex}");
            }
        }

        await _signatureSheetRepository.SaveChangesAsync();
        return results.SignatureSheet;
    }

    private async Task SaveSignatureSheetCells(
        SignatureSheetResult results)
    {
        var signatureSheetTable = results.SignatureTable;
        _signatureSheetTableRepository.Add(signatureSheetTable);
        await _signatureSheetTableRepository.SaveChangesAsync();
    }
}