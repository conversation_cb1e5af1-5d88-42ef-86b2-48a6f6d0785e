using DataInterface.ServiceInterfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Model.Geocoding.Google;
using Newtonsoft.Json;

namespace Service;

public class GoogleMapsPlacesService : IMapPlacesService
{
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger<GoogleMapsPlacesService> _logger;

    public GoogleMapsPlacesService(
        HttpClient httpClient,
        IConfiguration configuration,
        ILogger<GoogleMapsPlacesService> logger)
    {
        _httpClient = httpClient;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task<ServiceResult<MapPlacesResult>> GetPlaceTypeAsync(decimal latitude, decimal longitude)
    {
        var apiGoogleKey = _configuration.GetValue<string>("Keys:GoogleMapsApiKey");
        if (apiGoogleKey == null)
        {
            var message = "Google Maps API key is not configured.";
            _logger.LogError(message);
            return ServiceResult<MapPlacesResult>.Failed(message);
        }

        string requestUri =
            $"https://maps.googleapis.com/maps/api/place/nearbysearch/json?location={latitude},{longitude}&radius=10&key={apiGoogleKey}";
        string[] residentialKeywords = ["locality", "premise"];
        string[] commercialKeywords =
            { "cafe", "establishment", "food", "grocery_or_supermarket", "health", "pharmacy", "route", "store" };

        HttpResponseMessage response = await _httpClient.GetAsync(requestUri);
        if (!response.IsSuccessStatusCode)
        {
            var errorMessage = $"Get {requestUri} HTTP returned non-success status code: {response.StatusCode}";
            _logger.LogWarning(errorMessage);
            return ServiceResult<MapPlacesResult>.Failed(errorMessage);
        }

        string content = await response.Content.ReadAsStringAsync();
        var responseResult = JsonConvert.DeserializeObject<GoogleGeocodeResponse>(content);
        if (responseResult is not { Status: "OK" } || responseResult.Results == null)
        {
            var errorMessage = $"Google Place API error: {content}";
            _logger.LogWarning(errorMessage);
            return ServiceResult<MapPlacesResult>.Failed(errorMessage);
        }

        var types = responseResult.Results
            .SelectMany(result => result.Types).Distinct().ToList();
        var filteredCommercialTypes = types
            .Where(type => commercialKeywords.Any(type.Contains))
            .ToList();

        var mapPlacesResult = new MapPlacesResult
        {
            Categories = types,
            IsResidential = !(filteredCommercialTypes.Count > 0)
        };

        return ServiceResult<MapPlacesResult>.Succeeded(mapPlacesResult, $"({latitude},{longitude}) classified as {string.Join(",",types)}");
    }
}