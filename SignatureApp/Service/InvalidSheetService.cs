using DataInterface.RepositoryInterfaces;
using Model.SignatureSheets;

namespace Service;

public class InvalidSheetService
{
    private readonly IInvalidSheetRepository _invalidSheetRepository;
    private readonly ISignatureSheetRepository _signatureSheetRepository;

    public InvalidSheetService(
        IInvalidSheetRepository invalidSheetRepository,
        ISignatureSheetRepository signatureSheetRepository
        )
    {
        _invalidSheetRepository = invalidSheetRepository;
        _signatureSheetRepository = signatureSheetRepository;
    }

    public async Task AddInvalidSheetAsync(SignatureSheetUpload signatureSheetUpload, string filePath, int sheetNumber)
    {
        var invalidSheet = new InvalidSheet
        {
            MatterId = signatureSheetUpload.MatterId,
            TemplateId = signatureSheetUpload.TemplateId,
            SheetNumber = sheetNumber,
            Filename = filePath,
            SignatureSheetUploadId = signatureSheetUpload.Id,
        };
        _invalidSheetRepository.Add(invalidSheet);
        await _invalidSheetRepository.SaveChangesAsync();
    }

    public async Task MarkInvalidAsValid(InvalidSheet invalidSheet, SignatureSheet validSheet, string newFilename)
    {
        validSheet.FileName = newFilename;
        _signatureSheetRepository.SetModified(validSheet);
        invalidSheet.Status = InvalidSheetStatus.MarkedAsValid;
        _invalidSheetRepository.SetModified(invalidSheet);
        await _invalidSheetRepository.SaveChangesAsync();
    }

    public async Task AssignInvalidSheetAsync(InvalidSheet invalidSheet, string userEmail)
    {
        if (invalidSheet.Status == InvalidSheetStatus.Invalid
            || invalidSheet.AssignedTo != userEmail)
        {
            invalidSheet.AssignedTo = userEmail;
            invalidSheet.AssignmentDate = DateTime.UtcNow;
            invalidSheet.Status = InvalidSheetStatus.Assigned;
            _invalidSheetRepository.SetModified(invalidSheet);
            await _invalidSheetRepository.SaveChangesAsync();
        }
    }
}