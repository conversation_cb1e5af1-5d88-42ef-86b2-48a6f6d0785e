using System.Text.Json;
using AutoMapper;
using DataInterface.RepositoryInterfaces;
using DataInterface.ServiceInterfaces;
using Model.ExternalDataSources;
using Model.Geocoding;
using Model.SignatureSheets;
using Model.Templates;

namespace Service;

public class SignatoryService
{
    private readonly IGeocodingService _geocodingService;
    private readonly IMapper _mapper;
    private readonly ISignatoryRepository _signatoryRepository;
    private readonly ITemplateSignatureColumnRepository _templateSignatureColumnRepository;

    public SignatoryService(
        IGeocodingService geocodingService,
        IMapper mapper,
        ISignatoryRepository signatoryRepository,
        ITemplateSignatureColumnRepository templateSignatureColumnRepository)
    {
        _geocodingService = geocodingService;
        _mapper = mapper;
        _signatoryRepository = signatoryRepository;
        _templateSignatureColumnRepository = templateSignatureColumnRepository;
    }

    public ServiceResult<Signatory> GetSignatoryFromRow(
        IList<TemplateSignatureColumn> columns,
        SignatureSheetRow row,
        bool returnFullAddress = false)
    {
        var signatory = new Signatory();
        var nameColumns = columns.Where(c => c.IsName == true).ToList();
        var addressColumns = columns.Where(c => c.IsAddress == true).ToList();
        var dateColumn = columns.FirstOrDefault(c => c.IsDate == true);
        var result = SetSignatoryNameFields(nameColumns, row, signatory);
        if (!result.IsSuccess)
        {
            return result;
        }

        signatory = result.Value;
        if (signatory == null)
        {
            return ServiceResult<Signatory>.Failed("Signatory is null");
        }

        result = SetSignatoryAddressFields(addressColumns, row, signatory, returnFullAddress);
        if (!result.IsSuccess)
        {
            return result;
        }

        signatory = result.Value;
        if (signatory == null)
        {
            return ServiceResult<Signatory>.Failed("Signatory is null");
        }

        result = SetSignatoryDateField(dateColumn, row, signatory);

        return result;
    }

    public async Task<ServiceResult<Signatory>> UpdateSignatoryForRowAsync(SignatureSheetRow row,
        IList<TemplateSignatureColumn>? columns = null)
    {
        if (columns == null)
        {
            columns = await _templateSignatureColumnRepository.GetAllByTemplateIdAsync(row.SignatureSheet.TemplateId);
        }

        var result = GetSignatoryFromRow(columns, row, returnFullAddress: false);
        if (!result.IsSuccess)
        {
            return result;
        }

        if (result.Value == null)
        {
            return ServiceResult<Signatory>.Failed("Signatory is null");
        }

        var signatory = result.Value;
        if (signatory?.StreetName == null)
        {
            result = ServiceResult<Signatory>.Failed("Street name is null");
            return result;
        }

        if (row.SignatoryId == null)
        {
            await AddNewSignatoryToRowAsync(signatory, row); // calls geocode
        }
        else
        {
            var signatoryFromDb = await _signatoryRepository.GetByIdAsync(row.SignatoryId.Value);
            if (signatoryFromDb == null)
            {
                await AddNewSignatoryToRowAsync(signatory, row); // calls geocode
            }
            else
            {
                await UpdateSignatoryInRowAsync(row, signatory, signatoryFromDb); // calls geocode
            }
        }

        await _signatoryRepository.SaveChangesAsync();
        return ServiceResult<Signatory>.Succeeded(signatory);
    }

    private async Task UpdateSignatoryInRowAsync(SignatureSheetRow row, Signatory signatory, Signatory signatoryFromDb)
    {
        int signatoryId = signatoryFromDb.Id;
        var addressInputFromDb = GetAddressInputFromSignatory(signatoryFromDb);
        var addressFromDb = AddressParsingService.CreateAddressLines(addressInputFromDb);
        var newAddressInput = GetAddressInputFromSignatory(signatory);
        var newAddress = AddressParsingService.CreateAddressLines(newAddressInput);
        if (addressFromDb.newLine1 != newAddress.newLine1
            || addressFromDb.newLine2 != newAddress.newLine2
            || signatoryFromDb.Latitude == null || signatoryFromDb.Longitude == null)
        {
            await GeocodeSignatoryAsync(signatory, newAddressInput);
        }

        // overwrite all the properties on the database object
        _mapper.Map(signatory, signatoryFromDb);
        signatoryFromDb.Id = signatoryId; // Make sure we don't overwrite that one
        _signatoryRepository.SetModified(signatoryFromDb);
        row.SignatoryId = signatoryFromDb.Id;
    }

    private async Task AddNewSignatoryToRowAsync(Signatory signatory, SignatureSheetRow row)
    {
        var addressInput = GetAddressInputFromSignatory(signatory);
        await GeocodeSignatoryAsync(signatory, addressInput);

        _signatoryRepository.Add(signatory);
        await _signatoryRepository.SaveChangesAsync();
        row.SignatoryId = signatory.Id;
    }

    public async Task GeocodeSignatoryAsync(Signatory signatory, AddressInput addressInput)
    {
        var result = await _geocodingService.GeocodeAsync(addressInput);
        if (result.IsSuccess && result.Value?.ExactMatch != null)
        {
            signatory.Latitude = result.Value.ExactMatch.Latitude;
            signatory.Longitude = result.Value.ExactMatch.Longitude;
            return;
        }
        if (result.Value != null && result.Value.PossibleMatches.Any())
        {
            var possibleMatches = result.Value.PossibleMatches;
            var match = possibleMatches.FirstOrDefault(gc => gc.NewParsedAddress != null
                && gc.NewParsedAddress.PostalCode == addressInput.PostalCode
                && gc.NewParsedAddress.City == addressInput.City);
            if (match != null)
            {
                signatory.Latitude = match.Latitude;
                signatory.Longitude = match.Longitude;
                return;
            }
        }
        signatory.GeocodeErrors = string.Join("\n", result.ErrorMessages);
    }

    public AddressInput GetAddressInputFromSignatory(Signatory signatory)
    {
        var addressLine1 = AddressParsingService.CreateAddressLine1(signatory);
        var addressInput = new AddressInput
        {
            AddressLine = addressLine1,
            City = signatory.City,
            PostalCode = signatory.PostalCode,
        };
        if (signatory.UsStateId != 3)
        {
            addressInput.State = UsStates.FromId(signatory.UsStateId)?.Abbreviation ?? "AZ";
        }

        return addressInput;
    }

    private ServiceResult<Signatory> SetSignatoryDateField(TemplateSignatureColumn? dateColumn, SignatureSheetRow row,
        Signatory signatory)
    {
        if (dateColumn == null)
        {
            return ServiceResult<Signatory>.Failed("Date column is null");
        }

        var dateCell = row.Cells.FirstOrDefault(cell => cell.ColumnIndex == dateColumn.ColumnIndex);
        if (dateCell == null)
        {
            return ServiceResult<Signatory>.Failed("Date cell is null");
        }

        if (string.IsNullOrWhiteSpace(dateCell.Value))
        {
            return ServiceResult<Signatory>.Failed("Date cell value is null or whitespace");
        }

        if (DateTime.TryParse(dateCell.Value, out var dateTime))
        {
            signatory.DateSigned = dateTime;
        }

        return ServiceResult<Signatory>.Succeeded(signatory);
    }

    private ServiceResult<Signatory> SetSignatoryAddressFields(
        IList<TemplateSignatureColumn> addressColumns,
        SignatureSheetRow row,
        Signatory signatory,
        bool returnFullAddress = false)
    {
        var addressInputResult = RowExtractionService.GetAddressInputFromRow(addressColumns, row);
        if (!addressInputResult.IsSuccess || addressInputResult.Value == null)
        {
            return ServiceResult<Signatory>.Failed([.. addressInputResult.ErrorMessages]);
        }

        var input = addressInputResult.Value;
        var (addressLine1, addressLine2) = AddressParsingService.CreateAddressLines(input);
        string fullAddress = addressLine1 + addressLine2;
        if (returnFullAddress)
        {
            signatory.FullAddress = fullAddress;
            return ServiceResult<Signatory>.Succeeded(signatory);
        }

        if (input?.AddressLine == null)
        {
            return ServiceResult<Signatory>.Failed("Address line is null");
        }

        var usState = UsStates.ParseState(input.State);
        if (usState == null)
        {
            return ServiceResult<Signatory>.Failed("State is null");
        }

        var fullyParsedAddress = AddressParsingService.ParseAddressLine1(input.AddressLine);
        signatory.StreetNumber = fullyParsedAddress.StreetNumber;
        signatory.Direction = fullyParsedAddress.Direction;
        signatory.StreetName = fullyParsedAddress.StreetName;
        signatory.StreetType = fullyParsedAddress.StreetType;
        signatory.City = GetSignatoryCity(input.City);
        signatory.UsStateId = usState.Id;
        signatory.PostalCode = input.PostalCode;

        if (signatory.StreetNumber == null || signatory.StreetName == null)
        {
            return ServiceResult<Signatory>.Failed("Street number, street name or street type is null",
                fullAddress,
                JsonSerializer.Serialize(input),
                JsonSerializer.Serialize(fullyParsedAddress));
        }

        return ServiceResult<Signatory>.Succeeded(signatory);
    }

    private string? GetSignatoryCity(string? city)
    {
        return city == null
            ? null
            : AddressParsingService.ArizonaCityAbbreviations.GetValueOrDefault(city.ToUpperInvariant(), city);
    }

    private static ServiceResult<Signatory> SetSignatoryNameFields(IList<TemplateSignatureColumn> nameColumns,
        SignatureSheetRow row,
        Signatory signatory)
    {
        var nameColumnIndices = nameColumns.Select(col => col.ColumnIndex);

        var nameCells = row.Cells
            .Where(cell => nameColumnIndices.Contains(cell.ColumnIndex))
            //&& (cell.TemplateSignatureColumn.CanBeInvalid == true)
            .ToList();
        if (nameCells.Count == 1)
        {
            var fullName = nameCells[0].Value ?? "";
            if (fullName.Contains(","))
            {
                var nameParts = fullName.Split(',');
                if (nameParts.Length < 2)
                {
                    return ServiceResult<Signatory>.Failed(
                        $"Comma separated name is not in the correct format {fullName}");
                }

                signatory.FirstName = nameParts[1].Trim();
                signatory.LastName = nameParts[0].Trim();
            }
            else
            {
                var nameParts = fullName.Split(' ');
                if (nameParts.Length < 2)
                {
                    return ServiceResult<Signatory>.Failed(
                        $"Space separated name is not in the correct format {fullName}");
                }

                signatory.FirstName = nameParts[0].Trim();
                signatory.LastName = nameParts[1].Trim();
            }
        }
        else if (nameCells.Count > 1)
        {
            // find which nameColumns that contains "first"
            var firstNameColumn =
                nameColumns.FirstOrDefault(c =>
                    c.Name != null && c.Name.Contains("first", StringComparison.CurrentCultureIgnoreCase));
            var firstNameCell = nameCells.FirstOrDefault(c => c.ColumnIndex == firstNameColumn?.ColumnIndex);
            var lastNameColumn =
                nameColumns.FirstOrDefault(c =>
                    c.Name != null && c.Name.Contains("last", StringComparison.CurrentCultureIgnoreCase));
            var lastNameCell = nameCells.FirstOrDefault(c => c.ColumnIndex == lastNameColumn?.ColumnIndex);

            if (firstNameCell?.Value == null)
            {
                return ServiceResult<Signatory>.Failed("First name cell value is null");
            }

            if (lastNameCell?.Value == null)
            {
                return ServiceResult<Signatory>.Failed("Last name cell value is null");
            }

            signatory.FirstName = firstNameCell.Value.Trim();
            signatory.LastName = lastNameCell.Value.Trim();
        }

        return ServiceResult<Signatory>.Succeeded(signatory);
    }
}