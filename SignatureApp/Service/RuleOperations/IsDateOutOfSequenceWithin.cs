﻿using DataInterface.ServiceInterfaces;
using Model.SignatureSheets;
using NetTopologySuite.Geometries;
using NetTopologySuite.IO;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Service.ServiceModels;

namespace Service.RuleOperations;

public class IsDateOutOfSequenceWithin : IRule, IRuleOperation
{
    // A rule returns true if there is a deficiency
    public ServiceResult<bool> Evaluate(RuleContext context, JToken lhsValue, JToken? rhsValue)
    {
        var cell = lhsValue.ToObject<SignatureSheetCell>();
        if (cell == null || !cell.IsReviewed || cell.Validity != Validity.Valid)
        {
            return ServiceResult<bool>.Succeeded(false); // can't test a cell without a valid value
        }

        if (rhsValue == null)
        {
            return ServiceResult<bool>.Succeeded(false); // if there is no value, we can't determine if it is a deficiency
        }
        var jarray = (JArray)rhsValue;
        var column = jarray.ToObject<IList<SignatureSheetCell>>();
        if (column == null)
        {
            return ServiceResult<bool>.Succeeded(false); // if there is no value, we can't determine if it is a deficiency
        }
        var columnIds = column.Select(c => c.Id).ToList();

        var cellIndex = columnIds.IndexOf(cell.Id);
        if (cellIndex == 0
            || !cell.IsReviewed
            || cell.Validity != Validity.Valid)
        {  // First row can NOT be out of sequence, neither can rows not reviewed, or illegible dates
            return ServiceResult<bool>.Succeeded(false);
        }
        var isCellDate = DateTime.TryParse(cell.Value, out var cellDate);
        if (!isCellDate)
        {  // If we can't parse the date, that is a deficiency
            return ServiceResult<bool>.Succeeded(true, $"Unable to parse the date {cell.Value}");
        }

        var maxDate = column
            .Where((c, i) => c.IsReviewed && c.Validity == Validity.Valid && i < cellIndex)
            .Select(c =>
            {
                if (DateTime.TryParse(c.Value, out var date))
                    return date;
                return DateTime.MinValue;
            }).Max();
        return ServiceResult<bool>.Succeeded(cellDate < maxDate, $"Cell date {cellDate.ToShortDateString()} is less than {maxDate.ToShortDateString()}.");
    }
}

