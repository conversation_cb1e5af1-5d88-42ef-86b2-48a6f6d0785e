using System.Text;
using DataInterface.RepositoryInterfaces;
using DataInterface.ServiceInterfaces;
using Model.Rules;
using Model.SignatureSheets;
using Newtonsoft.Json.Linq;
using Service.ServiceModels;

namespace Service.RuleOperations;

public class AreDuplicateVoters : IRule, IRuleOperationAsync, ISelfManaged
{
    private readonly ISignatureSheetRowRepository _signatureSheetRowRepository;
    private readonly DeficiencyService _deficiencyService;

    public AreDuplicateVoters(
        ISignatureSheetRowRepository signatureSheetRowRepository,
        DeficiencyService deficiencyService)
    {
        _signatureSheetRowRepository = signatureSheetRowRepository;
        _deficiencyService = deficiencyService;
    }

    // A rule returns true if there is a deficiency
    public async Task<ServiceResult<bool>> EvaluateAsync(RuleContext context, JToken lhsValue, JToken? rhsValue)
    {
        if (context.Rule == null)
        {
            return ServiceResult<bool>.Succeeded(false);
        }
        var matterId = context.Matter.Id;
        var registeredDuplicates = await _signatureSheetRowRepository.GetDuplicateRegisteredVotersByMatterIdAsync(matterId);
        foreach (var duplicateList in registeredDuplicates)
        {
            await CreateDeficienciesFromRegisteredDuplicates(context, duplicateList, matterId);
        }

        var textualDuplicates = await _signatureSheetRowRepository.GetTextualDuplicatesByMatterIdAsync(matterId);
        foreach (var duplicateList in textualDuplicates)
        {
            await SiftThroughTextualDuplicates(context, duplicateList, matterId);
        }
        // We have handled our own deficiencies, no need to create another one
        return ServiceResult<bool>.Succeeded(false);
    }

    private async Task SiftThroughTextualDuplicates(RuleContext context, List<SignatureSheetRow> firstInitialLastNameDuplicates, int matterId)
    {
        // this get passed all duplicates with the same first initial and last name

        // Separate the rows with only a first initial instead of a full first name
        var initialOnlyRows = firstInitialLastNameDuplicates
            .Where(x => IsFirstNameInitial(x))
            .ToList();

        // We are left with ones that have at least a full first name (they might also have an initial).
        // We group THOSE full name signatories by full first name (it is assumed that they have the same last name).
        var groupedRows = firstInitialLastNameDuplicates.Except(initialOnlyRows)
            .GroupBy(x => x.Signatory!.FirstName);

        foreach (var duplicateRows in groupedRows)
        {
            // The ones with only initials could be duplicates of any the full name signatories.
            var potentialDuplicates = duplicateRows.Union(initialOnlyRows).ToList();
            await CreateDeficienciesFromSignatoryDuplicates(context, potentialDuplicates, matterId);
        }
    }

    private static bool IsFirstNameInitial(SignatureSheetRow row)
    {
        return row.Signatory?.FirstName?.Length == 1
               || (row.Signatory?.FirstName?.Length == 2 && row.Signatory?.FirstName[1] == '.');
    }

    private async Task CreateDeficienciesFromRegisteredDuplicates(RuleContext context, List<SignatureSheetRow> duplicateList, int matterId)
    {
        var orderedRows = duplicateList
            .OrderBy(x => x.Signatory?.DateSigned)
            .ThenBy(x => x.SignatureSheet.SheetNumber * 100 + x.RowNumber); // row number is two digits
        var earliestRow = orderedRows.First();
        var notes = "Registered duplicates on: " + CreateStringForListOfDuplicates(duplicateList);
        foreach (var duplicateRow in duplicateList)
        {
            if (duplicateRow == earliestRow)
            {
                continue;
            }

            await CreateDeficiencyForDuplicate(context, matterId, earliestRow, duplicateRow, notes, notes);
        }
    }

    private static string CreateStringForListOfDuplicates(List<SignatureSheetRow> duplicateList)
    {
        var builder = new StringBuilder();
        foreach (var row in duplicateList)
        {
            if (builder.Length > 0) { builder.Append(", "); }
            builder.Append(CreateLineForRow(row));
        }
        return builder.ToString();
    }

    private static string CreateLineForRow(SignatureSheetRow row)
    {
        ArgumentNullException.ThrowIfNull(row.Signatory);
        return $"Sheet {row.SignatureSheet.SheetNumber} Row {row.RowNumber} Date {row.Signatory.DateSigned:d}";
    }

    internal async Task CreateDeficienciesFromSignatoryDuplicates(RuleContext context, List<SignatureSheetRow> duplicateList, int matterId)
    {
        var orderedRows = duplicateList
            .OrderBy(x => x.Signatory?.DateSigned)
            .ThenBy(x => x.SignatureSheet.SheetNumber * 100 + x.RowNumber) // row number is two digits
            .ToList();

        var notesForRow = new DefaultDictionary<SignatureSheetRow, StringBuilder>();
        var deficiencies = new HashSet<(SignatureSheetRow earliestRow, SignatureSheetRow duplicateRow)>();

        for (int outerIndex = 0; outerIndex < orderedRows.Count - 1; outerIndex++)
        {
            var earliestRow = orderedRows[outerIndex];
            var isEarliestRowInitial = IsFirstNameInitial(earliestRow);
            ArgumentNullException.ThrowIfNull(earliestRow.Signatory);

            var earliestStreetAddress = AddressParsingService.CreateAddressLine1(earliestRow.Signatory);
            for (int innerIndex = outerIndex+1; innerIndex < orderedRows.Count; innerIndex++)
            {
                var duplicateRow = orderedRows[innerIndex];
                var isDuplicateRowInitial = IsFirstNameInitial(duplicateRow);

                ArgumentNullException.ThrowIfNull(duplicateRow.Signatory);
                var duplicateStreetAddress = AddressParsingService.CreateAddressLine1(duplicateRow.Signatory);
                if (IsFullAddressMatch(earliestStreetAddress, duplicateStreetAddress, earliestRow, duplicateRow))
                {
                    var needsReview = isDuplicateRowInitial || isEarliestRowInitial;
                    var message = $"{(needsReview ? "Soft" : "Hard")} match on Full Address: ";
                    notesForRow[earliestRow].AppendLine(message + CreateLineForRow(duplicateRow));
                    notesForRow[duplicateRow].AppendLine(message + CreateLineForRow(earliestRow));
                    deficiencies.Add((earliestRow, duplicateRow));
                }
                else
                {
                    // These are all soft matches
                    if (string.Compare(earliestStreetAddress, duplicateStreetAddress,
                            StringComparison.CurrentCultureIgnoreCase) == 0)
                    {
                        var message = "Match on Street Address: ";
                        notesForRow[earliestRow].AppendLine(message + CreateLineForRow(duplicateRow));
                        notesForRow[duplicateRow].AppendLine(message + CreateLineForRow(earliestRow));
                        deficiencies.Add((earliestRow, duplicateRow));
                    }
                    else if (string.Compare(earliestRow.Signatory.StreetName, duplicateRow.Signatory.StreetName,
                                 StringComparison.CurrentCultureIgnoreCase) == 0)
                    {
                        var message = "Match on Street Name: ";
                        notesForRow[earliestRow].AppendLine(message + CreateLineForRow(duplicateRow));
                        notesForRow[duplicateRow].AppendLine(message + CreateLineForRow(earliestRow));
                        deficiencies.Add((earliestRow, duplicateRow));
                    }

                    // Match on city is treated as soft match, which should allow law admin to resolve.
                    if (string.Compare(earliestRow.Signatory.City, duplicateRow.Signatory.City,
                            StringComparison.CurrentCultureIgnoreCase) == 0)
                    {
                        var message = "Match on City: ";
                        notesForRow[earliestRow].AppendLine(message + CreateLineForRow(duplicateRow));
                        notesForRow[duplicateRow].AppendLine(message + CreateLineForRow(earliestRow));
                        deficiencies.Add((earliestRow, duplicateRow));
                    }

                    // Match on zip is treated as soft match, which should allow law admin to resolve.
                    if (string.Compare(earliestRow.Signatory.PostalCode, duplicateRow.Signatory.PostalCode,
                            StringComparison.CurrentCultureIgnoreCase) == 0)
                    {
                        var message = "Match on ZipCode: ";
                        notesForRow[earliestRow].AppendLine(message + CreateLineForRow(duplicateRow));
                        notesForRow[duplicateRow].AppendLine(message + CreateLineForRow(earliestRow));
                        deficiencies.Add((earliestRow, duplicateRow));
                    }
                }
            }
        }

        foreach (var (earliestRow, duplicateRow) in deficiencies)
        {
            var earliestRowNotes = notesForRow[earliestRow].ToString();
            var duplicateRowNotes = notesForRow[duplicateRow].ToString();
            await CreateDeficiencyForDuplicate(context, matterId, earliestRow, duplicateRow, earliestRowNotes, duplicateRowNotes);
        }
    }

    private static bool IsFullAddressMatch(string earliestStreetAddress, string duplicateStreetAddress, SignatureSheetRow earliestRow, SignatureSheetRow duplicateRow)
    {
        ArgumentNullException.ThrowIfNull(earliestRow.Signatory);
        ArgumentNullException.ThrowIfNull(duplicateRow.Signatory);
        return string.Compare(earliestStreetAddress, duplicateStreetAddress,
                   StringComparison.CurrentCultureIgnoreCase) == 0
               && string.Compare(earliestRow.Signatory.City, duplicateRow.Signatory.City,
                   StringComparison.CurrentCultureIgnoreCase) == 0
               && string.Compare(earliestRow.Signatory.PostalCode, duplicateRow.Signatory.PostalCode,
                   StringComparison.CurrentCultureIgnoreCase) == 0;
    }

    private async Task CreateDeficiencyForDuplicate(RuleContext context, int matterId,
        SignatureSheetRow earliestRow,
        SignatureSheetRow duplicateRow,
        string earliestRowNotes,
        string duplicateRowNotes)
    {
        ArgumentNullException.ThrowIfNull(earliestRow.Signatory);
        ArgumentNullException.ThrowIfNull(duplicateRow.Signatory);
        if (context.Rule == null)
        {
            throw new Exception("Rule not in context");
        }

        bool deficiencyAlreadyExists;
        var earliestDate = earliestRow.Signatory.DateSigned;
        var thisDate = duplicateRow.Signatory.DateSigned;

        if (earliestDate == thisDate)
        {
            earliestRow.Validity = Validity.Invalid;
            deficiencyAlreadyExists = await _deficiencyService.DoesDeficiencyExistForRowAsync(earliestRow,
                context.Rule.Id, matterId);
            if (!deficiencyAlreadyExists)
            {
                var deficiency = await _deficiencyService.CreateRuleDeficiencyAsync(earliestRow.Id, RecordIdType.SignatureSheetRow,
                    context.Rule.Id, matterId, earliestRow.SignatureSheetId, note: earliestRowNotes, needsReview: true);
                deficiency.OtherRecordId = duplicateRow.Id;
                deficiency.OtherRecordIdType = RecordIdType.SignatureSheetRow;
                await _deficiencyService.AddRuleDeficiencyAsync(earliestRow, RecordIdType.SignatureSheetRow,
                    deficiency);
            }
        }
        duplicateRow.Validity = Validity.Invalid;
        deficiencyAlreadyExists = await _deficiencyService.DoesDeficiencyExistForRowAsync(duplicateRow,
            context.Rule.Id, matterId);
        if (!deficiencyAlreadyExists)
        {
            var deficiency = await _deficiencyService.CreateRuleDeficiencyAsync(duplicateRow.Id, RecordIdType.SignatureSheetRow,
                context.Rule.Id, matterId, duplicateRow.SignatureSheetId, note: duplicateRowNotes, needsReview: true);
            deficiency.OtherRecordId = earliestRow.Id;
            deficiency.OtherRecordIdType = RecordIdType.SignatureSheetRow;
            await _deficiencyService.AddRuleDeficiencyAsync(duplicateRow, RecordIdType.SignatureSheetRow,
                deficiency);
        }
    }
}
