using DataInterface.RepositoryInterfaces;
using DataInterface.ServiceInterfaces;
using Model.Rules;
using Newtonsoft.Json.Linq;
using Service.ServiceModels;

namespace Service.RuleOperations;

public class AreCirculatorsInvalid : IRule, IRuleOperationAsync, ISelfManaged
{
    private readonly DeficiencyService _deficiencyService;
    private readonly ICirculatorRepository _circulatorRepository;
    private readonly IExternalDataSourceRepository _externalDataSourceRepository;
    private readonly IInvalidCirculatorRepository _invalidCirculatorRepository;

    public AreCirculatorsInvalid(DeficiencyService deficiencyService,
        ICirculatorRepository circulatorRepository,
        IExternalDataSourceRepository externalDataSourceRepository,
        IInvalidCirculatorRepository invalidCirculatorRepository)
    {
        _deficiencyService = deficiencyService;
        _circulatorRepository = circulatorRepository;
        _externalDataSourceRepository = externalDataSourceRepository;
        _invalidCirculatorRepository = invalidCirculatorRepository;
    }

    public async Task<ServiceResult<bool>> EvaluateAsync(RuleContext context, JToken lhsValue, JToken? rhsValue)
    {
        if (context.Rule == null)
        {
            return ServiceResult<bool>.Succeeded(false);
        }

        var ruleId = context.Rule.Id;
        var matterId = context.Matter.Id;
        // find external data source by rule id and matter id
        var externalDataSource = await _externalDataSourceRepository.GetByMatterIdAndRuleIdAsync(matterId, ruleId);
        if (externalDataSource == null)
        {
            return ServiceResult<bool>.Succeeded(false);
        }
        var invalidCirculators =
            await _invalidCirculatorRepository.GetByExternalDataSourceIdAsync(externalDataSource.Id);
        var circulators = await _circulatorRepository.GetAllByMatterIdAsync(matterId);
        var invalidCirculatorsInMatter =
            circulators.Where(c => invalidCirculators.Any(ic => ic.CirculatorName == c.Name)).ToList();
        // var sheetsWithInvalidCirculators =
        //     invalidCirculatorsInMatter.Select(ic => ic.SignatureSheetId).Distinct().ToList();

        foreach (var circulator in invalidCirculatorsInMatter)
        {
            foreach (var signatureSheet in circulator.SignatureSheets)
            {
                var deficiency = await _deficiencyService.CreateRuleDeficiencyAsync(signatureSheet.Id, RecordIdType.SignatureSheet,
                    context.Rule.Id, matterId, context.Sheet?.Id, note: $"Circulator {circulator.Name}",
                    needsReview: context.Rule.NeedsReview);
                await _deficiencyService.AddRuleDeficiencyAsync(signatureSheet, RecordIdType.SignatureSheet,
                    deficiency);
            }
        }
        // We have handled our own deficiencies, no need to create another one
        return ServiceResult<bool>.Succeeded(false);
    }
}