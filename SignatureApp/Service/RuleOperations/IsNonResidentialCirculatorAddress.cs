using DataInterface.ServiceInterfaces;
using Model.SignatureSheets;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Service.ServiceModels;

namespace Service.RuleOperations;

public class IsNonResidentialCirculatorAddress : IRule, IRuleOperationAsync
{
    private readonly IGeocodingService _geocodingService;
    private readonly IMapPlacesService _mapPlacesService;

    public IsNonResidentialCirculatorAddress(
        IGeocodingService geocodingService,
        IMapPlacesService mapPlacesService)
    {
        _geocodingService = geocodingService;
        _mapPlacesService = mapPlacesService;
    }

    // A rule returns true if there is a deficiency
    public async Task<ServiceResult<bool>> EvaluateAsync(RuleContext context, JToken lhsValue, JToken? rhsValue)
    {
        if (rhsValue == null)
        {
            return ServiceResult<bool>.Succeeded(false);
        }
        var leftField = JsonConvert.DeserializeObject<SignatureSheetField>(lhsValue.ToString());
        if (leftField == null)
        {
            return ServiceResult<bool>.Succeeded(true, "Left field is NULL");
        }

        var rightField = JsonConvert.DeserializeObject<SignatureSheetField>(rhsValue.ToString());
        if (rightField == null)
        {
            return ServiceResult<bool>.Succeeded(true, "Right field is NULL");
        }

        if (!leftField.IsReviewed)
        {
            return ServiceResult<bool>.Succeeded(false);
        }
        if (!rightField.IsReviewed)
        {
            return ServiceResult<bool>.Succeeded(false);
        }

        var addressLine1 = leftField.Value;
        var addressLine2 = rightField.Value;
        if (string.IsNullOrWhiteSpace(addressLine1) || string.IsNullOrWhiteSpace(addressLine2))
        {
            return ServiceResult<bool>.Succeeded(false); // if either are blank the AreAddressLinesIncomplete should pick it up
        }

        var addressInput = AddressParsingService.ParseAddressLine2(addressLine2);
        addressInput.AddressLine = addressLine1;

        // This could be avoided by storing the geocoded location in the circulator, just like we do for signatory
        var geocodeResult = await _geocodingService.GeocodeAsync(addressInput);
        if (!geocodeResult.IsSuccess || geocodeResult.Value?.ExactMatch == null)
        {
            return ServiceResult<bool>.Succeeded(true, string.Join("\n", geocodeResult.ErrorMessages.ToArray()));
        }

        var exactMatch = geocodeResult.Value.ExactMatch;
        var addressTypeResult = await _mapPlacesService.GetPlaceTypeAsync(exactMatch.Latitude, exactMatch.Longitude);
        if (!addressTypeResult.IsSuccess || addressTypeResult.Value == null)
        {
            return ServiceResult<bool>.Succeeded(true, string.Join("\n", addressTypeResult.ErrorMessages.ToArray()));
        }

        bool isNonResidential = !addressTypeResult.Value.IsResidential;
        var result = ServiceResult<bool>.Succeeded(isNonResidential,
            isNonResidential ? $"Address {addressLine1} {addressLine2} is qualified as {string.Join(", ", addressTypeResult.Value.Categories)}" : null);
        return result;
    }
}