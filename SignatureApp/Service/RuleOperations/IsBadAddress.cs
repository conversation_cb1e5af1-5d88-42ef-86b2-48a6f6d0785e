using DataInterface.ServiceInterfaces;
using Microsoft.Extensions.Logging;
using Model.ExternalDataSources;
using Newtonsoft.Json.Linq;
using Service.ServiceModels;

namespace Service.RuleOperations;

public class IsBadAddress : IRule, IRuleOperation
{
    private readonly ILogger<IsBadAddress> _logger;

    public IsBadAddress(BoundaryService boundaryService, ILogger<IsBadAddress> logger)
    {
        _logger = logger;
    }

    // A rule returns true if there is a deficiency
    public ServiceResult<bool> Evaluate(RuleContext context, JToken lhsValue, JToken? rhsValue)
    {
        var signatory = lhsValue.ToObject<Signatory>();
        if (signatory == null)
        {
            return ServiceResult<bool>.Succeeded(false);
        }

        if (signatory.Latitude == null || signatory.Longitude == null)
        {
            return ServiceResult<bool>.Succeeded(true, string.Join("\n", signatory.GeocodeErrors));
        }

        return ServiceResult<bool>.Succeeded(false);
    }
}