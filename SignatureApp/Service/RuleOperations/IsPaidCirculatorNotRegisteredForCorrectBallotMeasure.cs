using DataInterface.RepositoryInterfaces;
using DataInterface.ServiceInterfaces;
using Model.ExternalDataSources;
using Model.SignatureSheets;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Service.ServiceModels;

namespace Service.RuleOperations
{
    public class IsPaidCirculatorNotRegisteredForCorrectBallotMeasure : IRule, IRuleOperationAsync
    {
        private readonly IRegisteredCirculatorRepository _registeredCirculatorRepository;

        public IsPaidCirculatorNotRegisteredForCorrectBallotMeasure(IRegisteredCirculatorRepository registeredCirculatorRepository)
        {
            _registeredCirculatorRepository = registeredCirculatorRepository;
        }

        // A rule returns true if there is a deficiency
        public async Task<ServiceResult<bool>> EvaluateAsync(RuleContext context, JToken lhsValue, JToken? rhsValue)
        {
            var matterId = context.Matter.Id;
            if (rhsValue == null)
            {
                return ServiceResult<bool>.Succeeded(false);
            }

            var circulator = lhsValue.ToObject<Circulator>();
            if (circulator == null)
            {
                return ServiceResult<bool>.Succeeded(false);
            }

            var rightField = JsonConvert.DeserializeObject<SignatureSheetField>(rhsValue.ToString());
            if (rightField == null)
            {
                return ServiceResult<bool>.Succeeded(false);
            }

            if (!rightField.IsReviewed)
            {
                return ServiceResult<bool>.Succeeded(false);
            }

            var ballotMeasure = context.Matter.Variables.SingleOrDefault(v => v.Key == "InitiativeSerialNumber");
            if (ballotMeasure == null)
            {
                // if there is nothing to check against, there is no defect
                return ServiceResult<bool>.Succeeded(false);
            }

            var paidValue = rightField.Value;

            var isPaid = !string.IsNullOrWhiteSpace(paidValue);
            if (!isPaid) { return ServiceResult<bool>.Succeeded(false); }
            // this is now a paid circulator

            // if we have a circulatorId and it is in the DB,
            if (!string.IsNullOrEmpty(circulator.RegistrationId))
            {
                var criteria = new RegisteredCirculator { CirculatorId = circulator.RegistrationId };
                var circulators = await _registeredCirculatorRepository.SearchByCriteriaAsync(
                    matterId, criteria);
                if (circulators.Any())
                {
                    criteria.CandidateOrBallotMeasure = ballotMeasure.Value;
                    circulators = await _registeredCirculatorRepository.SearchByCriteriaAsync(
                        matterId, criteria);
                    if (!circulators.Any())
                    {
                        return ServiceResult<bool>.Succeeded(true,
                            $"Circulator ID {circulator.RegistrationId} not found with ballot measure {ballotMeasure.Value}");
                    }
                }
            }

            if (!string.IsNullOrEmpty(circulator.Name))
            {
                var criteria = new RegisteredCirculator { CirculatorName = circulator.Name };
                var circulators = await _registeredCirculatorRepository.SearchByCriteriaAsync(
                    matterId, criteria);
                if (circulators.Any())
                {
                    criteria.CandidateOrBallotMeasure = ballotMeasure.Value;
                    circulators = await _registeredCirculatorRepository.SearchByCriteriaAsync(
                        matterId, criteria);
                    if (!circulators.Any())
                    {
                        return ServiceResult<bool>.Succeeded(true,
                            $"Circulator Name {circulator.Name} not found with ballot measure {ballotMeasure.Value}");
                    }
                }
            }

            return ServiceResult<bool>.Succeeded(false);
        }
    }
}