﻿using DataInterface.RepositoryInterfaces;
using DataInterface.ServiceInterfaces;
using Model.Rules;
using Model.SignatureSheets;
using Newtonsoft.Json.Linq;
using Service.ServiceModels;

namespace Service.RuleOperations;

public class AreDuplicateCrossCandidateVoters : IRule, IRuleOperationAsync, ISelfManaged
{
    private readonly ISignatureSheetRowRepository _signatureSheetRowRepository;
    private readonly DeficiencyService _deficiencyService;

    public AreDuplicateCrossCandidateVoters(
        ISignatureSheetRowRepository signatureSheetRowRepository,
        DeficiencyService deficiencyService)
    {
        _signatureSheetRowRepository = signatureSheetRowRepository;
        _deficiencyService = deficiencyService;
    }

    // A rule returns true if there is a deficiency
    public async Task<ServiceResult<bool>> EvaluateAsync(RuleContext context, JToken lhsValue, JToken? rhsValue)
    {
        if (context.Rule == null || context.Sheet == null)
        {
            return ServiceResult<bool>.Succeeded(false);
        }
        var matterId = context.Matter.Id;
        // TODO: we need the office parameter to be passed in.  Maybe all parameters can be on the context?
        var office = "Legislative District 1";
        var duplicates = await _signatureSheetRowRepository.GetDuplicateRegisteredVotersByOfficeAsync(office);
        foreach (var duplicateList in duplicates)
        {
            var earliestRow = duplicateList.MinBy(x => x.SignatureSheet.SheetNumber * 100 + x.RowNumber); // row number is two digits
            var notes = string.Join(". ", duplicateList.Select(x => $"Duplicate on Sheet number {x.SignatureSheet.SheetNumber}, Row number {x.RowNumber}"));
            foreach (var duplicateRow in duplicateList)
            {
                if (duplicateRow == earliestRow || duplicateRow.Validity == Validity.Strikethrough)
                {
                    continue;
                }

                var dateColumn = duplicateRow.TemplateSignatureTable.Columns.FirstOrDefault(x => x.Name != null
                    && x.Name.Contains("Date", StringComparison.InvariantCultureIgnoreCase));
                if (dateColumn == null)
                {
                    continue;
                }
                var earliestDateCell = earliestRow?.Cells.First(c => c.ColumnIndex == dateColumn.ColumnIndex);
                var thisDateCell = duplicateRow.Cells.First(c => c.ColumnIndex == dateColumn.ColumnIndex);

                bool deficiencyAlreadyExists = false;
                if (earliestRow != null && AreDatesEqual(earliestDateCell?.Value, thisDateCell.Value))
                {
                    earliestRow.Validity = Validity.Invalid;
                    deficiencyAlreadyExists = await _deficiencyService.DoesDeficiencyExistForRowAsync(earliestRow,
                        context.Rule.Id, matterId);
                    if (!deficiencyAlreadyExists)
                    {
                        var deficiency = await _deficiencyService.CreateRuleDeficiencyAsync(earliestRow.Id,
                            RecordIdType.SignatureSheetRow,
                            context.Rule.Id, matterId, context.Sheet?.Id, note: notes,
                            needsReview: context.Rule.NeedsReview);
                        deficiency.OtherRecordId = earliestRow.Id;
                        deficiency.OtherRecordIdType = RecordIdType.SignatureSheetRow;
                        await _deficiencyService.AddRuleDeficiencyAsync(duplicateRow, RecordIdType.SignatureSheetRow,
                            deficiency);
                    }
                }
            }
        }
        // We have handled our own deficiencies, no need to create another one
        return ServiceResult<bool>.Succeeded(false);
    }

    private bool AreDatesEqual(string? value1, string? value2)
    {
        if (string.IsNullOrEmpty(value1)
            || string.IsNullOrEmpty(value2))
        {
            return false; // both have to be valid dates in order to be rule deficiencies
        }

        if (!DateTime.TryParse(value1, out var lhsDate))
        {
            return false;
        }

        if (!DateTime.TryParse(value2, out var rhsDate))
        {
            return false;
        }

        return lhsDate == rhsDate;
    }
}
