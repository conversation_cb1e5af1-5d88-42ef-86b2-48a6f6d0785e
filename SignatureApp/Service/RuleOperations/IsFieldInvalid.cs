﻿using DataInterface.RepositoryInterfaces;
using DataInterface.ServiceInterfaces;
using Model.Rules;
using Model.SignatureSheets;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Service.ServiceModels;

namespace Service.RuleOperations;

public class IsFieldInvalid : IRule, IRuleOperationAsync, ISelfManaged
{
    private readonly IDeficiencyRepository _deficiencyRepository;
    private readonly DeficiencyService _deficiencyService;

    public IsFieldInvalid(
        IDeficiencyRepository deficiencyRepository,
        DeficiencyService deficiencyService)
    {
        _deficiencyRepository = deficiencyRepository;
        _deficiencyService = deficiencyService;
    }

    // A rule returns true if there is a deficiency
    public async Task<ServiceResult<bool>> EvaluateAsync(RuleContext context, JToken lhsValue, JToken? rhsValue)
    {
        var matterId = context.Matter.Id;
        var field = JsonConvert.DeserializeObject<SignatureSheetField>(lhsValue.ToString());
        if (field == null) { return ServiceResult<bool>.Succeeded(false); }

        bool isDeficient = field.Validity == Validity.Invalid;
        if (isDeficient)
        {
            // load the deficiencies for this field
            var deficiencies = await _deficiencyRepository.GetDeficienciesByRecordAsync(field.Id, RecordIdType.SignatureSheetField);
            foreach (var deficiency in deficiencies)
            {
                await _deficiencyService.AddRowDeficienciesForSheetDeficiency(matterId, field.SignatureSheetId, deficiency.RuleId);
            }
        }
        // We have handled our own deficiencies, no need to create another one
        return ServiceResult<bool>.Succeeded(false);
    }
}

