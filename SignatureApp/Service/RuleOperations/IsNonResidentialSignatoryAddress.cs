using AutoMapper;
using DataInterface.ServiceInterfaces;
using Microsoft.Extensions.Logging;
using Model.ExternalDataSources;
using Model.Geocoding;
using Newtonsoft.Json.Linq;
using Service.ServiceModels;

namespace Service.RuleOperations;

public class IsNonResidentialSignatoryAddress : IRule, IRuleOperationAsync
{
    private readonly ILogger<IsNonResidentialSignatoryAddress> _logger;
    private readonly IMapper _mapper;
    private readonly IMapPlacesService _mapPlacesService;


    public IsNonResidentialSignatoryAddress(
        ILogger<IsNonResidentialSignatoryAddress> logger,
        IMapper mapper,
        IMapPlacesService mapPlacesService)
    {
        _logger = logger;
        _mapper = mapper;
        _mapPlacesService = mapPlacesService;
    }

    // A rule returns true if there is a deficiency
    public async Task<ServiceResult<bool>> EvaluateAsync(RuleContext context, JToken lhsValue, JToken? rhsValue)
    {
        var signatory = lhsValue.ToObject<Signatory>();
        if (signatory == null)
        {
            return ServiceResult<bool>.Succeeded(false, "No signatory");
        }

        if (signatory.Latitude == null || signatory.Longitude == null)
        {
            return ServiceResult<bool>.Succeeded(false, "No signatory lat/long");
        }

        var addressTypeResult = await _mapPlacesService.GetPlaceTypeAsync(signatory.Latitude.Value, signatory.Longitude.Value);
        if (!addressTypeResult.IsSuccess || addressTypeResult.Value == null)
        {
            return ServiceResult<bool>.Succeeded(true, string.Join("\n", addressTypeResult.ErrorMessages.ToArray()));
        }

        bool isNonResidential = !addressTypeResult.Value.IsResidential;
        var addressInput = _mapper.Map<AddressInput>(signatory);
        var (addressLine1, addressLine2) = AddressParsingService.CreateAddressLines(addressInput);
        var result = ServiceResult<bool>.Succeeded(isNonResidential,
            isNonResidential ? $"Address {addressLine1} {addressLine2} is qualified as {string.Join(", ", addressTypeResult.Value.Categories)}" : null);
        return result;

    }

}