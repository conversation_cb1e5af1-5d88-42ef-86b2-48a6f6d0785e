﻿using DataInterface.ServiceInterfaces;
using Newtonsoft.Json.Linq;
using Service.ServiceModels;

namespace Service.RuleOperations;

public class IsBackTextDifferent : IRule, IRuleOperation, ISelfManaged
{
    // A rule returns true if there is a deficiency
    public ServiceResult<bool> Evaluate(RuleContext context, JToken lhsValue, JToken? rhsValue)
    {
        bool isDeficient = false;
        // TODO: Need to use the MapLinesInTemplateToLinesInSheet function to compare the text of the template and the sheet
        return ServiceResult<bool>.Succeeded(isDeficient);
    }
}
