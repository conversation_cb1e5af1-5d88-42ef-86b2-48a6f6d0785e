﻿using DataInterface.ServiceInterfaces;
using Microsoft.Extensions.Logging;
using Model.ExternalDataSources;
using Newtonsoft.Json.Linq;
using Service.ServiceModels;

namespace Service.RuleOperations;

public class IsAddressLineOutOfBoundsOf : IRule, IRuleOperationAsync
{
    private readonly ILogger<IsAddressLineOutOfBoundsOf> _logger;

    private readonly BoundaryService _boundaryService;


    public IsAddressLineOutOfBoundsOf(BoundaryService boundaryService, ILogger<IsAddressLineOutOfBoundsOf> logger)
    {
        _logger = logger;
        _boundaryService = boundaryService;
    }

    // A rule returns true if there is a deficiency
    public async Task<ServiceResult<bool>> EvaluateAsync(RuleContext context, JToken lhsValue, JToken? rhsValue)
    {
        if (context.Row == null)
        {
            return ServiceResult<bool>.Succeeded(false, "Row is null");
        }

        if (rhsValue == null)
        {
            return ServiceResult<bool>.Succeeded(false);
        }

        var signatory = lhsValue.ToObject<Signatory>();
        if (signatory == null)
        {
            return ServiceResult<bool>.Succeeded(false, "No signatory");
        }
        if (signatory.Latitude == null || signatory.Longitude == null)
        {
            return ServiceResult<bool>.Succeeded(false, "No signatory lat/long");
        }

        var boundsName = rhsValue.Value<string>();
        if (string.IsNullOrEmpty(boundsName))
        {
            return ServiceResult<bool>.Succeeded(false, "Null or empty bounds name");
        }

        var isInBoundary = await _boundaryService.IsAddressLineInBoundary(
            new PointD((double)signatory.Latitude, (double)signatory.Longitude),
            signatory.UsStateId, boundsName);

        bool isDeficient = !isInBoundary;
        return ServiceResult<bool>.Succeeded(isDeficient, $"({signatory.Latitude:N6},{signatory.Longitude:N6}) was not in {boundsName} boundary");
    }
}