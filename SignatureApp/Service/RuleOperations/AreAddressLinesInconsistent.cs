﻿using System.Text;
using DataInterface.ServiceInterfaces;
using Microsoft.Extensions.Logging;
using Model.SignatureSheets;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Service.ServiceModels;

namespace Service.RuleOperations;

public class AreAddressLinesInconsistent : IRule, IRuleOperationAsync
{
    private readonly ILogger<AreAddressLinesInconsistent> _logger;
    private readonly IGeocodingService _geocodingService;

    public AreAddressLinesInconsistent(
        ILogger<AreAddressLinesInconsistent> logger,
        IGeocodingService geocodingService)
    {
        _logger = logger;
        _geocodingService = geocodingService;
    }

    // A rule returns true if there is a deficiency
    public async Task<ServiceResult<bool>> EvaluateAsync(RuleContext context, JToken lhsValue, JToken? rhsValue)
    {
        if (rhsValue == null)
        {
            return ServiceResult<bool>.Succeeded(false);
        }
        var leftField = JsonConvert.DeserializeObject<SignatureSheetField>(lhsValue.ToString());
        if (leftField == null)
        {
            return ServiceResult<bool>.Succeeded(true, "Address Line 1 field is NULL");
        }

        var rightField = JsonConvert.DeserializeObject<SignatureSheetField>(rhsValue.ToString());
        if (rightField == null)
        {
            return ServiceResult<bool>.Succeeded(true, "Address Line 2 field is NULL");
        }

        if (!leftField.IsReviewed)
        {
            return ServiceResult<bool>.Succeeded(false);
        }
        if (!rightField.IsReviewed)
        {
            return ServiceResult<bool>.Succeeded(false);
        }

        var addressLine1 = leftField.Value;
        var addressLine2 = rightField.Value;
        if (string.IsNullOrWhiteSpace(addressLine1))
        {
            return ServiceResult<bool>.Succeeded(true, "Address Line 1 field is empty");
        }
        if (string.IsNullOrWhiteSpace(addressLine2))
        {
            return ServiceResult<bool>.Succeeded(true, "Address Line 2 field is empty");
        }


        var originalAddress = AddressParsingService.ParseAddress(addressLine1, addressLine2);
        if (originalAddress == null)
        {
            return ServiceResult<bool>.Succeeded(true, $"Unable to parse {addressLine1} {addressLine2}");
        }
        var addressInput = AddressParsingService.ParseAddressLine2(addressLine2);
        addressInput.AddressLine = addressLine1;
        var result = await _geocodingService.GeocodeAsync(addressInput);
        if (!result.IsSuccess)
        {
            return ServiceResult<bool>.Succeeded(false, string.Join("\n", result.ErrorMessages.ToArray()));
        }

        if (result.Value?.ExactMatch?.NewParsedAddress == null)
        {
            return ServiceResult<bool>.Succeeded(true, "Newly parsed address is NULL");
        }

        var notesBuilder = new StringBuilder();
        var newAddress = result.Value.ExactMatch.NewParsedAddress;
        var areStreetNumbersTheSame =
            originalAddress.StreetNumber == newAddress.StreetNumber;
        if (!areStreetNumbersTheSame)
        {
            notesBuilder.AppendLine($"Street numbers are different: {originalAddress.StreetNumber} vs {newAddress.StreetNumber}");
        }

        var areStreetNamesTheSame = originalAddress.StreetName == newAddress.StreetName;
        if (!areStreetNamesTheSame)
        {
            notesBuilder.AppendLine($"Street names are different: {originalAddress.StreetName} vs {newAddress.StreetName}");
        }

        // var areStreetTypesTheSame = originalAddress.StreetType == newAddress.StreetType;
        // if (!areStreetTypesTheSame)
        // {
        //     notesBuilder.AppendLine($"Street types are different: {originalAddress.StreetType} vs {newAddress.StreetType}");
        // }

        var areCitiesTheSame = originalAddress.City == newAddress.City;
        if (!areCitiesTheSame)
        {
            notesBuilder.AppendLine($"Cities are different: {originalAddress.City} vs {newAddress.City}");
        }

        var areStatesTheSame = originalAddress.State == newAddress.State;
        if (!areStatesTheSame)
        {
            notesBuilder.AppendLine($"States are different: {originalAddress.State} vs {newAddress.State}");
        }

        if (originalAddress.PostalCode == null || newAddress.PostalCode == null)
        {
            return ServiceResult<bool>.Succeeded(true, "Original postal code was NULL or new postal code is null"); // if either are blank it is a deficiency
        }
        var arePostalCodesTheSame =
            AddressParsingService.ArePostalCodesTheSame(originalAddress.PostalCode, newAddress.PostalCode);
        if (!arePostalCodesTheSame)
        {
            notesBuilder.AppendLine($"Postal codes are different: {originalAddress.PostalCode} vs {newAddress.PostalCode}");
        }

        bool isAddressSame =
            areStreetNumbersTheSame
            && areStreetNamesTheSame
            //&& areStreetTypesTheSame
            && areCitiesTheSame
            && areStatesTheSame
            && arePostalCodesTheSame;

        // if we couldn't geocode the address or the it doesn't correspond to a resource it is deficient
        // we have a value that is high confidence and the address returned is equal to the address given
        bool isDeficient = !isAddressSame;
        return ServiceResult<bool>.Succeeded(isDeficient, notesBuilder.ToString());
    }
}
