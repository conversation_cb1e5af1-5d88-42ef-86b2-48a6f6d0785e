﻿using DataInterface.ServiceInterfaces;
using Model.Geocoding;
using Model.SignatureSheets;
using Newtonsoft.Json.Linq;
using Service.ServiceModels;

namespace Service.RuleOperations;

public class IsZipCodeOutOfBoundsOf : IRule, IRuleOperationAsync
{
    private readonly BoundaryService _boundaryService;

    public IsZipCodeOutOfBoundsOf(BoundaryService boundaryService)
    {
        _boundaryService = boundaryService;
    }

    // A rule returns true if there is a deficiency
    public async Task<ServiceResult<bool>> EvaluateAsync(RuleContext context, JToken lhsValue, JToken? rhsValue)
    {
        if (rhsValue == null)
        {
            return ServiceResult<bool>.Succeeded(false);
        }

        var cell = lhsValue.ToObject<SignatureSheetCell>();
        if (cell == null || !cell.IsReviewed || cell.Validity != Validity.Valid)
        {
            return ServiceResult<bool>.Succeeded(false); // can't test a cell without a valid value
        }

        var address = new AddressInput();
        var templateColumn = cell.TemplateSignatureColumn;
        if (templateColumn?.IsAddress != true)
        {
            return ServiceResult<bool>.Succeeded(false);
        }

        string postalCode = "";
        if (templateColumn.Name != null && (templateColumn.Name.Contains("Zip", StringComparison.InvariantCultureIgnoreCase)
                                            || templateColumn.Name.Contains("Postal", StringComparison.InvariantCultureIgnoreCase)))
        {
            postalCode = cell.Value ?? "";
        }
        else
        {
            if (cell.Value != null)
            {
                address = AddressParsingService.ParseFullAddressToInput(cell.Value);
                if (address?.PostalCode != null)
                {
                    postalCode = address.PostalCode;
                }
            }
        }

        if (string.IsNullOrEmpty(postalCode))
        {
            return ServiceResult<bool>.Succeeded(false); // can't test an empty address
        }

        var boundsName = rhsValue.Value<string>();
        if (string.IsNullOrEmpty(boundsName))
        {
            return ServiceResult<bool>.Succeeded(false, "Null or empty bounds name");
        }

        var usState = UsStates.ParseState(address?.State);
        if (usState == null)
        {
            return ServiceResult<bool>.Succeeded(false, "Unable to find the correct state for the address");
        }
        bool isInBoundary = await _boundaryService.IsZipCodeInBoundary(usState.Id, boundsName, postalCode);

        bool isDeficient = !isInBoundary;
        return ServiceResult<bool>.Succeeded(isDeficient);
    }

    /*
    private AddressInput GetAddressFromRow(SignatureSheetRow row)
    {
        var addressInput = new AddressInput { Country = "USA", State = "AZ" };
        bool isPartialAddress = false;
        for (int index = row.Cells.Count - 1; index >= 0; index--)
        {
            var cell = row.Cells[index];
            var templateColumn = cell.TemplateSignatureColumn;
            if (templateColumn.IsAddress != true)
            {
                continue;
            }

            if (templateColumn.Name.Contains("City", StringComparison.InvariantCultureIgnoreCase))
            {
                isPartialAddress = true;
                addressInput.City = cell.Value;
            }
            else if (templateColumn.Name.Contains("Postal", StringComparison.InvariantCultureIgnoreCase)
                || templateColumn.Name.Contains("Zip", StringComparison.InvariantCultureIgnoreCase))
            {
                isPartialAddress = true;
                addressInput.PostalCode = cell.Value;
            }
            else if (isPartialAddress)
            {
                addressInput.AddressLine = cell.Value;
            }
            else
            {
                addressInput = AddressParsingService.ParseFullAddressToInput(cell.Value);
            }
        }

        return addressInput;
    }
    */
}
