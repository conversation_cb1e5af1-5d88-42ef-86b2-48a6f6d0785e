﻿using Model.Interfaces;
using Model.Templates;
using Service.ServiceModels;
using System.Drawing;
using Point = NetTopologySuite.Geometries.Point;

namespace Service
{
    public static class GeometryService
    {
        public static IHaveSimpleBounds RawTransformBounds(IHaveSimpleBounds originalBounds, IHaveDimensions thisPage, double[] shiftVector)
        {
            var topLeft = new PointD((double)originalBounds.Left, (double)originalBounds.Top);
            var bottomRight = new PointD((double)originalBounds.Right, (double)originalBounds.Bottom);
            var newTopLeft = AffineTransformationService.ApplyAffineTransform(shiftVector, topLeft);
            var newBottomRight = AffineTransformationService.ApplyAffineTransform(shiftVector, bottomRight);
            return new CellBounds
            {
                Left = (decimal)newTopLeft.X,
                Top = (decimal)newTopLeft.Y,
                Right = (decimal)newBottomRight.X,
                Bottom = (decimal)newBottomRight.Y,
            };
        }

        public static bool IsOutOfBounds(IHaveSimpleBounds bounds, IHaveDimensions thisPage)
        {
            var width = bounds.Right - bounds.Left;
            var percentOfHeight = 0.50m*(bounds.Bottom - bounds.Top);
            if (bounds.Left < 0)
            {
                return bounds.Left < -(0.2m * width);
            }

            if (bounds.Top < 0)
            {
                return bounds.Top < -percentOfHeight;
            }

            if (bounds.Right > (decimal)thisPage.Width)
            {
                return bounds.Right > (decimal)thisPage.Width + 0.5m*width;
            }

            if (bounds.Bottom > (decimal)thisPage.Height)
            {
                return bounds.Bottom > (decimal)thisPage.Height + percentOfHeight;
            }

            return false;
        }

        public static IHaveSimpleBounds TransformBounds(IHaveSimpleBounds originalBounds, IHaveDimensions thisPage, double[] shiftVector)
        {
            var topLeft = new PointD((double)originalBounds.Left, (double)originalBounds.Top);
            var bottomRight = new PointD((double)originalBounds.Right, (double)originalBounds.Bottom);
            var newTopLeft = AffineTransformationService.ApplyAffineTransform(shiftVector, topLeft);
            var newBottomRight = AffineTransformationService.ApplyAffineTransform(shiftVector, bottomRight);
            return new CellBounds
            {
                Left = Math.Max(0m, (decimal)newTopLeft.X),
                Top = Math.Max(0m, (decimal)newTopLeft.Y),
                Right = Math.Min((decimal)thisPage.Width, (decimal)newBottomRight.X),
                Bottom = Math.Min((decimal)thisPage.Height, (decimal)newBottomRight.Y),
            };
        }

        public static IHaveSimpleBounds VerticallyTransformBounds(IHaveSimpleBounds originalBounds, decimal height, double[] shiftVector)
        {
            var topLeft = new PointD((double)originalBounds.Left, (double)originalBounds.Top);
            var bottomRight = new PointD((double)originalBounds.Right, (double)originalBounds.Bottom);
            var newTopLeft = AffineTransformationService.ApplyAffineTransform(shiftVector, topLeft);
            var newBottomRight = AffineTransformationService.ApplyAffineTransform(shiftVector, bottomRight);
            return new CellBounds
            {
                Left = originalBounds.Left,
                Top = Math.Max(0m, (decimal)newTopLeft.Y),
                Right = originalBounds.Right,
                Bottom = Math.Min(height, (decimal)newBottomRight.Y),
            };
        }

        public static IHaveSimpleBounds HorizontallyTransformBounds(IHaveSimpleBounds originalBounds, decimal width, double[] shiftVector)
        {
            var topLeft = new PointD((double)originalBounds.Left, (double)originalBounds.Top);
            var bottomRight = new PointD((double)originalBounds.Right, (double)originalBounds.Bottom);
            var newTopLeft = AffineTransformationService.ApplyAffineTransform(shiftVector, topLeft);
            var newBottomRight = AffineTransformationService.ApplyAffineTransform(shiftVector, bottomRight);
            return new CellBounds
            {
                Top = originalBounds.Top,
                Bottom = originalBounds.Bottom,
                Left = Math.Max(0m, (decimal)newTopLeft.X),
                Right = Math.Min(width, (decimal)newBottomRight.X),
            };
        }

        public static void TransformBoundsInto(IHaveSimpleBounds originalBounds, IHaveDimensions thisPage,
            double[] shiftVector, IHaveSimpleBounds targetObject)
        {
            var newBounds = TransformBounds(originalBounds, thisPage, shiftVector);
            targetObject.Left = newBounds.Left;
            targetObject.Top = newBounds.Top;
            targetObject.Right = newBounds.Right;
            targetObject.Bottom = newBounds.Bottom;
        }


        public static CellBounds GetCellBounds(IHaveSimpleBounds cell)
        {
            return new CellBounds
            {
                Left = cell.Left,
                Top = cell.Top,
                Bottom = cell.Bottom,
                Right = cell.Right,
            };
        }

        public static CellBounds ComputeCellBoundsFromRowAndColumn(IHaveSimpleBounds row, IHaveSimpleBounds column)
        {
            return new CellBounds
            {
                Left = column.Left,
                Top = row.Top,
                Right = column.Right,
                Bottom = row.Bottom,
            };
        }

        public static Point TranslateToPoint(IList<PointF> polygon, int boundingBoxIndex)
        {
            return new Point(polygon[boundingBoxIndex].X, polygon[boundingBoxIndex].Y);
        }

        public static TemplateFormLine? GetLeftMostLine(List<TemplateFormLine> nextLines)
        {
            return nextLines.MinBy(l => l.Left);
        }

        public static TemplateFormLine? GetRightMostLine(List<TemplateFormLine> prevLines)
        {
            return prevLines.MaxBy(l => l.Right);
        }

        public static T CreateFieldAboveThis<T>(T fieldInForm)
            where T : IHaveSimpleBounds, new()
        {
            var fieldHeight = fieldInForm.Bottom - fieldInForm.Top;
            var lineSpacing = fieldHeight * 0.2m; // TODO: Hardcoded, and is it right?
            return new T
            {
                Left = fieldInForm.Left,
                Top = fieldInForm.Top - lineSpacing - fieldHeight,
                Right = fieldInForm.Right,
                Bottom = fieldInForm.Top - lineSpacing,
            };
        }

        public static T CreateFieldBelowThis<T>(T fieldInForm)
            where T : IHaveSimpleBounds, new()
        {
            var fieldHeight = fieldInForm.Bottom - fieldInForm.Top;
            var lineSpacing = fieldHeight * 0.2m;
            return new T
            {
                Left = fieldInForm.Left,
                Top = fieldInForm.Top + lineSpacing,
                Right = fieldInForm.Right,
                Bottom = fieldInForm.Top + lineSpacing + fieldHeight,
            };
        }

        public static bool AreOnSameLine(IHaveSimpleBounds fieldInForm, IHaveSimpleBounds line)
        {
            var yOverlap = Overlap1D(line.Top, line.Bottom, fieldInForm.Top, fieldInForm.Bottom);
            if (yOverlap > 0)
            {
                var lineHeight = line.Bottom - line.Top;
                var fieldHeight = fieldInForm.Bottom - fieldInForm.Top;
                var percentOverlap = yOverlap / Math.Min(lineHeight, fieldHeight);
                return (percentOverlap > .666m);
            }
            return false;
        }


        private static string GetPointString(Point point)
        {
            return $"({point.X:00.00},{point.Y:00.00})";
        }

        public static TemplateFormLine? GetLineOnLeft(IHaveSimpleBounds fieldInForm, List<TemplateFormLine> sameLines)
        {
            return sameLines
                .Where(l => fieldInForm.Left - l.Right > 0)
                .MinBy(l => fieldInForm.Left - l.Right);
        }

        public static TemplateFormLine? GetLineOnRight(IHaveSimpleBounds fieldInForm, List<TemplateFormLine> sameLines)
        {
            return sameLines
                .Where(l => l.Left - fieldInForm.Right > 0)
                .MinBy(l => l.Left - fieldInForm.Right);
        }

        public static List<TemplateFormLine> GetLinesOnSameLineAsField(TranscribableField fieldInForm, List<TemplateFormLine> formLines)
        {
            // get the formLines on the same line as this field (using two thirds overlap)
            var linesWithOverlap = new List<TemplateFormLine>();
            IEnumerable<TemplateFormLine> linesOnSamePage = formLines.Where(fl => fl.TemplatePage == fieldInForm.TemplatePage);
            foreach (var line in linesOnSamePage)
            {
                var yOverlap = Overlap1D(line.Top, line.Bottom, fieldInForm.Top, fieldInForm.Bottom);
                if (yOverlap > 0)
                {
                    var lineHeight = line.Bottom - line.Top;
                    var fieldHeight = fieldInForm.Bottom - fieldInForm.Top;
                    var percentOverlap = yOverlap / Math.Min(lineHeight, fieldHeight);
                    if (percentOverlap > .75m)
                    {
                        linesWithOverlap.Add(line);
                    }
                }
            }
            return linesWithOverlap;
        }

        public static bool IsContainedWithin(IHaveSimpleBounds outer, IHaveSimpleBounds inner)
        {
            bool isLeftWithin = outer.Left <= inner.Left;
            bool isRightWithin = outer.Right >= inner.Right;
            bool isTopWithin = outer.Top <= inner.Top;
            bool isBottomWithin = outer.Bottom >= inner.Bottom;
            return isLeftWithin && isRightWithin && isTopWithin && isBottomWithin;
        }

        public static bool AreOnSameLine(double top1, double bottom1, double top2, double bottom2)
        {
            return PercentOverlap1D(top1, bottom1, top2, bottom2) > .5;
        }

        public static double Overlap1D(double min1, double max1, double min2, double max2)
        {
            return Math.Max(0, Math.Min(max1, max2) - Math.Max(min1, min2));
        }
        public static decimal Overlap1D(decimal min1, decimal max1, decimal min2, decimal max2)
        {
            return Math.Max(0, Math.Min(max1, max2) - Math.Max(min1, min2));
        }

        public static double PercentOverlap1D(double min1, double max1, double min2, double max2)
        {
            var overlap1D = Overlap1D(min1, max1, min2, max2);
            var total = Math.Max(max1, max2) - Math.Min(min1, min2);
            return overlap1D / total;
        }
        public static decimal PercentOverlap1D(decimal min1, decimal max1, decimal min2, decimal max2)
        {
            var overlap1D = Overlap1D(min1, max1, min2, max2);
            var total = Math.Max(max1, max2) - Math.Min(min1, min2);
            return overlap1D / total;
        }

        public static bool TryGetOverlapPercent(
            this IHaveSimpleBounds line,
            IHaveSimpleBounds field,
            out decimal start,  // 0 → line.Left
            out decimal stop)   // 1 → line.Right
        {
            start = stop = 0.0m;

            decimal lineLength = line.Right - line.Left;
            if (lineLength <= 0) return false;

            decimal cLeft  = Math.Max(line.Left,  field.Left);
            decimal cRight = Math.Min(line.Right, field.Right);

            if (cLeft >= cRight) return false;      // No overlap

            start = (cLeft  - line.Left) / lineLength;
            stop  = (cRight - line.Left) / lineLength;
            return true;
        }
        public static double WidthPercentOverlap(double templateHeaderLeft, double templateHeaderRight, double scaledTableLeft, double scaledTableRight)
        {
            return PercentOverlap1D(0, templateHeaderRight - templateHeaderLeft, 0, scaledTableRight - scaledTableLeft);
        }
        public static decimal WidthPercentOverlap(decimal templateHeaderLeft, decimal templateHeaderRight, decimal scaledTableLeft, decimal scaledTableRight)
        {
            return PercentOverlap1D(0m, templateHeaderRight - templateHeaderLeft, 0m, scaledTableRight - scaledTableLeft);
        }

        public static decimal CalculateArea(IHaveSimpleBounds bounds)
        {
            var width = bounds.Right - bounds.Left;
            var height = bounds.Bottom - bounds.Top;
            return width * height;
        }

        public static decimal Overlap2D(IHaveSimpleBounds bounds1, IHaveSimpleBounds bounds2)
        {
            var widthOverlap = Overlap1D(bounds1.Left, bounds1.Right, bounds2.Left, bounds2.Right);
            if (widthOverlap == 0) return 0;
            var heightOverlap = Overlap1D(bounds1.Top, bounds1.Bottom, bounds2.Top, bounds2.Bottom);
            if (heightOverlap == 0) return 0;
            return widthOverlap * heightOverlap;
        }

        public static decimal PercentOverlap2D(IHaveSimpleBounds bounds1, IHaveSimpleBounds bounds2)
        {
            var overlap = Overlap2D(bounds1, bounds2);
            if (overlap == 0) return 0;
            var largestArea = Math.Max(CalculateArea(bounds1), CalculateArea(bounds2));
            return overlap / largestArea;
        }

        public static CellBounds ComputeCellBoundsFromCells(List<IHaveSimpleBounds> cells)
        {
            var upperY = cells.Min(c => c.Top);
            var lowerY = cells.Max(c => c.Bottom);
            var leftX  = cells.Min(c => c.Left);
            var rightX = cells.Max(c => c.Right);
            return new CellBounds
            {
                Left = leftX,
                Top = upperY,
                Right = rightX,
                Bottom = lowerY,
            };
        }

        public static CellBounds ComputeSurroundingRowCellBounds(CellBounds cellsBoundary)
        {
            var height = cellsBoundary.Bottom - cellsBoundary.Top;
            return new CellBounds
            {
                Left = cellsBoundary.Left,
                Top = cellsBoundary.Top - height,
                Right = cellsBoundary.Right,
                Bottom = cellsBoundary.Bottom + height,
            };
        }

        internal static CellBounds GetBoundsFromPolygon(IReadOnlyList<PointF> polygon)
        {
            return new CellBounds
            {
                Left = (decimal)Math.Min(polygon[0].X, polygon[3].X),
                Top = (decimal)Math.Min(polygon[0].Y, polygon[1].Y),
                Right = (decimal)Math.Max(polygon[1].X, polygon[2].X),
                Bottom = (decimal)Math.Max(polygon[2].Y, polygon[3].Y),
            };
        }

        public static CellBounds ExpandBounds(IHaveSimpleBounds cellBounds, decimal widthScaleRatio, decimal heightScaleRatio)
        {
            var width = cellBounds.Right - cellBounds.Left;
            var height = cellBounds.Bottom - cellBounds.Top;
            var widthDelta = width * widthScaleRatio;
            var heightDelta = height * heightScaleRatio;
            return new CellBounds
            {
                Left = Math.Max(0, cellBounds.Left - widthDelta),
                Top = Math.Max(0, cellBounds.Top - heightDelta),
                Right = cellBounds.Right + widthDelta,
                Bottom = cellBounds.Bottom + heightDelta,
            };
        }
    }
}
