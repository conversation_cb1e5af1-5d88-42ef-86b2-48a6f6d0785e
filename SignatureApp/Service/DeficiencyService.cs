using System.Diagnostics;
using System.Globalization;
using AutoMapper;
using CsvHelper;
using CsvHelper.Configuration;
using DataInterface.RepositoryInterfaces;
using DataInterface.ServiceInterfaces;
using Microsoft.Extensions.Logging;
using Model.Boundaries;
using Model.Deficiencies;
using Model.ExternalDataSources;
using Model.Interfaces;
using Model.Rules;
using Model.SignatureSheets;
using Model.Templates;
using Service.ServiceModels;
using Task = System.Threading.Tasks.Task;

namespace Service;

public class DeficiencyService
{
    private readonly IBoundaryRepository _boundaryRepository;
    private readonly ICirculatorRepository _circulatorRepository;
    private readonly IDeficiencyRepository _deficiencyRepository;
    private readonly IDeficiencyReviewRepository _deficiencyReviewRepository;
    private readonly IFileService _fileService;
    private readonly ILogger<DeficiencyService> _logger;
    private readonly IMapper _mapper;
    private readonly IMatterVariableRepository _matterVariableRepository;
    private readonly ISignatureSheetFieldRepository _signatureSheetFieldRepository;
    private readonly ISignatureSheetCellRepository _signatureSheetCellRepository;
    private readonly ISignatureSheetRowRepository _signatureSheetRowRepository;
    private readonly ISignatureSheetRepository _signatureSheetRepository;
    private readonly IRuleRepository _ruleRepository;
    private readonly ITemplateSignatureColumnRepository _templateSignatureColumnRepository;
    private readonly IWorkRepository _workRepository;
    private readonly PdfManipulationService _pdfManipulationService;
    private readonly RuleExpressionHelper _ruleExpressionHelper;
    private readonly SignatoryService _signatoryService;


    public DeficiencyService(
        IBoundaryRepository boundaryRepository,
        ICirculatorRepository circulatorRepository,
        IDeficiencyRepository deficiencyRepository,
        IDeficiencyReviewRepository deficiencyReviewRepository,
        IFileService fileService,
        ILogger<DeficiencyService> logger,
        IMapper mapper,
        IMatterVariableRepository matterVariableRepository,
        ISignatureSheetFieldRepository signatureSheetFieldRepository,
        ISignatureSheetCellRepository signatureSheetCellRepository,
        ISignatureSheetRowRepository signatureSheetRowRepository,
        ISignatureSheetRepository signatureSheetRepository,
        IRuleRepository ruleRepository,
        ITemplateSignatureColumnRepository templateSignatureColumnRepository,
        IWorkRepository workRepository,
        PdfManipulationService pdfManipulationService,
        RuleExpressionHelper ruleExpressionHelper,
        SignatoryService signatoryService
    )
    {
        _boundaryRepository = boundaryRepository;
        _circulatorRepository = circulatorRepository;
        _deficiencyRepository = deficiencyRepository;
        _deficiencyReviewRepository = deficiencyReviewRepository;
        _fileService = fileService;
        _logger = logger;
        _mapper = mapper;
        _matterVariableRepository = matterVariableRepository;
        _signatureSheetFieldRepository = signatureSheetFieldRepository;
        _signatureSheetCellRepository = signatureSheetCellRepository;
        _signatureSheetRowRepository = signatureSheetRowRepository;
        _signatureSheetRepository = signatureSheetRepository;
        _ruleRepository = ruleRepository;
        _templateSignatureColumnRepository = templateSignatureColumnRepository;
        _workRepository = workRepository;
        _pdfManipulationService = pdfManipulationService;
        _ruleExpressionHelper = ruleExpressionHelper;
        _signatoryService = signatoryService;
    }

    public async Task AddRuleDeficiencyAsync(IHaveId record, RecordIdType recordIdType, Deficiency deficiency)
    {
        _deficiencyRepository.Add(deficiency);
        await _deficiencyRepository.SaveChangesAsync();
        await TriggerRecomputeValidityAsync(record, recordIdType);
    }

    public async Task<Deficiency> CreateRuleDeficiencyAsync(int recordId, RecordIdType recordIdType, int ruleId, int matterId,
        int? signatureSheetId, string? note = null, bool needsReview = false)
    {
        // Validate that the rule exists
        var rule = await _ruleRepository.GetByIdAsync(ruleId);
        if (rule == null)
        {
            throw new InvalidOperationException($"Rule with ID {ruleId} does not exist. Cannot create deficiency for record {recordId} of type {recordIdType}.");
        }

        var deficiency = CreateDeficiency(recordId, recordIdType, ruleId, matterId, signatureSheetId, note: note, needsReview:needsReview);
        if (needsReview)
        {
            await CreateDeficiencyReviewIfNecessaryAsync(deficiency);
        }
        return deficiency;
    }

    private async Task CreateDeficiencyReviewIfNecessaryAsync(Deficiency deficiency)
    {
        var deficiencyReview = await _deficiencyReviewRepository.GetByDeficiencyAsync(deficiency);
        if (deficiencyReview == null)
        {
            deficiencyReview = new DeficiencyReview
            {
                RecordId = deficiency.RecordId,
                RecordIdType = deficiency.RecordIdType,
                RuleId = deficiency.RuleId,
                MatterId = deficiency.MatterId,
                SignatureSheetId = deficiency.SignatureSheetId,
            };

            _deficiencyReviewRepository.Add(deficiencyReview);
        }
    }

    public static Deficiency CreateUserDeficiency(int recordId, RecordIdType recordIdType, int ruleId, int matterId,
        int? signatureSheetId, int userId, int? workId, string? note)
    {
        return CreateDeficiency(recordId, recordIdType, ruleId, matterId, signatureSheetId, workId, userId, note: note);
    }

    private static Deficiency CreateDeficiency(int recordId, RecordIdType recordIdType, int ruleId, int matterId,
        int? signatureSheetId, int? workId = null, int? userId = null, string? note = null, bool? needsReview = null)
    {
        var deficiency = new Deficiency
        {
            RecordId = recordId,
            RecordIdType = recordIdType,
            RuleId = ruleId,
            MatterId = matterId,
            SignatureSheetId = signatureSheetId,
            Note = note,
            UserId = userId,
            WorkId = workId,
            NeedsReview = needsReview == true
        };

        return deficiency;
    }

    public async Task RecomputeCellValidityAsync(SignatureSheetCell signatureSheetCell)
    {
        var cellValidity = signatureSheetCell.Validity;
        if (cellValidity == Validity.Strikethrough)
        {
            return;
        }

        var existingDeficiencyies =
            await _deficiencyRepository.GetDeficienciesByRecordAsync(signatureSheetCell.Id,
                RecordIdType.SignatureSheetCell);
        signatureSheetCell.Validity = existingDeficiencyies.Count > 0 ? Validity.Invalid : Validity.Valid;
        if (cellValidity != signatureSheetCell.Validity)
        {
            await RecomputeRowValidityAsync(signatureSheetCell.SignatureSheetRow);
        }
    }

    public async Task RecomputeRowValidityAsync(SignatureSheetRow signatureSheetRow)
    {
        var previousRowValidity = signatureSheetRow.Validity;
        if (previousRowValidity == Validity.Strikethrough)
        {
            return;
        }

        var existingDeficiencyies =
            await _deficiencyRepository.GetDeficienciesByRecordAsync(signatureSheetRow.Id,
                RecordIdType.SignatureSheetRow);
        signatureSheetRow.Validity = existingDeficiencyies.Count > 0 ? Validity.Invalid : Validity.Valid;
    }

    public async Task<int> AddOrRemoveTranscriptionDeficiencyAsync(AddOrRemove addOrRemove, IHaveId record,
        RecordIdType recordIdType, int? ruleId, int matterId, int workId = 0, int userId = 0, string? note = null)
    {
        int? signatureSheetId = GetSignatureSheetIdFromRecord(record, recordIdType);
        var existingDeficiencies = await _deficiencyRepository.GetDeficienciesByRecordAsync(record.Id, recordIdType);
        var previousDeficiencyCount = existingDeficiencies.Count;
        var deficiencyCount = previousDeficiencyCount;
        Deficiency? thisDeficiency = null;
        Rule? rule = null;
        if (ruleId != null)
        {
            thisDeficiency = existingDeficiencies.SingleOrDefault(d => d.RuleId == ruleId);
            rule = thisDeficiency?.Rule;
        }
        else if (addOrRemove == AddOrRemove.Remove)
        {
            thisDeficiency = existingDeficiencies.SingleOrDefault(d =>
                d.RecordId == record.Id && d.RecordIdType == recordIdType
                && d.UserId != null);
            rule = thisDeficiency?.Rule;
        }
        if (addOrRemove == AddOrRemove.Remove && thisDeficiency != null)
        {
            _deficiencyRepository.Remove(thisDeficiency);
            deficiencyCount--;
        }
        // If this rule is a transcription rule, there can only be one transcription deficiency for a given cell
        else if (rule != null && rule.RuleContextType == RuleContextType.Transcription)
        {
            var transcriptionDeficiencies =
                existingDeficiencies.Where(d => d.Rule.RuleContextType == RuleContextType.Transcription);
            foreach (var transcriptDeficiency in transcriptionDeficiencies)
            {
                _deficiencyRepository.Remove(transcriptDeficiency);
                deficiencyCount--;
            }
        }

        if (addOrRemove == AddOrRemove.Add && rule != null)
        {
            var deficiency = CreateUserDeficiency(record.Id, recordIdType, rule.Id, matterId, signatureSheetId,
                userId, workId, note);
            _deficiencyRepository.Add(deficiency);
            deficiencyCount++;
        }

        await _deficiencyRepository.SaveChangesAsync();
        if (previousDeficiencyCount != deficiencyCount)
        {
            await TriggerRecomputeValidityAsync(record, recordIdType);
        }

        return deficiencyCount;
    }

    private static int? GetSignatureSheetIdFromRecord(IHaveId record, RecordIdType recordIdType)
    {
        return recordIdType switch
        {
            RecordIdType.SignatureSheet => record.Id,
            RecordIdType.SignatureSheetRow => ((SignatureSheetRow)record).SignatureSheetId,
            RecordIdType.SignatureSheetField => ((SignatureSheetField)record).SignatureSheetId,
            RecordIdType.SignatureSheetCell => ((SignatureSheetCell)record).SignatureSheetRow.SignatureSheetId,
            _ => null
        };
    }

    private async Task TriggerRecomputeValidityAsync(IHaveId record, RecordIdType recordIdType)
    {
        switch (recordIdType)
        {
            case RecordIdType.SignatureSheetCell:
                await RecomputeCellValidityAsync((SignatureSheetCell)record);
                break;
            case RecordIdType.SignatureSheetRow:
                await RecomputeRowValidityAsync((SignatureSheetRow)record);
                break;
            case RecordIdType.None:
            case RecordIdType.SignatureSheetField:
            case RecordIdType.SignatureSheet:
            case RecordIdType.Matter:
            default:
                // No validity recomputation needed for these record types
                break;
        }
    }

    public async Task DeleteRulesEngineCreatedDeficienciesAsync(int matterId)
    {
        await _deficiencyRepository.DeleteRulesGeneratedDeficienciesAsync(matterId);
    }

    public async Task AddRowDeficienciesForSheetDeficiency(int matterId, int signatureSheetId, int ruleId,
        string? note = null)
    {
        var rows = await _signatureSheetRowRepository.GetRowsAndCellsBySignatureSheetIdAsync(signatureSheetId);
        foreach (var row in rows)
        {
            if (row.Validity == Validity.Strikethrough)
            {
                continue;
            }

            var ruleDeficiency = await CreateRuleDeficiencyAsync(row.Id, RecordIdType.SignatureSheetRow, ruleId, matterId,
                signatureSheetId, note: note);
            await AddRuleDeficiencyAsync(row, RecordIdType.SignatureSheetRow, ruleDeficiency);
        }
    }

    public async Task<Deficiency> AddMatterDeficiencyAsync(int matterId, int ruleId, int userId, string? note)
    {
        var matterDeficiency = CreateUserDeficiency(matterId, RecordIdType.Matter, ruleId, matterId, null,
            userId: userId, workId: null, note);
        _deficiencyRepository.Add(matterDeficiency);

        var sheets = await _signatureSheetRepository.GetAllByMatterIdAsync(matterId);
        foreach (var sheet in sheets)
        {
            await AddRowDeficienciesForSheetAsync(matterId, ruleId, note, sheet);
        }

        await _deficiencyRepository.SaveChangesAsync();
        return matterDeficiency;
    }

    private async Task AddRowDeficienciesForSheetAsync(int matterId, int ruleId, string? note, SignatureSheet sheet)
    {
        foreach (var row in sheet.Rows)
        {
            if (row.Validity == Validity.Strikethrough)
            {
                continue;
            }

            var deficiency = await CreateRuleDeficiencyAsync(row.Id, RecordIdType.SignatureSheetRow, ruleId, matterId,
                sheet.Id, note: note);
            _deficiencyRepository.Add(deficiency);
            row.Validity = Validity.Invalid;
        }
    }

    public async Task DeleteMatterDeficiencyAsync(int matterId, int ruleId)
    {
        var matterDeficiency =
            await _deficiencyRepository.GetDeficiencyByRecordAndRuleAsync(matterId, RecordIdType.Matter, ruleId);
        if (matterDeficiency == null)
        {
            return; // Not Found
        }

        _deficiencyRepository.Remove(matterDeficiency);
        var rowDeficiencies =
            await _deficiencyRepository.GetDeficienciesByRuleAsync(matterId, matterDeficiency.RuleId);
        foreach (var deficiency in rowDeficiencies)
        {
            _deficiencyRepository.Remove(deficiency);
        }

        await _deficiencyRepository.SaveChangesAsync();

        var sheets = await _signatureSheetRepository.GetAllByMatterIdAsync(matterId);
        foreach (var sheet in sheets)
        {
            foreach (var row in sheet.Rows)
            {
                await TriggerRecomputeValidityAsync(row, RecordIdType.SignatureSheetRow);
            }
        }

        await _signatureSheetRepository.SaveChangesAsync();
    }


    public async Task<List<DeficienciesBySheetAndRow>> GetDeficiencySheetRowsAsync(List<Deficiency> deficiencies)
    {
        var response = new List<DeficienciesBySheetAndRow>();

        foreach (var deficiency in deficiencies)
        {
            //if (deficiency.RecordIdType == RecordIdType.SignatureSheetField)
            //{
            //    var signatureSheetField = await _signatureSheetFieldRepository.GetByIdIncludingSignatureSheetAsync(deficiency.RecordId);
            //    if (signatureSheetField == null) { continue; }
            //    var sheet = signatureSheetField.SignatureSheet;
            //    int signatureTableId = sheet.Rows.First().TemplateSignatureTableId;
            //    var deficiencyBySheetAndRow = await GetOrCreateSheet(response, sheet, signatureTableId);

            //    foreach (var sheetRow in sheet.Rows)
            //    {
            //        var deficienciesByRow = GetOrCreateRow(deficiencyBySheetAndRow.DeficienciesByRow, sheetRow, signatureSheetField);
            //        deficienciesByRow.Deficiencies.Add(deficiency);
            //    }
            //}

            if (deficiency.RecordIdType == RecordIdType.SignatureSheetCell)
            {
                var cell = await _signatureSheetCellRepository.GetByIdIncludingSignatureSheetAsync(deficiency.RecordId);
                if (cell == null)
                {
                    continue;
                }

                var row = cell.SignatureSheetRow;
                var sheet = cell.SignatureSheetRow.SignatureSheet;
                int signatureTableId = cell.SignatureSheetRow.TemplateSignatureTableId;
                var deficiencyBySheetAndRow = await GetOrCreateSheet(response, sheet, signatureTableId);

                var deficienciesByRow = GetOrCreateRow(deficiencyBySheetAndRow.DeficienciesByRow, row, null);
                deficienciesByRow.Deficiencies.Add(deficiency);
            }

            if (deficiency.RecordIdType == RecordIdType.SignatureSheetRow)
            {
                var row = await _signatureSheetRowRepository.GetByIdIncludingSheetAndCellsAsync(deficiency.RecordId);
                if (row == null)
                {
                    continue;
                }

                var sheet = row.SignatureSheet;
                int signatureTableId = row.TemplateSignatureTableId;
                var deficiencyBySheetAndRow = await GetOrCreateSheet(response, sheet, signatureTableId);

                var deficienciesByRow = GetOrCreateRow(deficiencyBySheetAndRow.DeficienciesByRow, row, null);
                deficienciesByRow.Deficiencies.Add(deficiency);
            }
        }

        return response;
    }

    private async Task<DeficienciesBySheetAndRow> GetOrCreateSheet(List<DeficienciesBySheetAndRow> allSheets,
        SignatureSheet sheet, int signatureTableId)
    {
        var deficiencyBySheetAndRow = allSheets.SingleOrDefault(dbs =>
            dbs.SignatureSheet != null && dbs.SignatureSheet.SheetNumber == sheet.SheetNumber);
        if (deficiencyBySheetAndRow == null)
        {
            var templateSignatureColumns =
                await _templateSignatureColumnRepository.GetAllByTemplateSignatureTableIdAsync(signatureTableId);
            deficiencyBySheetAndRow = new DeficienciesBySheetAndRow
            {
                SignatureSheet = sheet,
                TemplateSignatureColumns = templateSignatureColumns,
                DeficienciesByRow = new List<DeficienciesByRow>(),
            };
            allSheets.Add(deficiencyBySheetAndRow);
        }

        return deficiencyBySheetAndRow;
    }

    private DeficienciesByRow GetOrCreateRow(List<DeficienciesByRow> allRows, SignatureSheetRow sheetRow,
        SignatureSheetField? signatureSheetField)
    {
        var deficiencyByRow = allRows.SingleOrDefault(dbr =>
            dbr.SignatureRow != null && dbr.SignatureRow.RowNumber == sheetRow.RowNumber);
        if (deficiencyByRow == null)
        {
            deficiencyByRow = new DeficienciesByRow
            {
                SignatureRow = sheetRow,
                SignatureSheetField = signatureSheetField,
                Deficiencies = new List<Deficiency>()
            };
            allRows.Add(deficiencyByRow);
        }

        return deficiencyByRow;
    }

    public async Task<List<DeficiencyResult>> TranslateToDtosAsync(List<Deficiency> deficiencies, int matterId,
        bool shouldIncludeImages = true)
    {
        var response = new List<DeficiencyResult>();

        foreach (var deficiency in deficiencies)
        {
            // TODO: Deficiency now has a SignatureSheetId, no need to go through this rigamarole
            // we could also query for all of the sheets ahead of time
            //  var sheetInfo = await GetSheetInfoFromDeficiency(deficiency.RecordId, deficiency.RecordIdType);
            if (deficiency.SignatureSheet == null)
            {
                continue;
            }

            var fileName = deficiency.SignatureSheet.FileName;
            var recordId = deficiency.RecordId;
            var result = _mapper.Map<DeficiencyResult>(deficiency);
            result.UserName = deficiency.User?.Email;
            bool isField = deficiency.RecordIdType == RecordIdType.SignatureSheetField;
            bool isRow = deficiency.RecordIdType == RecordIdType.SignatureSheetRow;
            bool isCell = deficiency.RecordIdType == RecordIdType.SignatureSheetCell;

            result.SignatureSheetFieldId = isField ? recordId : null;
            result.SignatureCellId = isCell ? recordId : null;
            result.SignatureRowId = isRow ? recordId : null;
            result.SignatureSheet = deficiency.SignatureSheet;
            result.SheetNumber = deficiency.SignatureSheet.SheetNumber;
            result.RowNumber = isRow
                ? deficiency.SignatureSheet.Rows.FirstOrDefault(r => r.Id == recordId)?.RowNumber
                : isCell
                    ? deficiency.SignatureSheet.Rows.SelectMany(r => r.Cells).FirstOrDefault(c => c.Id == recordId)
                        ?.RowIndex + 1 ?? 0
                    : 0;
            result.PageNumber = isField
                ? deficiency.SignatureSheet.Fields.FirstOrDefault(f => f.Id == recordId)?.Page ?? 0
                : 1;
            result.ColumnIndex = isCell
                ? deficiency.SignatureSheet.Rows.SelectMany(r => r.Cells).FirstOrDefault(c => c.Id == recordId)
                    ?.ColumnIndex
                : null;
            result.NeedsReview = deficiency.NeedsReview;
            result.IsReviewed = !deficiency.NeedsReview ? null : deficiency.ReviewedOn != null;
            if (shouldIncludeImages)
            {
                var imageSas = await GetDeficiencyImageSasUrl(matterId, fileName, deficiency);

                result.ImageUrl = imageSas;
            }

            response.Add(result);
        }

        return response;
    }

    private async Task<string?> GetDeficiencyImageSasUrl(int matterId, string fileName, Deficiency deficiency)
    {
        var containerName = $"matter{matterId}";
        var filePath = $"{deficiency.RecordIdType.ToString().ToLower()}-deficiencies/{deficiency.RecordId}.png";
        var filesInFolderAsync = await _fileService.GetFilesInFolderAsync($"{containerName}/{filePath}");
        if (filesInFolderAsync.Count == 0)
        {
            var imageBytes = await CreateImageFromDeficiencyAsync(deficiency, matterId, fileName);
            if (imageBytes == null)
            {
                return null;
            }

            using var imageStream = new MemoryStream(imageBytes);
            await _fileService.SaveFileStreamAsync(containerName, filePath, imageStream, overWrite: false,
                CancellationToken.None);
        }

        var imageSas = await _fileService.CreateSasUrlAsync(containerName, filePath);
        return imageSas;
    }

    private async Task<SheetInfo> GetSheetInfoFromDeficiency(int recordId, RecordIdType deficiencyRecordIdType)
    {
        var sheetInfo = new SheetInfo();
        if (deficiencyRecordIdType == RecordIdType.SignatureSheetField)
        {
            var signatureSheetField =
                await _signatureSheetFieldRepository.GetByIdIncludingSignatureSheetAsync(recordId);
            if (signatureSheetField == null)
            {
                return sheetInfo;
            }

            sheetInfo.SignatureSheetField = signatureSheetField;
            sheetInfo.SignatureSheet = signatureSheetField.SignatureSheet;
            sheetInfo.PageNumber = signatureSheetField.Page;
        }
        else if (deficiencyRecordIdType == RecordIdType.SignatureSheetCell)
        {
            var signatureSheetCell =
                await _signatureSheetCellRepository.GetByIdIncludingSignatureSheetAsync(recordId);
            if (signatureSheetCell == null)
            {
                return sheetInfo;
            }

            sheetInfo.SignatureSheetCell = signatureSheetCell;
            sheetInfo.SignatureSheetRow = signatureSheetCell.SignatureSheetRow;
            sheetInfo.SignatureSheet = sheetInfo.SignatureSheetRow.SignatureSheet;
        }

        else if (deficiencyRecordIdType == RecordIdType.SignatureSheetRow)
        {
            var signatureSheetRow =
                await _signatureSheetRowRepository.GetByIdIncludingSheetAndCellsAsync(recordId);
            if (signatureSheetRow == null)
            {
                return sheetInfo;
            }

            sheetInfo.SignatureSheetRow = signatureSheetRow;
            sheetInfo.SignatureSheet = signatureSheetRow.SignatureSheet;
        }
        else if (deficiencyRecordIdType == RecordIdType.SignatureSheet)
        {
            var signatureSheet = await _signatureSheetRepository.GetByIdAsync(recordId);
            if (signatureSheet == null)
            {
                return sheetInfo;
            }

            sheetInfo.SignatureSheet = signatureSheet;
        }

        return sheetInfo;
    }

    //TODO: This method can be optimized to avoid multiple database calls
    // Deficiency should have the asssociated SignatureSheetField, SignatureSheetCell, or SignatureSheetRow
    // so we can just return that directly instead of querying the database again.
    public IHaveSimpleBounds? GetBoundsFromDeficiency(Deficiency deficiency)
    {
        switch (deficiency.RecordIdType)
        {
            case RecordIdType.SignatureSheetField:
                var ssField = deficiency.SignatureSheet.Fields.FirstOrDefault(f => f.Id == deficiency.RecordId);
                // await _signatureSheetFieldRepository.GetByIdIncludingSignatureSheetAsync(deficiency.RecordId);
                if (ssField is null) return null;
                return ssField;
            case RecordIdType.SignatureSheetCell:
                var cell = deficiency.SignatureSheet.Rows.SelectMany(r => r.Cells)
                    .FirstOrDefault(c => c.Id == deficiency.RecordId);
                // var cell = await _signatureSheetCellRepository.GetByIdIncludingSignatureSheetAsync(deficiency.RecordId);
                if (cell is null) return null;
                return cell;
            case RecordIdType.SignatureSheetRow:
                var row = deficiency.SignatureSheet.Rows.FirstOrDefault(r => r.Id == deficiency.RecordId);
                // var row = await _signatureSheetRowRepository.GetByIdIncludingSheetAndCellsAsync(deficiency.RecordId);
                if (row is null) return null;
                return row;
            case RecordIdType.None:
            default:
                return null;
        }
    }

    public string GetBadgePrefix(DeficiencyResult sheetDeficiency)
    {
        var badgePrefix = $"s{sheetDeficiency.SheetNumber}";
        if (sheetDeficiency.FieldIndex != null)
        {
            badgePrefix += $":f{sheetDeficiency.FieldIndex}";
        }

        if (sheetDeficiency.RowNumber != null && sheetDeficiency.RowNumber != 0)
        {
            badgePrefix += $":r{sheetDeficiency.RowNumber}";
        }

        if (sheetDeficiency.ColumnIndex != null && sheetDeficiency.ColumnIndex != 0)
        {
            badgePrefix += $":c{sheetDeficiency.ColumnIndex}";
        }

        return badgePrefix;
    }


    private async Task<byte[]?> CreateImageFromDeficiencyAsync(Deficiency deficiency, int matterId, string fileName)
    {
        var pdf = await _fileService.GetFileStreamAsync($"matter{matterId}", fileName);
        if (pdf is null) return null;
        var amountToStretch = new CellBounds { Left = 0.1m, Right = 0.1m, Top = 0.2m, Bottom = 0.2m };

        int sheetIndex;
        switch (deficiency.RecordIdType)
        {
            case RecordIdType.SignatureSheetField:
            {
                // TODO: don't we already have this info?
                var ssField =
                    await _signatureSheetFieldRepository.GetByIdIncludingSignatureSheetAsync(deficiency.RecordId);
                if (ssField is null) return null;
                sheetIndex = ssField.SignatureSheet.SheetIndex;
                return _pdfManipulationService.CropImageToBounds(pdf, ssField, ssField, amountToStretch,
                    sheetIndex * 2 + ssField.Page);
            }
            case RecordIdType.SignatureSheetCell:
            {
                var cell = await _signatureSheetCellRepository.GetByIdIncludingSignatureSheetAsync(deficiency.RecordId);
                if (cell is null) return null;
                sheetIndex = cell.SignatureSheetRow.SignatureSheet.SheetIndex;
                return _pdfManipulationService.CropImageToBounds(pdf, cell, cell, amountToStretch, sheetIndex * 2 + 1);
            }
            case RecordIdType.SignatureSheetRow:
            {
                var row = await _signatureSheetRowRepository.GetByIdIncludingSheetAndCellsAsync(deficiency.RecordId);
                if (row is null) return null;
                sheetIndex = row.SignatureSheet.SheetIndex;
                return _pdfManipulationService.CropImageToBounds(pdf, row, row, amountToStretch, sheetIndex * 2 + 1);
            }
            case RecordIdType.SignatureSheet:
            {
                if (deficiency.WorkId == null)
                {
                    return null;
                }

                var ssField =
                    await _signatureSheetFieldRepository.GetByWorkIdAsync(deficiency.WorkId);
                if (ssField is null) return null;
                sheetIndex = ssField.SignatureSheet.SheetIndex;
                return _pdfManipulationService.CropImageToBounds(pdf, ssField, ssField,
                    amountToStretch, sheetIndex * 2 + ssField.Page);
            }
            case RecordIdType.None:
            default:
                return null;
        }
    }

    public async Task<bool> DoesDeficiencyExistForRowAsync(SignatureSheetRow row, int ruleId, int matterId)
    {
        var existingDeficiencyies = await
            _deficiencyRepository.GetDeficienciesByRecordAsync(row.Id, RecordIdType.SignatureSheetRow);
        return existingDeficiencyies.Any(d => d.RuleId == ruleId && d.MatterId == matterId);
    }

    public async Task<byte[]> GetDeficiencyCsvStreamAsync(List<Deficiency> deficiencies, int matterId)
    {
        var rules = deficiencies.Select(x => x.Rule).Distinct();
        var rulesHeader = rules.ToDictionary(r => r.Name, r => false);

        var headers = new[]
            {
                nameof(DeficiencyExport.SheetNumber),
                nameof(DeficiencyExport.LineNumber),
                nameof(DeficiencyExport.SignerFirstName),
                nameof(DeficiencyExport.SignerLastName),
                nameof(DeficiencyExport.SignerAddress),
                nameof(DeficiencyExport.SignerVoterId)
            }
            .Concat(rulesHeader.Keys.ToList())
            .Concat(new[] { "Notes" })
            .ToList();

        var csvConfig = new CsvConfiguration(CultureInfo.InvariantCulture)
        {
            ReadingExceptionOccurred = args =>
            {
                if (args.Exception is FieldValidationException)
                    return false;

                return true;
            }
        };

        using var memoryStream = new MemoryStream();
        await using var streamWriter = new StreamWriter(memoryStream);
        var csv = new CsvWriter(streamWriter, csvConfig);

        foreach (var header in headers)
        {
            csv.WriteField(header);
        }

        await csv.NextRecordAsync();

        var deficiencySheetRows = await GetDeficiencySheetRowsAsync(deficiencies);
        var deficiencyCounts = rulesHeader.ToDictionary(rule => rule.Key, rule => 0);

        foreach (var deficiencySheetRow in deficiencySheetRows.OrderBy(d => d.SignatureSheet?.SheetNumber))
        {
            var tableColumns = deficiencySheetRow.TemplateSignatureColumns;
            WriteDeficiencyRow(csv, rulesHeader, deficiencySheetRow, tableColumns, deficiencyCounts);
        }

        WriteDeficiencyTotalRow(csv, rulesHeader, deficiencyCounts);

        streamWriter.Close();
        return memoryStream.ToArray();
    }

    public async Task<List<FullSheetDeficiencyDTO>> GetFullSheetDeficiencyDTOsByMatterAsync(int matterId)
    {
        var responses = new List<FullSheetDeficiencyDTO>();
        var swGetAllByMatterIncludeSheetAsync = Stopwatch.StartNew();
        var deficiencies = await _deficiencyRepository.GetAllByMatterIncludeSheetAsync(matterId);
        swGetAllByMatterIncludeSheetAsync.Stop();
        _logger.LogInformation("GetAllByMatterIncludeSheetAsync for matter {matterId} took {elapsed} ", matterId,
            swGetAllByMatterIncludeSheetAsync.ElapsedMilliseconds);
        var swTranslateToDtosAsync = Stopwatch.StartNew();
        var deficiencyDtos = await TranslateToDtosAsync(deficiencies, matterId, shouldIncludeImages: false);
        swTranslateToDtosAsync.Stop();
        _logger.LogInformation("TranslateToDtosAsync for matter {matterId} took {elapsed} ", matterId,
            swTranslateToDtosAsync.ElapsedMilliseconds);
        var swDeficienciesPerSheet = Stopwatch.StartNew();
        var deficienciesPerSheet = deficiencyDtos
            .Where(x => x?.SignatureSheet?.SheetNumber != null)
            .GroupBy(x => x?.SignatureSheet?.SheetNumber ?? 0)
            .ToDictionary(g => g.Key, g => g.ToList()
                .OrderBy(x => (x.FieldIndex ?? 0) + (x.RowNumber * 100) + (x.ColumnIndex ?? 9)));
        swDeficienciesPerSheet.Stop();
        _logger.LogInformation("Fetched deficiencies per sheet for matter {MatterId} in {elapsed}", matterId,
            swDeficienciesPerSheet.ElapsedMilliseconds);
        var swDtoCreation = Stopwatch.StartNew();
        foreach (var kvp in deficienciesPerSheet)
        {
            var filePath = kvp.Value.First().SignatureSheet?.DeficiencyImageFilePath;
            if (filePath != null)
            {
                var imageSas = await _fileService.CreateSasUrlAsync($"matter{matterId}", filePath);
                if (imageSas != null && imageSas != "Image file does not exists!")
                {
                    responses.Add(new FullSheetDeficiencyDTO
                    {
                        SheetNumber = kvp.Key,
                        ImageUrl = imageSas,
                        SignatureSheetId = kvp.Value.FirstOrDefault()?.SignatureSheet?.Id,
                        DeficiencyBadges = kvp.Value.Select((deficiencyResult, index) =>
                        {
                            var suffix = !deficiencyResult.NeedsReview
                                ? ""
                                : deficiencyResult.IsReviewed == true
                                    ? " (R)" : " (Q)";

                            return new DeficiencyBadge
                            {
                                BadgeDescription =
                                    $"{GetBadgePrefix(deficiencyResult)} - {deficiencyResult.Rule.Name}{suffix}",
                                BadgeNumber = (index + 1).ToString(),
                                DeficiencyId = deficiencyResult.Id,
                                RuleId = deficiencyResult.Rule.Id,
                                WorkId = deficiencyResult.WorkId,
                                UserName = deficiencyResult.UserName,
                                RecordIdType = deficiencyResult.RecordIdType,
                                RecordId = deficiencyResult.RecordId,
                            };
                        }).ToList()
                    });
                }
            }
        }

        swDtoCreation.Stop();
        _logger.LogInformation("Created DTOs for matter {MatterId} in {elapsed}", matterId,
            swDtoCreation.ElapsedMilliseconds);
        return responses;
    }

    private void WriteDeficiencyRow(
        CsvWriter csv,
        Dictionary<string, bool> rulesHeader,
        DeficienciesBySheetAndRow deficiencySheetRow,
        IList<TemplateSignatureColumn> tableColumns,
        Dictionary<string, int> deficiencyCounts
    )
    {
        var nameColumns = tableColumns.Where(x => x.IsName == true);
        var addressColumns = tableColumns.Where(x => x.IsAddress == true);
        var dateColumn = tableColumns.FirstOrDefault(x => x.IsDate == true);
        var addressColumnIndices = addressColumns.Select(col => col.ColumnIndex);

        foreach (var deficiencyRow in deficiencySheetRow.DeficienciesByRow.OrderBy(r => r.SignatureRow?.RowNumber ?? 0))
        {
            var notes = "";
            var sheet = deficiencySheetRow.SignatureSheet;
            var row = deficiencyRow.SignatureRow;
            if (sheet == null || row == null || row.Validity == Validity.Strikethrough)
            {
                continue;
            }

            csv.WriteField(sheet.SheetNumber);
            csv.WriteField(row.RowNumber);

            var result = _signatoryService.GetSignatoryFromRow(tableColumns, row, returnFullAddress: true);
            var signatory = result.Value;
            csv.WriteField(signatory?.FirstName ?? "");
            csv.WriteField(signatory?.LastName ?? "");
            csv.WriteField(signatory?.FullAddress ?? "");
            csv.WriteField(row.RegisteredVoterId);

            var deficienciesByRuleName = deficiencyRow.Deficiencies
                .GroupBy(d => d.Rule.Name, StringComparer.OrdinalIgnoreCase)
                .ToDictionary(g => g.Key, g => g.First(), StringComparer.OrdinalIgnoreCase);
            foreach (var rule in rulesHeader)
            {
                var hasDeficiency = deficienciesByRuleName.TryGetValue(rule.Key, out var deficiency);
                if (hasDeficiency)
                {
                    deficiencyCounts[rule.Key] += 1;
                    csv.WriteField("X");
                    if (!string.IsNullOrWhiteSpace(deficiency?.Note))
                    {
                        notes += $"{(notes.Length > 0 ? "; " : "")}{deficiency.Note}";
                    }
                }
                else
                {
                    csv.WriteField(" ");
                }
            }

            csv.WriteField(notes);
            csv.NextRecord();
        }
    }

    private void WriteDeficiencyTotalRow(
        CsvWriter csv,
        Dictionary<string, bool> rulesHeader,
        Dictionary<string, int> deficiencyCounts
    )
    {
        csv.WriteField(" ");
        csv.WriteField(" ");
        csv.WriteField(" ");
        csv.WriteField(" ");
        csv.WriteField(" ");
        csv.WriteField(" ");

        foreach (var rule in rulesHeader)
        {
            csv.WriteField(deficiencyCounts[rule.Key]);
        }

        csv.NextRecord();
    }

    public async Task UpdateOrDeleteAlreadyReviewedDeficienciesAsync(int matterId)
    {
        await _deficiencyRepository.DeleteReviewedNonDeficienciesAsync(matterId);
        await _deficiencyRepository.UpdateReviewedDeficienciesAsync(matterId);
        await _deficiencyRepository.SaveChangesAsync();
    }

    public async Task CreateRowDeficienciesForSheetDeficiencies(int matterId)
    {
        var deficiencyAndRows = await _deficiencyRepository.GetRowDeficienciesForSheetDeficiencies(matterId);
        foreach (var deficiencyAndRow in deficiencyAndRows)
        {
            var sheetDeficiency = deficiencyAndRow.Deficiency;
            if (sheetDeficiency != null && deficiencyAndRow.SignatureSheetRowIds != null)
                foreach (var rowId in deficiencyAndRow.SignatureSheetRowIds)
                {
                    var rowDeficiency = CreateDeficiency(rowId, RecordIdType.SignatureSheetRow, sheetDeficiency.RuleId,
                        matterId, sheetDeficiency.SignatureSheetId, null);
                    _deficiencyRepository.Add(rowDeficiency);
                }
        }

        await _deficiencyRepository.SaveChangesAsync();
    }

    public async Task<ServiceResult<Deficiency>> AddSheetDeficiencyAsync(int sheetId, int ruleId, int userId,
        string? violationDtoNote)
    {
        var signatureSheet = await _signatureSheetRepository.GetByIdWithRowsAsync(sheetId);
        if (signatureSheet == null)
        {
            return ServiceResult<Deficiency>.Failed($"Signature sheet {sheetId} not found");
        }

        var deficiency = CreateUserDeficiency(
            signatureSheet.Id, RecordIdType.SignatureSheet, ruleId, signatureSheet.MatterId, signatureSheet.Id, userId,
            null, violationDtoNote);
        _deficiencyRepository.Add(deficiency);

        await AddRowDeficienciesForSheetAsync(signatureSheet.MatterId, ruleId, violationDtoNote, signatureSheet);
        await _signatureSheetRepository.SaveChangesAsync();
        return ServiceResult<Deficiency>.Succeeded(deficiency);
    }

    public async Task DeleteSheetDeficiencyAsync(int sheetId, int ruleId)
    {
        var sheetDeficiency =
            await _deficiencyRepository.GetDeficiencyByRecordAndRuleAsync(sheetId, RecordIdType.SignatureSheet, ruleId);
        if (sheetDeficiency == null)
        {
            return; // Not Found
        }

        var rowDeficiencies =
            await _deficiencyRepository.GetRowDeficienciesForSheetDeficiency(sheetDeficiency.MatterId, sheetId, ruleId);
        foreach (var deficiency in rowDeficiencies)
        {
            _deficiencyRepository.Remove(deficiency);
        }

        _deficiencyRepository.Remove(sheetDeficiency);

        await _deficiencyRepository.SaveChangesAsync();
    }

    public async Task<List<RuleExpressionAndWork>> GetRuleExpressionAndWorks(int matterId, Deficiency deficiency)
    {
        var expressions = new[] { deficiency.Rule.LeftHandExpression, deficiency.Rule.RightHandExpression };
        var expressionAndWorks = new List<RuleExpressionAndWork>();
        for (int index = 0; index < expressions.Length; index++)
        {
            var expression = expressions[index];
            var expressionAndWork = new RuleExpressionAndWork
            {
                Expression = expression,
            };
            var matterVariableMatch = RuleExpressionHelper.RegexMatterVariable.Match(expression);
            var fieldMatch = RuleExpressionHelper.RegexSheetFields.Match(expression);
            var cellMatch = RuleExpressionHelper.RegexRowCells.Match(expression);
            if (fieldMatch.Success)
            {
                if (deficiency.RecordIdType == RecordIdType.SignatureSheetField)
                {
                    expressionAndWork.Work = await _workRepository.GetByFieldIdAsync(deficiency.RecordId);
                }
                else
                {
                    var sheet = deficiency.SignatureSheet;
                    sheet.Fields = await _signatureSheetFieldRepository.GetAllBySignatureSheetIdAsync(sheet.Id);
                    var ruleContext = new RuleContext { Rule = deficiency.Rule, Sheet = sheet };
                    expressionAndWork.Work = await _ruleExpressionHelper.GetFieldWorkFromRuleExpressionAsync(ruleContext,
                        sheet.Id,
                        expression);
                }
            }
            else if (cellMatch.Success)
            {
                if (deficiency.RecordIdType == RecordIdType.SignatureSheetCell)
                {
                    expressionAndWork.Work = await _workRepository.GetByCellIdAsync(deficiency.RecordId);
                }
                else if (deficiency.RecordIdType == RecordIdType.SignatureSheetRow)
                {
                    var signatory = await _signatureSheetRowRepository.GetSignatoryByRowIdAsync(deficiency.RecordId);
                    if (signatory != null)
                    {
                        var boundaryPoint = await GetPointName(signatory, deficiency);
                        expressionAndWork.SignatoryPoint = boundaryPoint;
                    }

                    // get all the cells on that row, including Template Column names
                    // and find the one that matches the rule.
                    var cells = await _signatureSheetCellRepository.GetAllByRowIdAsync(deficiency.RecordId);
                    var cell = cells.SingleOrDefault(c =>
                        c.TemplateSignatureColumn.Name == cellMatch.Groups[1].Value);
                    if (cell != null)
                    {
                        expressionAndWork.Work = await _workRepository.GetByCellIdAsync(cell.Id);
                    }
                }
            }
            else if (matterVariableMatch.Success)
            {
                var matterVariable =
                    await _matterVariableRepository.GetByMatterAndNameAsync(matterId,
                        matterVariableMatch.Groups[1].Value);
                expressionAndWork.MatterVariable = _mapper.Map<MatterVariableDTO>(matterVariable);
            }
            else if (expression == "Sheet.Circulator")
            {
                if (deficiency.SignatureSheet.CirculatorId != null)
                {
                    expressionAndWork.Circulator = await _circulatorRepository.GetByIdAsync(
                        deficiency.SignatureSheet.CirculatorId.Value);
                }
            }
            else if (expression == "Row.Signatory" && deficiency.RecordIdType == RecordIdType.SignatureSheetRow)
            {
                var signatory = await _signatureSheetRowRepository.GetSignatoryByRowIdAsync(deficiency.RecordId);
                expressionAndWork.Signatory = signatory;
                if (signatory != null)
                {
                    var boundaryPoint = await GetPointName(signatory, deficiency);
                    expressionAndWork.SignatoryPoint = boundaryPoint;
                }
            }
            else if (deficiency.RecordIdType == RecordIdType.SignatureSheetRow &&
                     deficiency.SignatureSheetId != null)
            {
                var signatory = await _signatureSheetRowRepository.GetSignatoryByRowIdAsync(deficiency.RecordId);
                if (signatory != null)
                {
                    var boundaryPoint = await GetPointName(signatory, deficiency);
                    expressionAndWork.SignatoryPoint = boundaryPoint;
                }

                var sheetIdToUse = deficiency.SignatureSheetId.Value;
                var recordIdToUse = deficiency.RecordId;
                if (index == 0)
                {
                    expressionAndWork.Work = await _workRepository.GetVoterRegistrationWorkByRowIdAsync(
                        matterId,
                        sheetIdToUse,
                        recordIdToUse);
                }
                else // index = 1
                {
                    if (deficiency.OtherRecordIdType == RecordIdType.SignatureSheetRow
                        && deficiency.OtherRecordId != null)
                    {
                        recordIdToUse = deficiency.OtherRecordId.Value;
                        var otherRow = await _signatureSheetRowRepository.GetByIdAsync(recordIdToUse);
                        if (otherRow != null)
                        {
                            sheetIdToUse = otherRow.SignatureSheetId;
                        }

                        expressionAndWork.Work = await _workRepository.GetVoterRegistrationWorkByRowIdAsync(
                            matterId,
                            sheetIdToUse,
                            recordIdToUse);
                    }
                }
            }

            expressionAndWorks.Add(expressionAndWork);
        }

        return expressionAndWorks;
    }

    private async Task<BoundaryPointDTO> GetPointName(Signatory signatory, Deficiency deficiency)
    {
        var boundaryPoint = new BoundaryPointDTO();
        var operation = deficiency.Rule.OperationName;
        var boundaryType = operation.StartsWith("IsZipCodeOutOfBoundsOf")
            ? BoundaryType.ZipCode
            : operation.StartsWith("IsCityOutOfBoundsOf")
                ? BoundaryType.City
                : BoundaryType.Unknown;
        if (operation == "IsAddressLineOutOfBoundsOf" && signatory.Latitude != null && signatory.Longitude != null)
        {
            boundaryPoint.Latitude = signatory.Latitude.Value;
            boundaryPoint.Longitude = signatory.Longitude.Value;
        }

        switch (boundaryType)
        {
            case BoundaryType.ZipCode:
                boundaryPoint.PointName = signatory.PostalCode;
                break;
            case BoundaryType.City:
                boundaryPoint.PointName = signatory.City;
                // set the latitude and longitude using city center from the boundary repository using the city name
                if (signatory.City != null)
                {
                    var boundarPoints = await _boundaryRepository.GetByStateTypeAndNameAsync(
                        signatory.UsStateId, BoundaryType.CityTownPoint, signatory.City);
                    var cityPoint = boundarPoints?.BoundaryPoints.FirstOrDefault();
                    if (cityPoint != null)
                    {
                        boundaryPoint.Latitude = cityPoint.Latitude;
                        boundaryPoint.Longitude = cityPoint.Longitude;
                    }
                }

                break;
        }

        return boundaryPoint;
    }
}