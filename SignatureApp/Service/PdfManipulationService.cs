﻿using BitMiracle.LibTiff.Classic;
using iTextSharp.text;
using iTextSharp.text.pdf;
using Model.Interfaces;
using Model.Rules;
using Model.Templates;
using PDFtoImage;
using Service.ServiceModels;
using BitMiracle.Tiff2Pdf;
using BitMiracle.TiffCP;
using System.Text.RegularExpressions;
using Model.SignatureSheets;
using Image = iTextSharp.text.Image;
using Rectangle = iTextSharp.text.Rectangle;

namespace Service;

public partial class PdfManipulationService
{
    private readonly HttpClient _httpClient;
    private const int PPI = 72;
    private static Regex regexContainsDate = new Regex(@"\bdate\b", RegexOptions.IgnoreCase);
    private static Regex regexContainsCirculator = new Regex(@"\bcirculator\b", RegexOptions.IgnoreCase);
    private static Regex regexContainsId = new Regex(@"\bid\b", RegexOptions.IgnoreCase);
    private static Regex regexContainsInitiative = new Regex(@"\binitiative\b", RegexOptions.IgnoreCase);
    private static Regex regexContainsNumber = new Regex(@"\b(num|number|no)\b", RegexOptions.IgnoreCase);

    public PdfManipulationService(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public bool IsValidPdf(Stream sourceStream)
    {
        bool isValidPdf;
        try
        {
            using PdfReader reader = new PdfReader(sourceStream);
            isValidPdf = true;
        }
        catch (Exception)
        {
            isValidPdf = false;
        }

        return isValidPdf;
    }

    public int GetNumberOfPages(Stream sourceStream)
    {
        using var pdfReader = new PdfReader(sourceStream);
        int numPages = pdfReader.NumberOfPages;
        pdfReader.Close();
        return numPages;
    }

    public Rectangle GetPageSizeWithRotation(Stream sourceStream, int pageNumber)
    {
        using var pdfReader = new PdfReader(sourceStream);
        var pageSize = pdfReader.GetPageSizeWithRotation(pageNumber);
        pdfReader.Close();
        return pageSize;
    }

    public async Task<MemoryStream> AdjustPdfOrientationAsync(Stream sourceStream, SupportedPageSize? supportedPageSize)
    {
        using PdfReader reader = new PdfReader(sourceStream);
        bool needsRotation = false;
        for (int pageNum = 1; pageNum <= reader.NumberOfPages; pageNum++)
        {
            var pageSize = reader.GetPageSizeWithRotation(pageNum);
            if (pageSize.Rotation != 0)
            {
                needsRotation = true;
                break;
            }
        }

        sourceStream.Seek(0, SeekOrigin.Begin);

        var outputStream = new MemoryStream();
        if (!needsRotation)
        {
            await sourceStream.CopyToAsync(outputStream);
            outputStream.Position = 0;
            return outputStream;
        }

        // otherwise we do need to rotate
        Rectangle portraitPageSize = supportedPageSize == SupportedPageSize.Letter ? PageSize.Letter : PageSize.Legal;
        var landscapeRect = new Rectangle(urx: portraitPageSize.Height, ury: portraitPageSize.Width);
        var doc = new Document(landscapeRect);
        using PdfWriter writer = PdfWriter.GetInstance(doc, outputStream);
        doc.Open();
        for (int pageNum = 1; pageNum <= reader.NumberOfPages; pageNum++)
        {
            var pageSize = reader.GetPageSizeWithRotation(pageNum);
            doc.NewPage();
            var page = writer.GetImportedPage(reader, pageNum);
            Image img = Image.GetInstance(page);
            if (pageSize.Rotation != 0)
            {
                img.RotationDegrees = 0 - pageSize.Rotation;
            }

            img.SetAbsolutePosition(0, 0);
            doc.Add(img);
        }

        doc.Close();
        writer.Close();
        reader.Close();
        outputStream.Position = 0;
        return outputStream;
    }

    public async Task<Stream?> ConvertTwoTiffImagesToSinglePdfAsync(Stream frontPageTiffStream,
        Stream backPageTiffStream)
    {
        var combinedTiffStream = await ConvertTiffImagesToSingleTiffAsync([frontPageTiffStream, backPageTiffStream]);
        if (combinedTiffStream != null)
        {
            var pdfStream = await ConvertTiffToPdfAsync(combinedTiffStream);
            return pdfStream;
        }

        return null;
    }

    public async Task<Stream?> ConvertTiffImagesToSingleTiffAsync(Stream[] streams)
    {
        string outFilename = "out";
        var outputMemStream = new MemoryStream();
        var c = new Copier();

        FillOrder defaultFillOrder = 0;
        int defaultTileLength = -1;
        PlanarConfig defaultPlanarConfig = PlanarConfig.UNKNOWN;
        int defaultRowsPerStrip = 0;
        int defaultTileWidth = -1;


        Tiff outImage = Tiff.ClientOpen(outFilename, "w", outputMemStream, new TiffStream());
        int pageNumber = 1;
        foreach (var inputStream in streams)
        {
            var inputMemStream = new MemoryStream();
            await inputStream.CopyToAsync(inputMemStream);
            inputMemStream.Position = 0;
            using (Tiff inImage = Tiff.ClientOpen($"in{pageNumber}", "r", inputMemStream, new TiffStream()))
            {
                if (inImage == null)
                    return null;

                int initialPage = 0;
                int pageNumPos = 1;

                int totalPages = inImage.NumberOfDirectories();
                for (int i = initialPage; i < totalPages;)
                {
                    c.m_config = defaultPlanarConfig;
                    c.m_compression = c.m_defcompression;
                    c.m_predictor = c.m_defpredictor;
                    c.m_fillorder = defaultFillOrder;
                    c.m_rowsperstrip = defaultRowsPerStrip;
                    c.m_tilewidth = defaultTileWidth;
                    c.m_tilelength = defaultTileLength;
                    c.m_g3opts = c.m_defg3opts;

                    if (!inImage.SetDirectory((short)i))
                    {
                        return null;
                    }

                    if (!c.Copy(inImage, outImage) || !outImage.WriteDirectory())
                        return null;

                    // if we have at least one page specifier and current specifier is not empty.
                    // specifier is empty when trailing separator used like this: "file,num,"
                    if (pageNumPos < streams.Length)
                    {
                        // move to next page specifier
                        pageNumPos++;

                        if (pageNumPos >= streams.Length)
                        {
                            break;
                        }
                    }
                    else
                    {
                        // we have no page specifiers or current page specifier is empty
                        // just move to the next page
                        i++;
                    }
                }
            }
        }

        outputMemStream.Position = 0;
        return outputMemStream;
    }

    private async Task<Stream?> ConvertTiffToPdfAsync(Stream inputStream)
    {
        T2P t2p = new T2P();
        string inputFilename = "in";
        string outFilename = "out";
        t2p.m_outputfile = new MemoryStream();

        using (Tiff output = Tiff.ClientOpen(outFilename, "w", t2p, t2p.m_stream))
        {
            using (Tiff input = Tiff.ClientOpen(inputFilename, "r", inputStream, new TiffStream()))
            {
                if (input == null)
                {
                    // error
                    return await Task.FromResult<Stream?>(Stream.Null);
                }

                t2p.validate();

                object client = output.Clientdata();
                TiffStream outputStream = output.GetStream();
                outputStream.Seek(client, 0, SeekOrigin.Begin);

                t2p.write_pdf(input, output);
                if (t2p.m_error)
                {
                    return await Task.FromResult<Stream?>(Stream.Null);
                }
            }
        }

        t2p.m_outputfile.Position = 0;
        return await Task.FromResult(t2p.m_outputfile);
    }

    public async Task SplitIntoDifferentStreamsAsync(Stream sourceStream, Func<Stream, int, Task> actionOnOutputStream)
    {
        using PdfReader sourceReader = new PdfReader(sourceStream);
        try
        {
            for (int pageNum = 1; pageNum <= sourceReader.NumberOfPages; pageNum += 2)
            {
                using Document document = new Document();
                MemoryStream outStream = new MemoryStream();
                using PdfCopy copy = new PdfCopy(document, outStream);
                document.Open();
                PdfImportedPage importedFront = copy.GetImportedPage(sourceReader, pageNum);
                copy.AddPage(importedFront);
                if (pageNum + 1 <= sourceReader.NumberOfPages)
                {
                    PdfImportedPage importedBack = copy.GetImportedPage(sourceReader, pageNum + 1);
                    copy.AddPage(importedBack);
                }

                document.Close();
                outStream.Position = 0;
                try
                {
                    await actionOnOutputStream(outStream, pageNum / 2);
                }
                finally
                {
                    await outStream.DisposeAsync();
                }
            }
        }
        finally
        {
            sourceReader.Close();
        }
    }

    // public void FixBrokenForm(Stream sourceStream, Stream outputStream)
    // {
    //     PdfReader reader = new PdfReader(sourceStream);
    //     PdfDictionary root = reader.Catalog;
    //     PdfDictionary form = root.GetAsDict(PdfName.Acroform);
    //     PdfArray fields = form.GetAsArray(PdfName.Fields);
    //
    //     PdfDictionary page;
    //     PdfArray annots;
    //     for (int i = 1; i <= reader.NumberOfPages; i++)
    //     {
    //         page = reader.GetPageN(i);
    //         annots = page.GetAsArray(PdfName.Annots);
    //         for (int j = 0; j < annots.Size; j++)
    //         {
    //             fields.Add(annots.GetAsIndirectObject(j));
    //         }
    //     }
    //
    //     var stamper = new PdfStamper(reader, outputStream);
    //     stamper.Close();
    //     reader.Close();
    // }

    public List<TranscribableField> GetListOfLabeledFields(Stream sourceStream)
    {
        var fieldsInForm = new List<TranscribableField>();
        var pdfReader = new PdfReader(sourceStream);
        var pageSize = pdfReader.GetPageSizeWithRotation(1);
        var height = pageSize.Height / PPI;
        var width = pageSize.Width / PPI;
        AcroFields af = pdfReader.AcroFields;

        foreach (KeyValuePair<string, AcroFields.Item> field in af.Fields)
        {
            var positions = af.GetFieldPositions(field.Key.ToString());
            var page = (int)positions[0];
            var llx = positions[1] / PPI;
            var lly = height - (positions[2] / PPI);
            var urx = positions[3] / PPI;
            var ury = height - (positions[4] / PPI);
            var name = field.Key;
            var isCirculatorId = !string.IsNullOrEmpty(name) && regexContainsCirculator.IsMatch(name) &&
                                 regexContainsId.IsMatch(name);
            var isInitiativeNumber = !string.IsNullOrEmpty(name) && regexContainsInitiative.IsMatch(name) &&
                                     regexContainsNumber.IsMatch(name);
            var transcribableField = new TranscribableField
            {
                PageNumber = page,
                Name = name,
                IsHandwritten = true,
                IsSignature = name?.Contains("Signature", StringComparison.InvariantCultureIgnoreCase) == true,
                IsDate = name != null && regexContainsDate.IsMatch(name),
                IsPrefixed = isCirculatorId || isInitiativeNumber,
                Prefix = isCirculatorId ? "AZ" : isInitiativeNumber ? "I-" : null,
                Left = (decimal)llx,
                Top = (decimal)ury,
                Right = (decimal)urx,
                Bottom = (decimal)lly,
            };
            fieldsInForm.Add(transcribableField);
        }

        // Order the fields from Top to bottom and Left to Right
        return fieldsInForm
            .OrderBy(ctf => ctf.PageNumber)
            .ThenBy(ctf => ctf.Top)
            .ThenBy(ctf => ctf.Left)
            .ToList();
    }

    public byte[]? HighlightDeficiencies(
        MemoryStream sourceStream,
        IEnumerable<DeficiencyInfo> deficiencies,
        int pageNumber,
        bool returnImage,
        int dpi = 300, int? newWidth = null, int? newHeight = null)
    {
        using var destinationStream = new MemoryStream();
        sourceStream.Position = 0;
        var pdfReader = new PdfReader(sourceStream);
        Rectangle pageSize;
        try
        {
            pageSize = pdfReader.GetPageSizeWithRotation(pageNumber); // Should be hardcoded to 1?
        }
        catch (Exception) // we are assuming page does not exist
        {
            return null;
        }

        var originalHeight = (decimal)(pageSize.Height / PPI);
        var originalWidth = (decimal)(pageSize.Width / PPI);

        var pageRange = $"{pageNumber}:{pageNumber}";
        pdfReader.SelectPages(pageRange);

        using var stamper = new PdfStamper(pdfReader, destinationStream);
        var layer = new PdfLayer("Layer", stamper.Writer);

        var canvas = stamper.GetOverContent(1);

        foreach (var deficiency in deficiencies)
        {
            if (deficiency.Bounds == null)
            {
                continue;
            }

            float annotationLLX;
            float annotationLLY;
            var badgeText = deficiency.BadgeNumber;
            //var numberOfDigits = deficiency.BadgeNumber.ToString().Length;
            var deficiencyHeight = deficiency.Bounds.Bottom - deficiency.Bounds.Top;
            var annotationHeight = deficiencyHeight / 2.0m;
            var annotationHeightInPoints = (float)(annotationHeight * PPI);
            var fontHeightInPoints = (int)Math.Ceiling(annotationHeightInPoints) - 2;
            BaseFont bf = BaseFont.CreateFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED);
            var annotationWidthInPoints = (int)Math.Ceiling(bf.GetWidthPoint(badgeText, fontHeightInPoints));

            var thisRowDeficiencies = deficiencies.Where(d => d.RecordIdType == RecordIdType.SignatureSheetRow
                                                              && d.RecordId == deficiency.RecordId).ToList();
            var rowDeficiencyIndex = deficiency.RecordIdType == RecordIdType.SignatureSheetRow
                ? thisRowDeficiencies.FindIndex(d => d.BadgeNumber == deficiency.BadgeNumber)
                : -1;

            // is this a cell?
            if (deficiency.RecordIdType == RecordIdType.SignatureSheetCell)
            {
                annotationLLX = (float)(deficiency.Bounds.Right * PPI) - annotationWidthInPoints;
                annotationLLY = (float)((originalHeight - deficiency.Bounds.Top) * PPI) -
                                annotationHeightInPoints;
            }
            else if (rowDeficiencyIndex >= 0)
            {
                if (rowDeficiencyIndex % 2 == 0) // even on the left
                {
                    annotationLLX = (float)(deficiency.Bounds.Left * PPI) - annotationWidthInPoints;
                }
                else // odd on the right
                {
                    annotationLLX = (float)(deficiency.Bounds.Right * PPI);
                }

                // first two on the top
                if (rowDeficiencyIndex / 2 == 0)
                {
                    annotationLLY = (float)((originalHeight - deficiency.Bounds.Top) * PPI) -
                                    annotationHeightInPoints;
                }
                else
                {
                    annotationLLY = (float)((originalHeight - deficiency.Bounds.Bottom) * PPI);
                }
            }
            else
            {
                annotationLLX = (float)(deficiency.Bounds.Right * PPI);
                annotationLLY = (float)((originalHeight - deficiency.Bounds.Bottom) * PPI);
            }

            var annotationURX = annotationLLX + annotationWidthInPoints;
            var annotationURY = annotationLLY + annotationHeightInPoints;

            var annotationRect = new Rectangle(annotationLLX, annotationLLY, annotationURX, annotationURY);
            DrawRectangle(annotationRect, 1.0f, BaseColor.Red, canvas);

            var contentByte = stamper.GetOverContent(1);
            contentByte.BeginLayer(layer);
            contentByte.SetColorFill(BaseColor.White);
            contentByte.SetFontAndSize(bf, (int)annotationHeightInPoints - 2);
            contentByte.BeginText();
            contentByte.ShowTextAligned(PdfContentByte.ALIGN_LEFT, deficiency.BadgeNumber, annotationLLX + 1,
                annotationLLY + 1, rotation: 0);
            contentByte.EndText();
            contentByte.EndLayer();

            // highlight the deficiency bounds
            var rectangle = ConvertBoundsToRectangle(deficiency.Bounds, originalHeight);
            DrawRectangle(rectangle, 0.2f, BaseColor.Orange, canvas);
        }

        stamper.Close();
        pdfReader.Close();

        if (!returnImage)
        {
            return destinationStream.ToArray();
        }

        //We want to respect the original image Aspect Ratio, but stay within the new width/height bounds.
        var widthRatio = originalWidth / newWidth;
        var heightRatio = originalHeight / newHeight;
        var largerRatio = widthRatio > heightRatio ? widthRatio : heightRatio;

        return ConvertPdfToImage(destinationStream, dpi, (int)(originalWidth / largerRatio)!,
            (int)(originalHeight / largerRatio)!);
    }

    public byte[] HighlightAreaOnPdf(Stream sourceStream, IHaveSimpleBounds field, int pageNumber)
    {
        using var destinationStream = new MemoryStream();
        var pdfReader = new PdfReader(sourceStream);
        var pageSize = pdfReader.GetPageSizeWithRotation(pageNumber);
        var height = (decimal)(pageSize.Height / PPI);

        var pageRange = $"{pageNumber}:{pageNumber}";
        pdfReader.SelectPages(pageRange);

        var stamper = new PdfStamper(pdfReader, destinationStream);
        var canvas = stamper.GetOverContent(1);

        var rectangle = ConvertBoundsToRectangle(field, height);
        DrawRectangle(rectangle, 0.3f, BaseColor.Green, canvas);

        stamper.Close();
        pdfReader.Close();

        return ConvertPdfToImage(destinationStream);
    }

    public byte[] DrawTableOnPdf(Stream sourceStream, SignatureSheetTable table, int pageNumber = 1)
    {
        using var destinationStream = new MemoryStream();
        using var pdfReader = new PdfReader(sourceStream);
        var pageSize = pdfReader.GetPageSizeWithRotation(pageNumber);
        var pageHeight = (decimal)(pageSize.Height / PPI);

        var pageRange = $"{pageNumber}:{pageNumber}";
        pdfReader.SelectPages(pageRange);

        using var stamper = new PdfStamper(pdfReader, destinationStream);
        var canvas = stamper.GetOverContent(pageNumber);

        foreach (var row in table.Rows)
        {
            var rectangleHeight = 5.0m / 72.0m;
            var rectangleForTop = ConvertBoundsToRectangle(
                new CellBounds
                    { Left = row.Left, Top = row.Top, Right = row.Right, Bottom = row.Top + rectangleHeight },
                pageHeight);
            DrawRectangle(rectangleForTop, 1.0f, BaseColor.Green, canvas);

            var rectangleForBottom = ConvertBoundsToRectangle(
                new CellBounds
                    { Left = row.Left, Top = row.Bottom - rectangleHeight, Right = row.Right, Bottom = row.Bottom },
                pageHeight);
            DrawRectangle(rectangleForBottom, 1.0f, BaseColor.Green, canvas);
        }

        foreach (var col in table.Columns)
        {
            var rectangleWidth = 5.0m / 72.0m;
            var rectangleForLeft = ConvertBoundsToRectangle(
                new CellBounds { Left = col.Left, Top = col.Top, Right = col.Left + rectangleWidth, Bottom = col.Top },
                pageHeight);
            DrawRectangle(rectangleForLeft, 1.0f, BaseColor.Red, canvas);

            var rectangleForRight = ConvertBoundsToRectangle(
                new CellBounds
                    { Left = col.Right - rectangleWidth, Top = col.Top, Right = col.Right, Bottom = col.Bottom },
                pageHeight);
            DrawRectangle(rectangleForRight, 1.0f, BaseColor.Red, canvas);
        }

        stamper.Close();
        pdfReader.Close();

        return ConvertPdfToImage(destinationStream);
    }


    public byte[] CropImageToBounds(Stream sourceStream,
        IHaveSimpleBounds cropBounds,
        IHaveSimpleBounds highlightBounds,
        IHaveSimpleBounds amountToStretch,
        int pageNumber)
    {
        var pdfReader = new PdfReader(sourceStream);
        var pageSize = pdfReader.GetPageSizeWithRotation(pageNumber);
        var pageHeight = (decimal)(pageSize.Height / PPI);
        var pageWidth = (decimal)(pageSize.Width / PPI);

        var pdfDestinationStream = new MemoryStream();

        var pageRange = $"{pageNumber}:{pageNumber}";
        pdfReader.SelectPages(pageRange);

        var stamper = new PdfStamper(pdfReader, pdfDestinationStream);

        var cropLLX = cropBounds.Left * PPI;
        var cropLLY = (pageHeight - cropBounds.Bottom) * PPI; //height -  * PPI
        var cropURX = cropBounds.Right * PPI;
        var cropURY = (pageHeight - cropBounds.Top) * PPI;

        var highlightedArea = ConvertBoundsToRectangle(new CellBounds
        {
            Left = highlightBounds.Left, Bottom = highlightBounds.Bottom,
            Right = highlightBounds.Right, Top = highlightBounds.Top
        }, pageHeight);
        var canvas = stamper.GetOverContent(1);
        DrawRectangle(highlightedArea, 0.3f, BaseColor.Green, canvas);

        var stretchLeft = (cropURX - cropLLX) * amountToStretch.Left;
        var stretchRight = (cropURX - cropLLX) * amountToStretch.Right;
        var stretchTop = (cropURY - cropLLY) * amountToStretch.Top;
        var stretchBottom = (cropURY - cropLLY) * amountToStretch.Bottom;
        var llxStretch = Math.Max(cropLLX - stretchLeft, 0m);
        var llyStretch = Math.Max(cropLLY - stretchBottom, 0m);
        var urxStretch = Math.Min(cropURX + stretchRight, pageWidth * PPI);
        var uryStretch = Math.Min(cropURY + stretchTop, pageHeight * PPI);
        var rect = new PdfRectangle((float)llxStretch, (float)llyStretch, (float)urxStretch, (float)uryStretch);

        var pdfDictionary = pdfReader.GetPageN(1);
        pdfDictionary.Put(PdfName.Cropbox, rect);

        stamper.Close();
        pdfReader.Close();

        return ConvertPdfToImage(pdfDestinationStream);
    }

    public byte[] CropImageToBounds(Stream sourceStream,
        List<IHaveSimpleBounds> cropToBounds,
        List<IHaveSimpleBounds> highlightBounds,
        IHaveSimpleBounds amountToStretch,
        int pageNumber)
    {
        var cropLeft = cropToBounds.Select(b => b.Left).DefaultIfEmpty().Min();
        var cropTop = cropToBounds.Select(b => b.Top).DefaultIfEmpty().Min();
        var cropRight = cropToBounds.Select(b => b.Right).DefaultIfEmpty().Max();
        var cropBottom = cropToBounds.Select(b => b.Bottom).DefaultIfEmpty().Max();
        var cropBounds = new CellBounds
        {
            Left = cropLeft,
            Top = cropTop,
            Right = cropRight,
            Bottom = cropBottom
        };

        var highlightLeft = highlightBounds.Select(b => b.Left).DefaultIfEmpty().Min();
        var highlightTop = highlightBounds.Select(b => b.Top).DefaultIfEmpty().Min();
        var highlightRight = highlightBounds.Select(b => b.Right).DefaultIfEmpty().Max();
        var highlightBottom = highlightBounds.Select(b => b.Bottom).DefaultIfEmpty().Max();
        var highlightBoundsCombined = new CellBounds
        {
            Left = highlightLeft,
            Top = highlightTop,
            Right = highlightRight,
            Bottom = highlightBottom
        };

        return CropImageToBounds(sourceStream, cropBounds, highlightBoundsCombined, amountToStretch, pageNumber);
    }

    public byte[] ConvertPdfPageToImage(Stream pdfFile, SupportedPageSize supportedPageSize, int pageNumber = 1)
    {
        using var pdfReader = new PdfReader(pdfFile);
        var pageRange = $"{pageNumber}:{pageNumber}";
        pdfReader.SelectPages(pageRange);
        var pdfDestinationStream = new MemoryStream();
        var stamper = new PdfStamper(pdfReader, pdfDestinationStream);
        stamper.Close();
        pdfReader.Close();
        Rectangle pageSize = supportedPageSize == SupportedPageSize.Letter ? PageSize.Letter : PageSize.Legal;
        // NOTE these pageSizes are in portrait mode, so we reverse them
        return ConvertPdfToImage(pdfDestinationStream, 300, (int)pageSize.Height, (int)pageSize.Width);
    }

    private Rectangle ConvertBoundsToRectangle(IHaveSimpleBounds bounds, decimal pageHeight)
    {
        var left = (float)(bounds.Left * PPI);
        var top = (float)((pageHeight - bounds.Top) * PPI);
        var right = (float)(bounds.Right * PPI);
        var bottom = (float)((pageHeight - bounds.Bottom) * PPI);
        return new Rectangle(left, bottom, right, top);
    }

    private byte[] ConvertPdfToImage(MemoryStream destinationStream, int dpi = 300, int? width = null,
        int? height = null)
    {
        if (!OperatingSystem.IsLinux() && !OperatingSystem.IsWindows() && !OperatingSystem.IsMacOS())
        {
            return new byte[0];
        }

        SkiaSharp.SKBitmap? skBitmap;
        if (height is not null && width is not null)
        {
            skBitmap = Conversion.ToImage(destinationStream, dpi: dpi, height: height, width: width);
        }
        else
        {
            skBitmap = Conversion.ToImage(destinationStream, dpi: dpi);
        }

        destinationStream.Close();

        if (skBitmap is null)
        {
            return new byte[0];
        }

        using var skData = skBitmap.Encode(SkiaSharp.SKEncodedImageFormat.Png, 100); // quality is ignored for PNG
        if (skData == null)
        {
            return new byte[0];
        }

        return skData.ToArray();
    }

    private void DrawRectangle(Rectangle rectangle, float opacity, BaseColor color, PdfContentByte canvas)
    {
        rectangle.BackgroundColor = color;
        PdfGState state = new PdfGState();
        state.FillOpacity = opacity;
        canvas.SetGState(state);
        canvas.Rectangle(rectangle);
        canvas.Fill();
    }

    public MemoryStream GenerateHelloWorldPdf()
    {
        var memoryStream = new MemoryStream();
        using var document = new Document(PageSize.A4, 25, 25, 30, 30);
        using var writer = PdfWriter.GetInstance(document, memoryStream);
        document.Open();
        var normalFont = FontFactory.GetFont(FontFactory.HELVETICA, 12);
        document.Add(new Paragraph("Hello World", normalFont));
        document.Close();
        memoryStream.Position = 0;
        return memoryStream;
    }

    public async Task<MemoryStream> GeneratePdfDocumentForSheetDeficiencies(
        List<FullSheetDeficiencyDTO> fullSheetDeficiencies)
    {
        var memoryStream = new MemoryStream();
        using var document = new Document(PageSize.A4, 25, 25, 30, 30);
        using var writer = PdfWriter.GetInstance(document, memoryStream);
        document.Open();

        var titleFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 14);
        var normalFont = FontFactory.GetFont(FontFactory.HELVETICA, 12);

        foreach (var sheetDeficiencyDto in fullSheetDeficiencies)
        {
            document.Add(new Paragraph($"Sheet Number: {sheetDeficiencyDto.SheetNumber}", titleFont));
            document.Add(Chunk.Newline);

            // Add image if present
            if (!string.IsNullOrEmpty(sheetDeficiencyDto.ImageUrl))
            {
                try
                {
                    var imageData = await _httpClient.GetByteArrayAsync(sheetDeficiencyDto.ImageUrl);
                    var image = Image.GetInstance(imageData);
                    image.ScaleToFit(400f, 300f);
                    image.Alignment = Element.ALIGN_CENTER;
                    document.Add(image);
                }
                catch
                {
                    document.Add(new Paragraph("[Could not load image]", normalFont));
                }

                document.Add(Chunk.Newline);
            }

            // Add badges
            foreach (var badge in sheetDeficiencyDto.DeficiencyBadges)
            {
                var badgeText = $"{badge.BadgeNumber}: {badge.BadgeDescription}";
                document.Add(new Paragraph(badgeText, normalFont));
            }

            document.Add(new Paragraph("--------------------------------------------------", normalFont));
            document.Add(Chunk.Newline);
        }

        document.Close();
        memoryStream.Position = 0;
        return memoryStream;
    }
}