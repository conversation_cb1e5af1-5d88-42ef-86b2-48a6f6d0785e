﻿using DataInterface.RepositoryInterfaces;
using DataInterface.ServiceInterfaces;
using Microsoft.Extensions.Logging;
using Model.SignatureSheets;
using Model.Templates;
using Model.Workflow;
using Service.ServiceModels;
using Task = Model.Workflow.Task;

namespace Service;

public class WorkGenerationService
{
    private readonly ITranscribableFieldRepository _transcribableFieldRepository;
    private readonly ISignatureSheetRepository _signatureSheetRepository;
    private readonly ITaskRepository _taskRepository;
    private readonly IWorkRepository _workRepository;
    private readonly ILogger<WorkGenerationService> _logger;

    public WorkGenerationService(
        ITranscribableFieldRepository transcribableFieldRepository,
        ISignatureSheetRepository signatureSheetRepository,
        ITaskRepository taskRepository,
        IWorkRepository workRepository,
        ILogger<WorkGenerationService> logger)
    {
        _transcribableFieldRepository = transcribableFieldRepository;
        _signatureSheetRepository = signatureSheetRepository;
        _taskRepository = taskRepository;
        _workRepository = workRepository;
        _logger = logger;
    }

    public async Task<ServiceResult<string>> SaveWorkFromSignatureSheet(int templateId, int matterId,
        SignatureSheet signatureSheet, WorkStatus? workStatus = null)
    {
        var tasks = await _taskRepository.GetAllByTemplateAndMatterId(templateId, matterId);

        var sheetReviewTask = tasks.FirstOrDefault(x =>
            x.TaskType == TaskType.SheetReview);
        if (sheetReviewTask != null)
        {
            var work = new Work
            {
                TaskId = sheetReviewTask.Id,
                WorkStatus = WorkStatus.None,
                LastStartDateTime = DateTime.UtcNow,
                LastStopDateTime = DateTime.UtcNow,
                SecondsWorked = 0,
                MatterId = matterId,
                SignatureSheetId = signatureSheet.Id,
                SheetNumber = signatureSheet.SheetNumber,
            };
            if (workStatus != null)
            {
                work.WorkStatus = workStatus.Value;
            }

            _workRepository.Add(work);
        }

        // Get the transcribable fields
        var transcribableFields = await _transcribableFieldRepository.GetAllByTemplateIdAsync(templateId);
        var idToTranscribableFields = transcribableFields.ToDictionary(tf => tf.Id, tf => tf);

        var signatureSheetFields = signatureSheet.Fields.Where(f => !f.IsMissing).ToList();
        foreach (var field in signatureSheetFields)
        {
            AddWorkFromSignatureSheetField(tasks, idToTranscribableFields, field, signatureSheet.Fields, matterId,
                signatureSheet.Id, workStatus);
        }

        var signatureSheetRows = signatureSheet.Rows.Where(r => !r.IsMissing).ToList();
        foreach (var row in signatureSheetRows)
        {
            var addWorkResult = AddWorkFromSignatureRow(tasks, row, matterId, signatureSheet.Id, workStatus);
            if (!addWorkResult.IsSuccess)
            {
                return addWorkResult;
            }
        }

        // Note if there are 57 sheets this is doing 57 saves
        await _signatureSheetRepository.SaveChangesAsync();
        return ServiceResult<string>.Succeeded(string.Empty);
    }

    public void AddWorkFromSignatureSheetField(IEnumerable<Task> tasks,
        Dictionary<int, TranscribableField> idToTranscribableFields,
        SignatureSheetField sheetField,
        IList<SignatureSheetField> sheetFields,
        int matterId,
        int signatureSheetId,
        WorkStatus? workStatus = null)
    {
        var task = tasks.FirstOrDefault(x =>
            x.TaskType == TaskType.TranscribableField &&
            x.DeserializedFieldIds.Contains(sheetField.TranscribableFieldId));

        if (task is not null)
        {
            var work = new Work
            {
                TaskId = task.Id,
                WorkStatus = WorkStatus.None,
                LastStartDateTime = DateTime.UtcNow,
                LastStopDateTime = DateTime.UtcNow,
                SecondsWorked = 0,
                MatterId = matterId,
                SignatureSheetId = signatureSheetId,
                SheetNumber = sheetField.SignatureSheet.SheetNumber,
                FieldNumber = sheetField.TranscribableField.FieldNumber,
            };
            if (workStatus != null)
            {
                work.WorkStatus = workStatus.Value;
            }

            if (!idToTranscribableFields.TryGetValue(sheetField.TranscribableFieldId, out var transcribableField))
            {
                return;
            }

            var isHandWritten = transcribableField?.IsHandwritten;
            if (isHandWritten == true)
            {
                _workRepository.Add(work);
                _logger.LogInformation(
                    $"Added work for field {sheetField.TranscribableFieldId} work sheet {signatureSheetId} and task {task.Id}");

                work.WorkFields =
                    CreateWorkFieldsFromSignatureSheetField(work, sheetField, sheetFields, idToTranscribableFields);
            }
        }
    }

    private ServiceResult<string> AddWorkFromSignatureRow(IEnumerable<Task> tasks,
        SignatureSheetRow row,
        int matterId,
        int signatureSheetId,
        WorkStatus? workStatus = null)
    {
        foreach (var task in tasks)
        {
            if (task.TaskType == TaskType.TranscribableField
                || task.TaskType == TaskType.SheetReview)
            {
                continue;
            }

            if (task.FirstColumnIndex is null || task.LastColumnIndex is null)
            {
                continue;
            }

            var hasNonMissingCells = Enumerable.Range((int)task.FirstColumnIndex, (int)task.LastColumnIndex - (int)task.FirstColumnIndex + 1)
                .Any(columnIndex => row.Cells.Any(c => c.ColumnIndex == columnIndex && !c.IsMissing));
            if (!hasNonMissingCells)
            {
                continue;
            }

            var work = new Work
            {
                TaskId = task.Id,
                WorkStatus = WorkStatus.None,
                LastStartDateTime = DateTime.UtcNow,
                LastStopDateTime = DateTime.UtcNow,
                SecondsWorked = 0,
                MatterId = matterId,
                SignatureSheetId = signatureSheetId,
                SheetNumber = row.SignatureSheet.SheetNumber,
                RowNumber = row.RowNumber,
            };
            if (workStatus != null)
            {
                work.WorkStatus = workStatus.Value;
            }

            _workRepository.Add(work);

            var signatureSheetCells = row.Cells.Where(c => !c.IsMissing).ToList();
            var fields = CreateWorkFieldsFromSignatureTable(work, signatureSheetCells, task);
            if (fields is null)
            {
                return ServiceResult<string>.Failed(
                    $"No fields for work sheet {signatureSheetId} row {row.RowNumber} and task {task.Id}");
            }

            work.WorkFields = fields;
        }

        return ServiceResult<string>.Succeeded("Added work from signature row");
    }

    private List<WorkField> CreateWorkFieldsFromSignatureSheetField(
        Work work, SignatureSheetField sheetField,
        IList<SignatureSheetField> sheetFields,
        Dictionary<int, TranscribableField> idToTranscribableFields)
    {
        var transcribableField = idToTranscribableFields[sheetField.TranscribableFieldId];
        if (!string.IsNullOrEmpty(transcribableField.GroupName))
        {
            var fieldsInGroup = idToTranscribableFields
                .Where(kvp => kvp.Value.GroupName == transcribableField.GroupName);
            var workFields = fieldsInGroup
                .Select(kvp => new WorkField
                {
                    Work = work,
                    FieldType = FieldType.SignatureSheetField,
                    FieldId = sheetFields.Single(sf => sf.TranscribableFieldId == kvp.Key).Id
                })
                .ToList();
            foreach (var field in fieldsInGroup)
            {
                idToTranscribableFields.Remove(field.Key);
            }

            return workFields;
        }

        return
        [
            new WorkField
            {
                Work = work, FieldType = FieldType.SignatureSheetField, FieldId = sheetField.Id
            }
        ];
    }

    private List<WorkField>? CreateWorkFieldsFromSignatureTable(Work work,
        List<SignatureSheetCell> row, Task task)
    {
        var results = new List<WorkField>();

        if (task.FirstColumnIndex is null) return null;

        for (int index = (int)task.FirstColumnIndex; index <= task.LastColumnIndex; index++)
        {
            var cell = row.FirstOrDefault(x => x.ColumnIndex == index);
            if (cell is not null)
            {
                results.Add(new WorkField
                {
                    FieldId = cell.Id, FieldType = FieldType.SignatureSheetCell, Work = work
                });
            }
        }

        return results;
    }
}