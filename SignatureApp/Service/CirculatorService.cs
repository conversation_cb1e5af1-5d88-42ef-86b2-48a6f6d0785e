using DataInterface.RepositoryInterfaces;
using DataInterface.ServiceInterfaces;
using Model.ExternalDataSources;
using Model.Geocoding;
using Model.SignatureSheets;
using Model.Templates;

namespace Service;

public class CirculatorService
{
    private readonly ICirculatorRepository _circulatorRepository;
    private readonly ITranscribableFieldRepository _transcribableFieldRepository;

    public CirculatorService(
        ICirculatorRepository circulatorRepository,
        ITranscribableFieldRepository transcribableFieldRepository)

    {
        _circulatorRepository = circulatorRepository;
        _transcribableFieldRepository = transcribableFieldRepository;
    }

    internal ServiceResult<Circulator> GetCirculatorFromSignatureSheet(
        IList<TranscribableField> transcribableFields,
        SignatureSheet signatureSheet)
    {
        var circulator = new Circulator();
        var notNullFields = transcribableFields.Where(tf => tf.Name != null).ToList();
        ServiceResult<Circulator> result;

        result = SetCirculatorNameFields(signatureSheet, circulator, notNullFields);
        if (!result.IsSuccess || result.Value == null)
        {
            return result;
        }

        circulator = result.Value;
        result = SetCirculatorRegistrationIdFields(signatureSheet, circulator, notNullFields);
        if (!result.IsSuccess || result.Value == null)
        {
            return result;
        }

        circulator = result.Value;
        result = SetCirculatorAddressFields(signatureSheet, circulator, notNullFields);

        return result;
    }


    public async Task<ServiceResult<Circulator>> UpdateCirculatorFromSheetAsync(SignatureSheet signatureSheet,
        IList<TranscribableField>? transcribableFields = null)
    {
        if (transcribableFields == null)
        {
            transcribableFields =
                await _transcribableFieldRepository.GetAllByTemplateIdAsync(signatureSheet.TemplateId);
        }

        var result = GetCirculatorFromSignatureSheet(transcribableFields, signatureSheet);
        if (!result.IsSuccess || result.Value == null)
        {
            return result;
        }

        var circulator = result.Value;
        var circulatorFromDb =
            await _circulatorRepository.GetByCirculatorAndMatterId(circulator, signatureSheet.MatterId);
        if (circulatorFromDb == null)
        {
            _circulatorRepository.Add(circulator);
            await _circulatorRepository.SaveChangesAsync();
        }
        else
        {
            circulator = circulatorFromDb;
        }

        if (signatureSheet.CirculatorId == null || signatureSheet.CirculatorId != circulator.Id)
        {
            signatureSheet.CirculatorId = circulator.Id;
            await _circulatorRepository.SaveChangesAsync();
        }

        return ServiceResult<Circulator>.Succeeded(circulator);
    }

    private ServiceResult<Circulator> SetCirculatorRegistrationIdFields(SignatureSheet signatureSheet,
        Circulator circulator, List<TranscribableField> transcribableFields)
    {
        var frontRegisteredCirculatorIdTranscribableField = transcribableFields.SingleOrDefault(tf =>
            tf.Name != null
            && tf.Name.Contains("Circulator", StringComparison.InvariantCultureIgnoreCase)
            && tf.Name.Contains("ID", StringComparison.InvariantCultureIgnoreCase)
            && tf.Name.Contains("Front", StringComparison.InvariantCultureIgnoreCase));
        if (frontRegisteredCirculatorIdTranscribableField == null)
        {
            return ServiceResult<Circulator>.Succeeded(circulator);
        }

        var backRegisteredCirculatorIdTranscribableField = transcribableFields.SingleOrDefault(tf =>
            tf.Name != null
            && tf.Name.Contains("Circulator", StringComparison.InvariantCultureIgnoreCase)
            && tf.Name.Contains("ID", StringComparison.InvariantCultureIgnoreCase)
            && tf.Name.Contains("Back", StringComparison.InvariantCultureIgnoreCase));
        if (backRegisteredCirculatorIdTranscribableField == null)
        {
            return ServiceResult<Circulator>.Succeeded(circulator);
        }

        var frontCirculatorIdField = signatureSheet.Fields
            .FirstOrDefault(f => f.TranscribableField.Name == frontRegisteredCirculatorIdTranscribableField.Name);
        var backCirculatorIdField = signatureSheet.Fields
            .FirstOrDefault(f => f.TranscribableField.Name == backRegisteredCirculatorIdTranscribableField.Name);

        if (frontCirculatorIdField == null || backCirculatorIdField == null)
        {
            return ServiceResult<Circulator>.Failed("Circulator ID field not found");
        }

        var frontCirculatorIdIsReviewed = frontCirculatorIdField.IsReviewed &&
                                          frontCirculatorIdField.Value != null;
        var backCirculatorIdIsReviewed = backCirculatorIdField.IsReviewed &&
                                         backCirculatorIdField.Value != null;

        if (frontCirculatorIdIsReviewed && backCirculatorIdIsReviewed)
        {
            if (frontCirculatorIdField.Value == backCirculatorIdField.Value)
            {
                circulator.RegistrationId = frontCirculatorIdField.Value;
            }
        }
        else if (frontCirculatorIdIsReviewed)
        {
            circulator.RegistrationId = frontCirculatorIdField.Value;
        }
        else if (backCirculatorIdIsReviewed)
        {
            circulator.RegistrationId = backCirculatorIdField.Value;
        }

        return ServiceResult<Circulator>.Succeeded(circulator);
    }


    private ServiceResult<Circulator> SetCirculatorNameFields(SignatureSheet signatureSheet,
        Circulator circulator, List<TranscribableField> fields)
    {
        var circulatorNameFields = fields.Where(tf =>
            tf.Name != null
            && tf.Name.Contains("Circulator", StringComparison.InvariantCultureIgnoreCase)
            && tf.Name.Contains("Name", StringComparison.InvariantCultureIgnoreCase)).ToList();
        if (!circulatorNameFields.Any())
        {
            return ServiceResult<Circulator>.Failed("Circulator name transcribable field not found");
        }

        TranscribableField circulatorNameField;
        if (circulatorNameFields.Count > 1)
        {
            circulatorNameField = circulatorNameFields
                                      .FirstOrDefault(tf =>
                                          tf.Name != null && tf.Name.Contains("Print",
                                              StringComparison.InvariantCultureIgnoreCase))
                                  ?? circulatorNameFields
                                      .First(); // if there is no print field, just take the first one
        }
        else
        {
            circulatorNameField = circulatorNameFields.Single();
        }

        var nameField = signatureSheet.Fields
            .FirstOrDefault(f => f.TranscribableField.Name == circulatorNameField.Name);
        if (nameField == null)
        {
            return ServiceResult<Circulator>.Failed("Circulator name field not found");
        }

        if (!nameField.IsReviewed || nameField.Value == null)
        {
            return ServiceResult<Circulator>.Failed("Circulator name not found");
        }

        circulator.Name = nameField.Value;
        return ServiceResult<Circulator>.Succeeded(circulator);
    }

    private ServiceResult<Circulator> SetCirculatorAddressFields(SignatureSheet signatureSheet,
        Circulator circulator, List<TranscribableField> fields)
    {
        var circulatorAddressField = fields.SingleOrDefault(tf =>
            tf.Name != null
            && tf.Name.Contains("Circulator", StringComparison.InvariantCultureIgnoreCase)
            && tf.Name.Contains("Address", StringComparison.InvariantCultureIgnoreCase));
        if (circulatorAddressField == null)
        {
            return ServiceResult<Circulator>.Failed("Circulator address transcribable field not found");
        }

        var addressField = signatureSheet.Fields
            .FirstOrDefault(f => f.TranscribableField.Name == circulatorAddressField.Name);
        if (addressField == null)
        {
            return ServiceResult<Circulator>.Failed("Circulator address field not found");
        }

        var addressFieldIsReviewed = addressField.IsReviewed &&
                                     addressField.Value != null;
        if (addressFieldIsReviewed)
        {
            circulator.Address = addressField.Value;
        }

        var circulatorCityField = fields.SingleOrDefault(tf =>
            tf.Name != null
            && tf.Name.Contains("Circulator", StringComparison.InvariantCultureIgnoreCase)
            && tf.Name.Contains("City", StringComparison.InvariantCultureIgnoreCase)
        );
        if (circulatorCityField == null)
        {
            return ServiceResult<Circulator>.Failed("Circulator city transcribable field not found");
        }

        var addressLine2Field = signatureSheet.Fields
            .FirstOrDefault(f => f.TranscribableField.Name == circulatorCityField.Name);
        if (addressLine2Field == null)
        {
            return ServiceResult<Circulator>.Failed("Circulator city field not found");
        }

        var addressLine2FieldIsReviewed = addressLine2Field.IsReviewed &&
                                          addressLine2Field.Value != null;
        if (addressLine2FieldIsReviewed)
        {
            var addressInput = AddressParsingService.ParseAddressLine2(addressLine2Field.Value!);
            var usState = UsStates.ParseState(addressInput.State);
            if (usState == null)
            {
                return ServiceResult<Circulator>.Failed("Circulator state not found");
            }

            circulator.City = addressInput.City;
            circulator.UsStateId = usState.Id;
            circulator.PostalCode = addressInput.PostalCode;
        }

        return ServiceResult<Circulator>.Succeeded(circulator);
    }
}