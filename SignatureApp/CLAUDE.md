# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

SignatureApp is a comprehensive **petition/signature collection management system** for legal campaign workflows. Built with a modern full-stack architecture (.NET 8 backend, React 17 frontend), it handles OCR processing, signature validation, voter verification, and compliance tracking with complex business rules.

## Architecture

### Backend (.NET 8)
- **Framework**: ASP.NET Core Web API with Entity Framework Core
- **Database**: SQL Server with spatial data support (NetTopologySuite)
- **Authentication**: JWT Bearer + API Key authentication
- **Cloud**: Azure Blob Storage, Queue Storage, Application Insights

### Frontend (React 17)
- **Stack**: React 17 + TypeScript, Bootstrap 5, React Router v6
- **State**: React Context (AuthProvider, MatterProvider)
- **Testing**: Jest, React Testing Library, Cypress E2E

### Project Structure
```
SignatureApp/
├── Backend/           # ASP.NET Core Web API controllers
├── Model/             # Domain entities and DTOs
├── Data/              # EF Core context, repositories, 150+ migrations
├── DataInterface/     # Repository interfaces and service contracts
├── Service/           # Business logic layer
├── Backend.Tests/     # API integration tests
├── Service.Test/      # Business logic unit tests (MSTest)
├── Tools/             # Console utilities for data operations
└── frontend/          # React TypeScript application
```

## Development Commands

### Backend (.NET 8)
```bash
# Build and test
dotnet build
dotnet test                              # All tests
dotnet test Backend.Tests/               # API tests  
dotnet test Service.Test/                # Service layer tests

# Database migrations
dotnet ef migrations add <NAME> -p Data -s Backend
dotnet ef database update -p Data -s Backend

# Run specific test
dotnet test --filter "TestMethodName"
```

### Frontend (React)
```bash
# Development
npm start                    # HTTPS dev server (port 3000)
npm test                     # Jest unit tests
npm run build:dev            # Development build
npm run build:prod           # Production build

# E2E Testing
cd web-automation && npm run cypress:open
```

### Testing Framework Standards
- **Backend**: Use **MSTest** (`[TestClass]`, `[TestMethod]`, `Assert.IsTrue()`) - NOT xUnit
- **Mocking**: Use Moq with proper `.Returns(Task.FromResult(0))` for async void methods
- **Test Naming**: `MethodName_Scenario_ExpectedResult` pattern

## Key Domain Concepts

### Matter-Centric Workflow
- **Matter**: A petition campaign with templates, signature sheets, and voter data
- **Templates**: Form layouts for OCR processing
- **Signature Sheets**: Individual petition pages with transcribed data
- **Deficiencies**: Validation issues requiring manual review
- **Circulators**: People who collect signatures (with address/registration validation)

### Role-Based Access Control
- **Admin**: Full system access, template/matter management
- **Manager**: Matter oversight, work assignment, deficiency review
- **Reviewer**: Complete transcription work items

### Complex Business Logic
- **Duplicate Detection**: Sophisticated voter matching algorithms
- **Address Validation**: Geocoding with boundary checking
- **Registration ID Validation**: Front/back comparison logic
- **Deficiency Rules**: Configurable validation with automated and manual review paths

## Common Development Patterns

### Service Layer Pattern
All business logic in Service/ layer with dependency injection:
```csharp
// Use ServiceResult<T> for operation results
public async Task<ServiceResult<Circulator>> UpdateCirculatorFromSheetAsync(...)
```

### Repository Pattern
Interface-based repositories with bulk operations support:
```csharp
public interface ICirculatorRepository : IBaseRepository<Circulator>
{
    Task<Circulator?> GetByCirculatorAndMatterId(Circulator circulator, int matterId);
}
```

### Frontend Service Layer
```typescript
// All API calls through authenticated service classes
class BaseService {
    // protected authGet/authPost/authPut/authDelete methods
}
```

### Context-Driven React State
```typescript
// Matter-specific data automatically loaded from URL params
const { matterId, matter, loading } = useMatterContext();
```

## Local Development Setup

### Database
- Docker SQL Server 2022 or local installation
- Run migrations: `dotnet ef database update -p Data -s Backend`

### Storage
- Azurite emulator for blob/queue operations
- Connection strings in `Backend/appsettings.Development.json`

### HTTPS Setup
- Frontend: `npm run prestart` configures HTTPS certificates
- Backend: Uses `launchSettings.json` for local HTTPS

## Testing Infrastructure

### Unit Tests
- **Backend**: MSTest with comprehensive mocking
- **Service Layer**: Database integration tests with test fixtures
- **Frontend**: Jest + React Testing Library

### Integration Tests  
- **E2E**: Cypress suite with Azure Storage integration
- **Health Checks**: Monitoring for all external dependencies
- **Performance**: Logging aspects for query performance

## External Integrations

- **Azure Form Recognizer**: OCR for signature sheet processing
- **Geocoding Services**: Address validation (Azure Maps, Bing, Google)
- **SendGrid**: Email notifications
- **Application Insights**: Telemetry and performance monitoring

## Important Development Notes

### Entity Framework Considerations
- Large dataset operations use bulk extensions
- Spatial queries with NetTopologySuite for boundary calculations  
- Audit trails implemented via `IAuditedEntity` interface

### Performance Patterns
- Repository logging aspects track query performance
- Bulk operations for large dataset uploads
- Caching strategies for reference data

### Security Requirements
- JWT authentication with role-based authorization
- API key authentication for background services
- Sensitive data protection with proper logging exclusions

### CI/CD
- Azure DevOps pipelines (backend, frontend, functions)
- Health check endpoints for deployment verification
- Environment-specific configuration management
