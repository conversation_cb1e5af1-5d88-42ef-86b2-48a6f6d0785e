﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Model.Matters;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using DataInterface.RepositoryInterfaces;

namespace Service.Test;

[TestClass]
public class TemplateHierarchyServiceTests : DatabaseTestsBase
{
    [TestInitialize]
    public async Task TestInitialize()
    {
        await InitializeAsync();
    }

    [TestMethod]
    [TestCategory("Integration")]
    public async Task TestCreateTranscribableFields()
    {
        var docsDir = TestFileUtils.GetRootDocsDirectory();
        var filePath = Path.Combine(docsDir, "BlankTemplates", "AZ Candidate Petition Form (Blank w Fields).pdf");
        var inputBytes = File.ReadAllBytes(filePath);
        var stream = new MemoryStream(inputBytes);

        const int templateId = 1; //TODO: Must be adjusted per DB

        var pdfManipulationService = _serviceProvider.GetRequiredService<PdfManipulationService>();
        var formRecognizerService = _serviceProvider.GetRequiredService<SimpleFormRecognizerService>();
        var templateGeometryService = _serviceProvider.GetRequiredService<TemplateGeometryService>();
        var templateHierarchyService = _serviceProvider.GetRequiredService<TemplateHierarchyService>();

        var fieldsInForm = pdfManipulationService.GetListOfLabeledFields(stream);
        stream.Position = 0;

        var templateRepositoryService = _serviceProvider.GetRequiredService<ITemplateRepository>();
        var template = await templateRepositoryService.GetByIdAsync(templateId);
        var formResults = await formRecognizerService.RecognizeFormAsync(new Matter{Type= MatterType.Candidate}, stream, isTemplate: true);
        var results = templateGeometryService.Transform(template, formResults, fieldsInForm);

        var pagesByPageNumber = results.Pages.ToDictionary(tp => tp.PageNumber, tp => tp);

        var fields = templateHierarchyService.CreateTranscribableFields(fieldsInForm, pagesByPageNumber, results.FormLines);
        Assert.IsTrue(fields.All(f => f.LeftLine != null));
    }
}
