using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace Service.Test;

[TestClass]
public class SheetNumberServiceTests
{
    [TestMethod]
    public void TestGetPageRangeStartFromFilename_LongestOneWins()
    {
        var sheetNumberService = GetSheetNumberService();
        var (pageNumberStart, pageNumberEnd) = sheetNumberService.GetPageNumberRangeFromFilename("12345-23456.123456-234567.34567-45678.pdf");
        Assert.AreEqual(123456, pageNumberStart);
        Assert.AreEqual(234567, pageNumberEnd);
    }

    [TestMethod]
    public void TestGetSingleSheetNumberFromFilename_FullPath()
    {
        var sheetNumberService = GetSheetNumberService();
        var result = sheetNumberService.GetSingleSheetNumberFromFilename(
            "unverified/matter3033/upload4058template1/03251-03500 I-14-2024_20240726111150_14.3429.pdf");
        Assert.AreEqual(3429, result);
    }

    [TestMethod]
    public void TestGetSingleSheetNumberFromFilename_Simple()
    {
        var sheetNumberService = GetSheetNumberService();
        var result = sheetNumberService.GetSingleSheetNumberFromFilename(
            "1/003429.pdf");
        Assert.AreEqual(3429, result);
    }

    private static SheetNumberService GetSheetNumberService()
    {
        var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        var logger = loggerFactory.CreateLogger<SheetNumberService>();
        var sheetNumberService = new SheetNumberService(logger);
        return sheetNumberService;
    }

}
