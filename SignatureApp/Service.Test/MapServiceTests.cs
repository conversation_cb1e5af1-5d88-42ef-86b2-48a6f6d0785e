﻿using Backend.Controllers;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Model.Geocoding;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace Service.Test;

[TestClass]
public class AzureMapsGeocodingServiceTests : DatabaseTestsBase
{
    [TestInitialize]
    public async Task TestInitialize()
    {
        await InitializeAsync();
    }

    [TestMethod]
    public async Task TestGeocodeAsync_Success()
    {
        var geocodingService = _serviceProvider.GetRequiredService<AzureMapsGeocodingService>();
        var addressInput = AddressParsingService.ParseAddressLine2("Phoenix, AZ 85007");
        addressInput.AddressLine = "1700 W Washington St";
        var result = await geocodingService.GeocodeAsync(addressInput);
        Assert.IsTrue(result.IsSuccess && result.Value != null);
    }

    [TestMethod]
    public async Task TestGeocodeAsync_Fail()
    {
        var geocodingService = _serviceProvider.GetRequiredService<AzureMapsGeocodingService>();
        var addressInput = AddressParsingService.ParseAddressLine2("Tucson, AZ 85250");
        addressInput.AddressLine = "9435 W 33rd St";
        var result = await geocodingService.GeocodeAsync(addressInput);
        Assert.IsFalse(result.IsSuccess);
        Assert.AreEqual(result.ErrorMessages.Count, result.ErrorMessages.Distinct().Count());
    }

}