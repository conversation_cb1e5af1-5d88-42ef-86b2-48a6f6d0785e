using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace Service.Test;

[TestClass]
public class AddressParsingServiceTests: DatabaseTestsBase
{
    [TestInitialize]
    public async Task TestInitialize()
    {
        await InitializeAsync();
    }

    [TestMethod]
    public void TestParseFullAddress()
    {
        var address = AddressParsingService.ParseFullAddressToInput("123 Main St Tucson, AZ 85701");
        Assert.AreEqual("123 Main St", address?.AddressLine);
        Assert.AreEqual("Tucson", address?.City);
        Assert.AreEqual("AZ", address?.State);
        Assert.AreEqual("85701", address?.PostalCode);
    }

    [TestMethod]
    public void TestParseFullAddress_MultipleCities()
    {
        var address = AddressParsingService.ParseFullAddressToInput("4632 E Sunset Dr Phoenix 85028");
        Assert.AreEqual("4632 E Sunset Dr", address?.AddressLine);
        Assert.AreEqual("Phoenix", address?.City);
        Assert.AreEqual("AZ", address?.State);
        Assert.AreEqual("85028", address?.PostalCode);
    }

    [TestMethod]
    public void TestParseFullAddress_MultiWordCity()
    {
        var address =
            AddressParsingService.ParseFullAddressToInput("1100 McCulloch Blvd N Lake Havasu City, AZ 86403");
        Assert.AreEqual("1100 McCulloch Blvd N", address?.AddressLine);
        Assert.AreEqual("Lake Havasu City", address?.City);
        Assert.AreEqual("AZ", address?.State);
        Assert.AreEqual("86403", address?.PostalCode);
    }

    [TestMethod]
    public void TestParseFullAddress_AbbreviatedCity()
    {
        var address = AddressParsingService.ParseFullAddressToInput("123 Main St PHX, AZ 85001");
        Assert.AreEqual("123 Main St", address?.AddressLine);
        Assert.AreEqual("Phoenix", address?.City);
        Assert.AreEqual("AZ", address?.State);
        Assert.AreEqual("85001", address?.PostalCode);
    }
}