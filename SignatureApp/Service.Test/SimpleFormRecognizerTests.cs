﻿using System;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Model.Matters;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using System.Text.Json;

namespace Service.Test
{
    [TestClass]
    public class SimpleFormRecognizerTests : UploadIntegrationTestsBase
    {
        [TestInitialize]
        public async Task TestInitialize()
        {
            await InitializeAsync();
        }

        [TestMethod]
        [TestCategory("Integration")]
        public async Task TestRecognizeHelloWorld()
        {
            var formRecognizerService = _serviceProvider.GetRequiredService<SimpleFormRecognizerService>();

            var docsDir = TestFileUtils.GetRootDocsDirectory();
            var filePath = Path.Combine(docsDir, "HelloWorld.pdf");
            await using var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
            using var memoryStream = new MemoryStream();
            await fileStream.CopyToAsync(memoryStream);
            memoryStream.Position = 0;
            var formResults =
                await formRecognizerService.AnalyzeDocumentAsync(memoryStream);
            Assert.IsNotNull(formResults);
        }


        [TestMethod]
        [TestCategory("Integration")]
        public async Task TestSerializeAllFormOutput()
        {
            var docsDir = TestFileUtils.GetRootDocsDirectory();
            var matterType = MatterType.Initiative; // "CandidatePetitions";
            var matterTypeDirName = $"{matterType}Petitions";
            //var matterName = "Javier Soto";
            //var fileName = "2020.04.06 - Javier Soto - Nomination Petitions.pdf";
            //var matterName = "Zuhdi Jasser";
            //var fileName = "Jasser Signatures Scan1.pdf";
            var matterName = "DamcoReview";
            var fileName =
                // "00251-00500 I-14-2024_20240726111150_2.pdf";
                // "02501-02750 I-14-2024_20240726111150_11.pdf";
                // "03251-03500 I-14-2024_20240726111150_14.pdf";
                // "06751-07000 I-14-2024_20240726111150_28.pdf";
                // "07001-07250 I-14-2024_20240726111150_29.pdf";
                // "09001-09250 I-14-2024_20240726111150_37.pdf";
                // "11251-11500 I-14-2024_20240726111150_46.pdf";
                // "12001-12250 I-14-2024_20240726111150_49.pdf";
                // "12751-13000 I-14-2024_20240726111150_52.pdf";
                "15251-15500 I-14-2024_20240726111150_62.pdf";
            // "18001-18250 I-14-2024_20240726111150_73.pdf",
            // "18751-19000 I-14-2024_20240726111150_76.pdf",
            // "21001-21250 I-14-2024_20240726111150_85.pdf",
            // "23751-24000 I-14-2024_20240726111150_96.pdf",
            // "25001-25250 I-14-2024_20240726111150_101.pdf",
            // "26501-26750 I-14-2024_20240726111150_107.pdf",
            // "27751-28000 I-14-2024_20240726111150_112.pdf",
            // "28379-28628 I-14-2024_20240727083523_2.pdf",
            // "29129-29378 I-14-2024_20240727083523_5.pdf",
            // "31129-31378 I-14-2024_20240727083523_13.pdf",
            // "34879-35128 I-14-2024_20240727083523_28.pdf",
            // "35379-35628 I-14-2024_20240727083523_30.pdf",
            // "38379-38628 I-14-2024_20240727083523_42.pdf",
            // "39879-40128 I-14-2024_20240727083523_48.pdf",
            // "42379-42628 I-14-2024_20240727083523_58.pdf",
            // "45379-45628 I-14-2024_20240727083523_70.pdf",
            // "46879-47128 I-14-2024_20240727083523_76.pdf",
            // "49379-49628 I-14-2024_20240727083523_86.pdf",
            // "51629-51878 I-14-2024_20240727083523_95.pdf",
            // "54379-54628 I-14-2024_20240727083523_106.pdf",
            // "55712-55961 I-14-2024_20240728073900_4.pdf",
            // "57462-57711 I-14-2024_20240728073900_11.pdf",
            // "58712-58961 I-14-2024_20240728073900_16.pdf"

            var filePath = Path.Combine(docsDir, $"{matterTypeDirName}", $"{matterName.Replace(" ", "")}", fileName);

            var pdfManipulationService = _serviceProvider.GetRequiredService<PdfManipulationService>();
            var formRecognizerService = _serviceProvider.GetRequiredService<SimpleFormRecognizerService>();
            var pageNumberService = _serviceProvider.GetRequiredService<SheetNumberService>();

            await using var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
            (var pageStart, var pageEnd) = pageNumberService.GetPageNumberRangeFromFilename(fileName);
            if (pageStart == 0)
            {
                pageStart = pageNumberService.GetSingleSheetNumberFromFilename(fileName);
            }

            var stopwatchOuter = Stopwatch.StartNew();
            await pdfManipulationService.SplitIntoDifferentStreamsAsync(fileStream, async (stream, index) =>
            {
                using var memoryStream = new MemoryStream();
                await stream.CopyToAsync(memoryStream);
                var outputJsonPath = Path.Combine(docsDir, "FormResults", $"{matterName.Replace(" ", "")}",
                    $"sheet{pageStart + index}.json");
                if (!File.Exists(outputJsonPath))
                {
                    memoryStream.Position = 0;
                    var stopwatchInner = Stopwatch.StartNew();
                    var formResults =
                        await formRecognizerService.RecognizeFormAsync(new Matter { Id = -1, Type = matterType }, memoryStream, isTemplate: false);
                    stopwatchInner.Stop();
                    Console.WriteLine($"Time taken to recognize form: {stopwatchInner.Elapsed}");
                    await using FileStream createStream = File.Create(outputJsonPath);
                    await JsonSerializer.SerializeAsync(createStream, formResults);
                }
            });
            stopwatchOuter.Stop();
            Console.WriteLine($"Time taken to recognize all forms: {stopwatchOuter.Elapsed}");
        }
    }
}
