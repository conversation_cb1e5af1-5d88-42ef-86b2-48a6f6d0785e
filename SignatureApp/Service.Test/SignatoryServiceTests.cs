using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Service.Test.TestHelpers;
using VerifyMSTest;
using VerifyTests;

namespace Service.Test;

[TestClass]
[UsesVerify]
public partial class SignatoryServiceTests : DatabaseTestsBase
{
    [TestInitialize]
    public async Task TestInitialize()
    {
        await InitializeAsync();
    }

    [TestMethod]
    public async Task TestGetSignatoryFromRow()
    {
        var values = GetInitiative2022Values();
        await InternalVerify(values);
    }

    [TestMethod]
    public async Task TestGetSignatoryFromRow_BadAddress()
    {
        var values = GetInitiative2022Values();
        values[4] = "4042 E Nisbet";
        await InternalVerify(values);
    }

    [TestMethod]
    public async Task TestGetSignatoryFromRow_BadAddress2()
    {
        var values = GetInitiative2022Values();
        values[4] = "4632 E. Sunset Dr";
        values[5] = "85028";
        await InternalVerify(values);
    }

    private async Task InternalVerify(string[] values)
    {
        var service = _serviceProvider.GetRequiredService<SignatoryService>();
        var templateColumns = TemplateTestHelpers.CreateInitiative2022Columns();
        var row = SignatureSheetRowHelpers.CreateSignatureSheetRow(templateColumns, values);
        var result = service.GetSignatoryFromRow(templateColumns, row);
        Assert.IsTrue(result.IsSuccess);
        Assert.IsNotNull(result.Value);
        var signatory = result.Value;
        var addressInput = service.GetAddressInputFromSignatory(signatory);
        await service.GeocodeSignatoryAsync(signatory, addressInput);

        var settings = new VerifySettings();
        settings.DontScrubDateTimes();
        await Verifier.Verify(signatory, settings);
    }

    private static string[] GetInitiative2022Values()
    {
        return new []
        {
            "",
            "", // Signature
            "John", //First name
            "Doe", // Last name
            "1234 Main St", // Street address
            "85032", // Zip code
            "Phoenix", // City
            "2022-01-01", // Date signed
        };
    }
}