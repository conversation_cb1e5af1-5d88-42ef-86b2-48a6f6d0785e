﻿using DataInterface.RepositoryInterfaces;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Model.Matters;
using Model.SignatureSheets;
using Model.Templates;
using Service.ServiceModels;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using DataInterface.ServiceInterfaces;
using Model.Interfaces;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace Service.Test;

[TestClass]
public class SignatureSheetGeometryService_TableTests : UploadIntegrationTestsBase
{
    private enum ErrorType
    {
        None,
        NumRows,
        NumColumns,
        NumCells,
        Other,
        RowHeights,
        ColumnWidths,
        CellHeights,
        CellWidths,
    }

    static readonly Regex FilenameRegex = new Regex(@"sheet(\d+)\.json");
    // private const double TEST_ROW_HEIGHT_EPSILON = 0.133;
    // private const double TEST_COL_WIDTH_EPSILON = 0.133;
    // public const double TEST_CELL_HEIGHT_EPSILON = 0.166;

    [TestInitialize]
    public async Task TestInitialize()
    {
        await InitializeAsync();
    }

    [TestMethod]
    [TestCategory("Integration")]
    public async Task TestRowsAndColumnsForAllFormOutput()
    {
        var errorTypes = new Dictionary<ErrorType, List<string>>();
        foreach (ErrorType errorType in Enum.GetValues(typeof(ErrorType)))
        {
            errorTypes[errorType] = new List<string>();
        }
        const int templateId = 2; //TODO: Must be adjusted per DB, this is a 2022 Initiative for me
        var matterType = MatterType.Initiative;
        var matterName = "DamcoReview";
        //var matterName = "Javier Soto";
        //var matterName = "Zuhdi Jasser";
        var matterNameDir = matterName.Replace(" ", "");
        var docsDir = TestFileUtils.GetRootDocsDirectory();
        var incomingDirectory = Path.Combine(docsDir, "RecognizedSheetSerializedResults", matterNameDir);

        //await TableTestHelpers.WriteOutAllTableResults(Path.Combine(docsDir, "RecognizedSheetTableResults"), matterName, incomingDirectory);

        var templateRepository = _serviceProvider.GetRequiredService<ITemplateRepository>();
        var template = await templateRepository.GetByIdAsync(templateId);
        Assert.IsNotNull(template);

        var templateFormLineRepository = _serviceProvider.GetRequiredService<ITemplateFormLineRepository>();
        var templateFormLines = await templateFormLineRepository.GetAllByTemplateIdAsync(templateId);
        foreach (var page in template.Pages)
        {
            page.FormLines = templateFormLines.Where(tfl => tfl.TemplatePageId == page.Id).ToList();
        }

        var transcribableFieldRepository = _serviceProvider.GetRequiredService<ITranscribableFieldRepository>();
        var transcribableFields = await transcribableFieldRepository.GetAllByTemplateIdAsync(templateId);

        string? singleFileName = null; // substitute a sheet file name here to just test one sheet e.g. "sheet14.json";
        //string? singleFileName = "sheet370.json";
        string? skipFileName = null; // "sheet9.json";

        var matter = new Matter { Id = 42, Type = matterType, Name = matterName };
        var serializedOutputDirectory = Path.Combine(docsDir, "TransformedSheetSerializedResults", matterNameDir);
        var tableOutputDirectory = Path.Combine(docsDir, "TransformedSheetTableResults", matterNameDir);
        var sheetFileDirectory = Path.Combine(docsDir, $"{matterType}Petitions", matterNameDir);
        var sheetViewOutputDirectory = Path.Combine(docsDir, "TransformedSheetViewResults", matterNameDir);
        var inputFiles = Directory.EnumerateFiles(incomingDirectory, "*.json")
            .Where(filename => FilenameRegex.IsMatch(filename))
            .OrderBy(GetSheetNumber).ToList();

        foreach (var file in inputFiles)
        {
            if (Path.GetFileName(file) == skipFileName)
            {
                continue;
            }
            if (singleFileName == null || Path.GetFileName(file) == singleFileName)
            {
                var result = await TestRowsAndColumnsForSingleFile(
                    _serviceProvider, template, transcribableFields, templateFormLines, matter,
                    file, serializedOutputDirectory, tableOutputDirectory,
                    sheetFileDirectory, sheetViewOutputDirectory);
                errorTypes[result.Value].Add(result.ErrorMessages.FirstOrDefault() ?? "");
            }
        }

        foreach (ErrorType errorType in Enum.GetValues(typeof(ErrorType)))
        {
            Console.WriteLine($"Total number of {errorType} errors: {errorTypes[errorType].Count}");
            if (errorType != ErrorType.None)
            {
                foreach (var error in errorTypes[errorType])
                {
                    Console.WriteLine(error);
                }
            }
        }
    }

    private static int GetSheetNumber(string file)
    {
        var match = FilenameRegex.Match(file);
        if (!match.Success)
        {
            throw new Exception($"{file} did not match {FilenameRegex}");
        }
        int sheetNumber = int.Parse(match.Groups[1].Value);
        return sheetNumber;
    }

    private async Task<ServiceResult<ErrorType>> TestRowsAndColumnsForSingleFile(
        IServiceProvider serviceProvider,
        Template template,
        IList<TranscribableField> transcribableFields,
        IList<TemplateFormLine> templateFormLines,
        Matter matter,
        string serializedFormResultsPath,
        string serializedOutputDir,
        string tableOutputDir,
        string sheetViewInputDir,
        string sheetViewOutputDir)
    {
        var pdfManipulationService = serviceProvider.GetRequiredService<PdfManipulationService>();
        var signatureSheetGeometryService = serviceProvider.GetRequiredService<SignatureSheetGeometryService>();

        var serviceResult = new ServiceResult<ErrorType> { Status = ResultStatus.Failure };
        var templatePages = template.Pages.ToDictionary(tp => tp.PageNumber);
        var templatePage = template.Pages[0];
        var templateTable = template.Tables[0];
        var match = FilenameRegex.Match(serializedFormResultsPath);
        if (!match.Success)
        {
            throw new Exception($"{serializedFormResultsPath} did not match {FilenameRegex}");
        }
        string fileNumber = match.Groups[1].Value;
        int sheetNumber = int.Parse(fileNumber);
        await using (var inputFileStream = new FileStream(serializedFormResultsPath, FileMode.Open, FileAccess.Read))
        {
            var formResults = await JsonSerializer.DeserializeAsync<FormRecognizerResults>(inputFileStream);
            if (formResults == null)
            {
                throw new Exception($"No form results for {sheetNumber}");
            }

            var signatureSheetUpload = new SignatureSheetUpload { Id = 42, TemplateId = template.Id, MatterId = matter.Id, FileName = serializedFormResultsPath };
            var sheetPage = formResults.Pages[0];

            var result = signatureSheetGeometryService.Transform(
                matter.Type,
                templatePages,
                transcribableFields,
                templateTable, formResults, signatureSheetUpload,
                0, sheetNumber,
                templateFormLines.AsReadOnly(),
                whichPartsAreValid: null);

            if (!result.IsSuccess || result.Value == null)
            {
                var error = string.Join("\n", result.ErrorMessages);
                Debug.WriteLine($"Error in signature sheet {sheetNumber}: {error}");
                serviceResult.ErrorMessages.Add(error);
                serviceResult.Value = ErrorType.Other;
                return serviceResult;
            }
            var sheetResults = result.Value;

            // write out the serialized results
            var serializedResultsFile = Path.Combine(serializedOutputDir, $"serializedSheet{sheetNumber}.json");
            await using (var streamWriter = new StreamWriter(new FileStream(serializedResultsFile, FileMode.Create, FileAccess.Write)))
            {
                await streamWriter.WriteAsync(JsonSerializer.Serialize(sheetResults));
            }

            // write out the table results
            var signatureSheetTable = sheetResults.SignatureTable;
            if (signatureSheetTable == null)
            {
                throw new Exception($"No signature sheet table for {sheetNumber}");
            }
            var tableResultsFile = Path.Combine(tableOutputDir, $"tableSignatureSheetResults{sheetNumber}.html");
            await using (var streamWriter = new StreamWriter(new FileStream(tableResultsFile, FileMode.Create, FileAccess.Write)))
            {
                await WriteOutHtmlTableAsync(streamWriter, signatureSheetTable);
            }


            if (templateTable.NumberOfCols != signatureSheetTable.Columns.Count)
            {
                var error =
                    $"Problem with columns {templateTable.NumberOfCols} != {signatureSheetTable.Columns.Count} with sheet {sheetNumber}";
                await WriteOutSignatureSheetView(pdfManipulationService, sheetViewInputDir, sheetViewOutputDir, sheetNumber, signatureSheetTable);
                return CreateErrorServiceResult(ErrorType.NumColumns, error, serviceResult);
            }

            if (templateTable.NumberOfRows != signatureSheetTable.Rows.Count)
            {
                var error =
                    $"Problem with rows {templateTable.NumberOfRows} != {signatureSheetTable.Rows.Count} with sheet index {sheetNumber}";
                await WriteOutSignatureSheetView(pdfManipulationService, sheetViewInputDir, sheetViewOutputDir, sheetNumber, signatureSheetTable);
                return CreateErrorServiceResult(ErrorType.NumRows, error, serviceResult);
            }

            if (templateTable.NumberOfRows * templateTable.NumberOfCols != signatureSheetTable.Cells?.Count)
            {
                var error =
                    $"Problem with cells {templateTable.NumberOfRows * templateTable.NumberOfCols} != {signatureSheetTable.Cells?.Count} with sheet index {sheetNumber}";
                await WriteOutSignatureSheetView(pdfManipulationService, sheetViewInputDir, sheetViewOutputDir, sheetNumber, signatureSheetTable);
                return CreateErrorServiceResult(ErrorType.NumCells, error, serviceResult);
            }

            // Compute the template column width row height
            var shiftVector = sheetResults.PageResults[0].ShiftVector;
            var idealColumns = templateTable.Columns.Select(col =>
            {
                var templateCol = GeometryService.HorizontallyTransformBounds(col, (decimal)sheetPage.Width, shiftVector);
                return templateCol;
            }).ToArray();

            var idealRow = GeometryService.VerticallyTransformBounds(templateTable.Rows[0], (decimal)sheetPage.Height, shiftVector);

            // Check column widths
            foreach (var (col, index) in signatureSheetTable.Columns
                         .OrderBy(c => c.ColumnIndex)
                         .Select((c, i) => (c, i)))
            {
                var idealCol = idealColumns[index];
                var allowableDifference = (idealCol.Right - idealCol.Left) / 10.0m; // less than 10%
                var (widthDifference, description) = GetWidthDifference(idealColumns[index], col);
                if (Math.Abs(widthDifference) >= allowableDifference)
                {
                    var error = $"Sheet index {sheetNumber} Column[{col.ColumnIndex}] is too {description}";
                    await WriteOutSignatureSheetView(pdfManipulationService, sheetViewInputDir, sheetViewOutputDir, sheetNumber, signatureSheetTable);
                    return CreateErrorServiceResult(ErrorType.ColumnWidths, error, serviceResult);
                }
            }

            // Check row heights
            var previousRow = signatureSheetTable.Rows.First();
            foreach (var row in signatureSheetTable.Rows)
            {
                var allowableDifference = (idealRow.Bottom - idealRow.Top) / 10.0m; // less than 10%
                var (heightDifference, description) = GetHeightDifference(previousRow, row);
                if (Math.Abs(heightDifference) >= allowableDifference)
                {
                    var error = $"Sheet index {sheetNumber} Row[{row.RowIndex}] is too {description}";
                    await WriteOutSignatureSheetView(pdfManipulationService, sheetViewInputDir, sheetViewOutputDir, sheetNumber, signatureSheetTable);
                    return CreateErrorServiceResult(ErrorType.RowHeights, error, serviceResult);
                }

                (heightDifference, description) = GetHeightDifference(idealRow, row);
                if (Math.Abs(heightDifference) >= allowableDifference)
                {
                    var error = $"Sheet index {sheetNumber} Row[{row.RowIndex}] is too {description}";
                    await WriteOutSignatureSheetView(pdfManipulationService, sheetViewInputDir, sheetViewOutputDir, sheetNumber, signatureSheetTable);
                    return CreateErrorServiceResult(ErrorType.RowHeights, error, serviceResult);
                }
                //Assert.IsTrue(Math.Abs(heightDifference) < (decimal)TEST_ROW_HEIGHT_EPSILON,
                //    $"Sheet index {index} Row[{row.RowIndex}] is too {description}");
                previousRow = row;
            }

            // Check the cell heights within the row, to see if they are out of expectations
            foreach (var row in signatureSheetTable.Rows)
            {
                var allowableDifference = (idealRow.Bottom - idealRow.Top) / 10.0m; // less than 10%
                foreach (var cell in signatureSheetTable.Cells.Where(c => c.RowIndex == row.RowIndex))
                {
                    var (heightDifference, description) = GetHeightDifference(row, cell);
                    if (Math.Abs(heightDifference) >= allowableDifference)
                    {
                        var error =
                            $"Sheet {sheetNumber} Cell[{cell.RowIndex},{cell.ColumnIndex}] is {Math.Abs(heightDifference)} too {description}";
                        await WriteOutSignatureSheetView(pdfManipulationService, sheetViewInputDir, sheetViewOutputDir, sheetNumber, signatureSheetTable);
                        return CreateErrorServiceResult(ErrorType.CellHeights, error, serviceResult);
                    }
                    //Assert.IsTrue(Math.Abs(heightDifference) < (decimal)TEST_CELL_HEIGHT_EPSILON,
                    //    $"Sheet index {index} Cell[{cell.RowIndex},{cell.ColumnIndex}] is {Math.Abs(heightDifference)} too {description}");
                }
            }

            // Check the widths of the cells to make sure they are within the norm
        }
        return ServiceResult<ErrorType>.Succeeded(ErrorType.None);
    }

    private static (decimal widthDifference, string description) GetWidthDifference(IHaveSimpleBounds idealCol, IHaveSimpleBounds col)
    {
        var idealColWidth = idealCol.Right - idealCol.Left;
        var colWidth = col.Right - col.Left;
        var widthDifference = idealColWidth - colWidth;
        var description = widthDifference > 0 ? "narrow" : "wide";
        return (widthDifference, description);
    }
    private static (decimal heightDifference, string description) GetHeightDifference(IHaveSimpleBounds idealRow, IHaveSimpleBounds row)
    {
        var idealRowHeight = idealRow.Bottom - idealRow.Top;
        var rowHeight = row.Bottom - row.Top;
        var heightDifference = idealRowHeight - rowHeight;
        var description = heightDifference > 0 ? "short" : "tall";
        return (heightDifference, description);
    }

    private static ServiceResult<ErrorType> CreateErrorServiceResult(ErrorType errorType, string error, ServiceResult<ErrorType> serviceResult)
    {
        Debug.WriteLine(error);
        serviceResult.ErrorMessages.Add(error);
        serviceResult.Value = errorType;
        return serviceResult;
    }

    private async Task WriteOutSignatureSheetView(PdfManipulationService pdfManipulationService, string sheetViewInputDir,
        string sheetViewOutputDir, int sheetNumber, SignatureSheetTable signatureSheetTable)
    {
        var pdfFilePath = Path.Combine(sheetViewInputDir, $"{sheetNumber.ToString("D6")}.pdf");
        var sheetViewResultsFile = Path.Combine(sheetViewOutputDir, $"sheetView{sheetNumber}.png");
        await using var pdfStream = new FileStream(pdfFilePath, FileMode.Open, FileAccess.Read);
        var imageBytes = pdfManipulationService.DrawTableOnPdf(pdfStream, signatureSheetTable);
        await File.WriteAllBytesAsync(sheetViewResultsFile, imageBytes);
    }

    private static async Task WriteOutHtmlTableAsync(StreamWriter writer, SignatureSheetTable table)
    {
        if (table.Cells != null)
        {
            int maxRows = Math.Max(table.Rows.Count, table.Cells.Max(c => c.RowIndex) + 1);
            int maxCols = Math.Max(table.Columns.Count, table.Cells.Max(c => c.ColumnIndex) + 1);
            SignatureSheetCell[,] cells = new SignatureSheetCell[maxRows, maxCols];
            foreach (var cell in table.Cells)
            {
                //if ((cell.RowIndex < table.Rows.Count) && (cell.ColumnIndex < table.Columns.Count))
                //{
                cells[cell.RowIndex, cell.ColumnIndex] = cell;
                //}
            }

            await WriteOutHtmlTableCellsAsync(writer, cells, table.Rows);
        }
    }

    private static async Task WriteOutHtmlTableCellsAsync(StreamWriter writer, SignatureSheetCell[,] cells,
        IList<SignatureSheetRow> rows)
    {
        await writer.WriteLineAsync("<table border=\"1\">");
        for (int rowIndex = 0; rowIndex < cells.GetLength(0); rowIndex++)
        {
            if (rows != null)
            {
                var row = rows[rowIndex];
                await writer.WriteAsync($"<tr height={row.Bottom - row.Top}>");
            }
            else
            {
                await writer.WriteAsync("<tr>");
            }

            for (int colIndex = 0; colIndex < cells.GetLength(1); colIndex++)
            {
                var cell = cells[rowIndex, colIndex];
                if (cell.Value != null)
                {
                    await writer.WriteAsync($"<td height={(cell.Bottom - cell.Top) * 50} width={(cell.Right - cell.Left) * 50}");
                    if (cell.ColumnSpan > 1)
                    {
                        await writer.WriteAsync($" colspan=\"{cell.ColumnSpan}\"");
                    }

                    if (cell.RowSpan > 1)
                    {
                        await writer.WriteAsync($" rowspan=\"{cell.RowSpan}\"");
                    }

                    if (colIndex == 0)
                    {
                        await writer.WriteAsync($">{cell.Value}</td> H({cell.Top}, {cell.Bottom}) = {cell.Bottom - cell.Top}");
                    }
                    else
                    {
                        await writer.WriteAsync($">{cell.Value} W({cell.Left}, {cell.Right}) = {cell.Right - cell.Left}</td>");
                    }
                }
                else
                {
                    await writer.WriteLineAsync("<td>NULL</td>");
                }
            }

            await writer.WriteLineAsync("</tr>");
        }

        await writer.WriteLineAsync("</table>");
    }

    private double ComputeStandardDeviation(decimal[] values)
    {
        decimal average = values.Average();
        double sdev = Math.Sqrt(values.Sum(x => Math.Pow((double)(x - average), 2) / values.Length));
        Console.WriteLine("Average: " + average);
        Console.WriteLine("Standard deviation: " + sdev);
        return sdev;
    }
}