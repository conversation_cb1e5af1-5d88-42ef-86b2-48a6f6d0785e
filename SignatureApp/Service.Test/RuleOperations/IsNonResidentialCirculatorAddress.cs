using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Newtonsoft.Json.Linq;
using Service.RuleOperations;
using Service.ServiceModels;
using Service.Test.TestHelpers;

namespace Service.Test.RuleOperations;

[TestClass]
public class IsNonResidentialCirculatorAddressTests : DatabaseTestsBase
{
    [TestInitialize]
    public async Task TestInitialize()
    {
        await InitializeAsync();
    }

    [TestMethod]
    public async Task TestIsNonResidentialCirculatorAddress()
    {
        var ruleContext = new RuleContext();
        var operation = _serviceProvider.GetRequiredService<IsNonResidentialCirculatorAddress>();

        // residence
        // Assert.AreEqual(false,
        //     await operation.EvaluateAsync(ruleContext, JToken.FromObject("15574 new park terrace"),
        //         JToken.FromObject("San Diego, CA 92127")));
        // Assert.AreEqual(false,
        //     await operation.EvaluateAsync(ruleContext, JToken.FromObject("10941 MATINAL CIR"),
        //         JToken.FromObject("San Diego, CA 92127")));
        // Assert.AreEqual(false,
        //     await operation.EvaluateAsync(ruleContext, JToken.FromObject("4364 Longmeadow Lane"),
        //         JToken.FromObject("Dayton, OH")));
        // Assert.AreEqual(false,
        //     await operation.EvaluateAsync(ruleContext, JToken.FromObject("313 Wakefield Road"),
        //         JToken.FromObject("Dayton, OH")));
        //
        // // Target address
        // Assert.AreEqual(true,
        //     await operation.EvaluateAsync(ruleContext, JToken.FromObject("17170 Camino Del Sur"),
        //         JToken.FromObject("San Diego, CA 92127")));
        //
        // // Costco address - carmel mountain
        // Assert.AreEqual(true,
        //     await operation.EvaluateAsync(ruleContext, JToken.FromObject("12350 Carmel Mountain Rd"),
        //         JToken.FromObject("San Diego, CA 92128")));
        //
        // // Ralph - 4S
        // Assert.AreEqual(true,
        //     await operation.EvaluateAsync(ruleContext, JToken.FromObject("10525 4S Commons Dr"),
        //         JToken.FromObject("San Diego, CA")));
        //
        // // This is a lot with no house on it, but is coming back as residential
        // Assert.AreEqual(false,
        //     await operation.EvaluateAsync(ruleContext,
        //         JToken.FromObject("8240 W Denton Ln"),
        //         JToken.FromObject("Glendale AZ 85305")));
        var result = await operation.EvaluateAsync(ruleContext,
            JToken.FromObject(
                SignatureSheetFieldHelpers.CreateSignatureSheetField(
                    "Circulator Address", "2184 N. Saddlewood Ranch Dr")),
            JToken.FromObject(
                SignatureSheetFieldHelpers.CreateSignatureSheetField(
                    "Circulator City Zip", "Tucson 85745"))
        );
        Assert.AreEqual(false, result.Value);


        // This is a non-existent lot
        result = await operation.EvaluateAsync(ruleContext,
            JToken.FromObject(SignatureSheetFieldHelpers.CreateSignatureSheetField(
                "Circulator Address", "17684 W. Acacia Ct.")),
            JToken.FromObject(SignatureSheetFieldHelpers.CreateSignatureSheetField(
                "Circulator City Zip", "Goodyear AZ 85338"))
        );
        Assert.AreEqual(true, result.Value);
    }
}