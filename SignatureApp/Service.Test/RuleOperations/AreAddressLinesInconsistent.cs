﻿using System.Threading.Tasks;
using DataInterface.ServiceInterfaces;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Model.SignatureSheets;
using Newtonsoft.Json.Linq;
using Service.RuleOperations;
using Service.ServiceModels;

namespace Service.Test.RuleOperations;

[TestClass]
public class AreAddressLinesInconsistentTests : DatabaseTestsBase
{
    [TestInitialize]
    public async Task TestInitialize()
    {
        await InitializeAsync();
    }

    [TestMethod]
    public async Task TestAreAddressLinesInconsistent()
    {
        var ruleContext = new RuleContext();
        var operation = _serviceProvider.GetRequiredService<AreAddressLinesInconsistent>();
        ServiceResult<bool> result;

        // Not yet reviewed
        result = await operation.EvaluateAsync(ruleContext,
            JToken.FromObject(new SignatureSheetField { Value = "1234 Totally Made Up St", IsReviewed = false }),
            JToken.FromObject(new SignatureSheetField { Value = "Wrong City, XX 0000", IsReviewed = false }));
        Assert.AreEqual(false, result.Value);

        // Correct Street. Correct City
        result = await operation.EvaluateAsync(ruleContext,
            JToken.FromObject(new SignatureSheetField { Value = "15574 New Park Terrace", IsReviewed = true }),
            JToken.FromObject(new SignatureSheetField { Value = "San Diego, CA 92127", IsReviewed = true }));
        Assert.AreEqual(false, result.Value);

        // Correct Street. Wrong City
        result = await operation.EvaluateAsync(ruleContext,
            JToken.FromObject(new SignatureSheetField { Value = "15574 New Park Terrace", IsReviewed = true }),
            JToken.FromObject(new SignatureSheetField { Value = "Escondido, CA 92127", IsReviewed = true }));
        Assert.AreEqual(true, result.Value);

        // Correct Street. Correct City
        result = await operation.EvaluateAsync(ruleContext,
            JToken.FromObject(new SignatureSheetField { Value = "9740 E Hampton Ave", IsReviewed = true }),
            JToken.FromObject(new SignatureSheetField { Value = "Mesa, AZ 85209", IsReviewed = true }));
        Assert.AreEqual(false, result.Value);

        // Correct Street. Wrong City
        result = await operation.EvaluateAsync(ruleContext,
            JToken.FromObject(new SignatureSheetField { Value = "9740 E Hampton Ave", IsReviewed = true }),
            JToken.FromObject(new SignatureSheetField { Value = "Flagstaff, AZ 85209", IsReviewed = true }));
        Assert.AreEqual(true, result.Value);

        // Bizarre case we saw in an actual sheet
        result = await operation.EvaluateAsync(ruleContext,
            JToken.FromObject(new SignatureSheetField
            { Value = "5350 E. Deer Valley Rd, Unit 1430", IsReviewed = true }),
            // Arizona vs AZ
            JToken.FromObject(new SignatureSheetField { Value = "Phoenix, Arizona 85054", IsReviewed = true }));
        Assert.AreEqual(false, result.Value);

        // Another actual use case
        result = await operation.EvaluateAsync(ruleContext,
            JToken.FromObject(new SignatureSheetField { Value = "3115 W Mountain View Rd", IsReviewed = true }),
            // mixed case Az
            JToken.FromObject(new SignatureSheetField { Value = "Phoenix Az 85051", IsReviewed = true }));
        Assert.AreEqual(false, result.Value);

        // This is actually a vacant lot, but is apparently a complete address
        result = await operation.EvaluateAsync(ruleContext,
            JToken.FromObject(new SignatureSheetField { Value = "8240 W Denton Ln", IsReviewed = true }),
            JToken.FromObject(new SignatureSheetField { Value = "Glendale AZ 85305", IsReviewed = true }));
        Assert.AreEqual(false, result.Value);

        // Another actual use case
        result = await operation.EvaluateAsync(ruleContext,
            // North wasn't picked up as direction, but fixed
            JToken.FromObject(new SignatureSheetField
            { Value = "6551 North Campbell Ave Apt 120", IsReviewed = true }),
            JToken.FromObject(new SignatureSheetField { Value = "Tucson AZ 85718", IsReviewed = true }));
        Assert.AreEqual(false, result.Value);
    }

}
