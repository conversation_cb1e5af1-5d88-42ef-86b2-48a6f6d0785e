﻿using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Model.SignatureSheets;
using Newtonsoft.Json.Linq;
using Service.RuleOperations;
using Service.ServiceModels;
using Service.Test.TestHelpers;

namespace Service.Test.RuleOperations;

[TestClass]
public class IsAddressLineOutOfBoundsOfTests : DatabaseTestsBase
{
    [TestInitialize]
    public async Task TestInitialize()
    {
        await InitializeAsync();
    }

    [TestMethod]
    public async Task TestIsAddressLineOutOfBounds()
    {
        var templateColumns = TemplateTestHelpers.CreateInitiative2019Columns();
        SignatureSheetCell cell;

        cell = TemplateTestHelpers.CreateCellWithAddressLine(templateColumns, "2184 Saddlewood Ranch Drive");
        await InternalTestIsAddressLineOutOfBoundsOf(cell, "Pima", false);

        cell = TemplateTestHelpers.CreateCellWithAddressLine(templateColumns, "813 W Welland Rd");
        await InternalTestIsAddressLineOutOfBoundsOf(cell, "Maricopa", false);

        cell = TemplateTestHelpers.CreateCellWithAddressLine(templateColumns, "1330 E Apollo rd");
        await InternalTestIsAddressLineOutOfBoundsOf(cell, "Maricopa", false);

        cell = TemplateTestHelpers.CreateCellWithAddressLine(templateColumns, "7816 S 48th Ln");
        await InternalTestIsAddressLineOutOfBoundsOf(cell, "Maricopa", false);

        // Sheet 9 row 3
        cell = TemplateTestHelpers.CreateCellWithAddressLine(templateColumns, "");
        await InternalTestIsAddressLineOutOfBoundsOf(cell, "Maricopa", false);

        // Sheet 9 row 10
        cell = TemplateTestHelpers.CreateCellWithAddressLine(templateColumns, "4344 E Campo Bello Dr");
        await InternalTestIsAddressLineOutOfBoundsOf(cell, "Maricopa", false);

        // Sheet 10 row 11, 15
        cell = TemplateTestHelpers.CreateCellWithAddressLine(templateColumns, "3602 W. Palm Ln");
        await InternalTestIsAddressLineOutOfBoundsOf(cell, "Maricopa", false);

        cell = TemplateTestHelpers.CreateCellWithAddressLine(templateColumns, "611 E. 4th St.");
        await InternalTestIsAddressLineOutOfBoundsOf(cell, "Maricopa", true);

        // from the 2/17 bug list
        cell = TemplateTestHelpers.CreateCellWithAddressLine(templateColumns, "7887 E Uhl");
        await InternalTestIsAddressLineOutOfBoundsOf(cell, "Maricopa", true);

        cell = TemplateTestHelpers.CreateCellWithAddressLine(templateColumns, "8449 E PENA BLANCA Dr");
        await InternalTestIsAddressLineOutOfBoundsOf(cell, "Maricopa", true);

        // A couple of out of District addresses from the 3/9 bug list
        //6501 E. Stadium Parkway (in LD19), 85756 (in LD19), and Prescott (out)
        cell = TemplateTestHelpers.CreateCellWithAddressLine(templateColumns, "6501 E. Stadium Parkway");
        await InternalTestIsAddressLineOutOfBoundsOf(cell, "Legislative District 19", false);

        // TODO: Add street Address from Eric 6/9 email:
        cell = TemplateTestHelpers.CreateCellWithAddressLine(templateColumns, "4042 E NISBET");
        await InternalTestIsAddressLineOutOfBoundsOf(cell, "Legislative District 1", true);
    }

    private async Task InternalTestIsAddressLineOutOfBoundsOf(SignatureSheetCell cell, string boundaryName,
        bool expectedResult)
    {
        var operation = _serviceProvider.GetRequiredService<IsAddressLineOutOfBoundsOf>();
        var context = new RuleContext();
        var result = await operation.EvaluateAsync(
            context, JToken.FromObject(cell), JToken.FromObject(boundaryName));
        Assert.IsTrue(result.IsSuccess, string.Join(", ", result.ErrorMessages));
        Assert.AreEqual(expectedResult, result.Value);
    }
}