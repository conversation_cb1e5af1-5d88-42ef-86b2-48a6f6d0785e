using System;
using System.Diagnostics;
using System.Threading.Tasks;
using DataInterface.ServiceInterfaces;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Model.Geocoding;

namespace Service.Test;

[TestClass]
public class BoundaryServiceTests : DatabaseTestsBase
{
    [TestInitialize]
    public async Task TestInitialize()
    {
        await InitializeAsync();
    }

    private readonly AddressInput _addressInput;
    private readonly PointD _point;

    public BoundaryServiceTests()
    {
        _addressInput = new AddressInput
        {
            AddressLine = "2184 Saddlewood Ranch Drive",
            City = "Tucson",
            State = "AZ",
            PostalCode = "85745"
        };
        _point = new PointD(32.2484320640534, -111.026067710579);
    }

    [TestMethod]
    public async Task TestIsAddressInBoundary()
    {
        var boundaryService = _serviceProvider.GetRequiredService<BoundaryService>();
        var isInBoundary = await boundaryService.IsPointInBoundary(_point, 3, "PIMA");
        Assert.IsTrue(isInBoundary);
        isInBoundary = await boundaryService.IsPointInBoundary(_point, 3,"MARICOPA");
        Assert.IsFalse(isInBoundary);
    }
}