using Microsoft.VisualStudio.TestTools.UnitTesting;
using Service.ServiceModels;

namespace Service.Test;

[TestClass]
public class GeometryServiceTests
{
    [TestMethod]
    public void TestIsContainedWithin()
    {
        var fieldBounds = new CellBounds
        {
            Left = 0.000000m,
            Top = 0.567672m,
            Right = 12.041700m,
            Bottom = 1.363309m,
        };
        var lineBounds = new CellBounds
        {
            Left = 0.000000m,
            Top = 0.585300m,
            Right = 11.900600m,
            Bottom = 0.742100m,
        };
        var result = GeometryService.IsContainedWithin(fieldBounds, lineBounds);
        Assert.IsTrue(result);
    }
}