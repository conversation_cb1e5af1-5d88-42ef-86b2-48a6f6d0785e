using DataInterface.RepositoryInterfaces;
using Model.ExternalDataSources;
using Model.SignatureSheets;
using Model.Templates;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Service.Test;

[TestClass]
public class CirculatorServiceTests
{
    private readonly Mock<ICirculatorRepository> _mockCirculatorRepository;
    private readonly Mock<ITranscribableFieldRepository> _mockTranscribableFieldRepository;
    private readonly CirculatorService _circulatorService;

    public CirculatorServiceTests()
    {
        _mockCirculatorRepository = new Mock<ICirculatorRepository>();
        _mockTranscribableFieldRepository = new Mock<ITranscribableFieldRepository>();
        _circulatorService = new CirculatorService(_mockCirculatorRepository.Object, _mockTranscribableFieldRepository.Object);
    }

    [TestMethod]
    public async Task UpdateCirculatorFromSheet_WithValidData_ShouldCreateNewCirculator()
    {
        // Arrange
        var signatureSheet = CreateTestSignatureSheet();
        var transcribableFields = CreateTestTranscribableFields();

        _mockTranscribableFieldRepository
            .Setup(x => x.GetAllByTemplateIdAsync(It.IsAny<int>()))
            .ReturnsAsync(transcribableFields);

        _mockCirculatorRepository
            .Setup(x => x.GetByCirculatorAndMatterId(It.IsAny<Circulator>(), It.IsAny<int>()))
            .ReturnsAsync((Circulator?)null);

        _mockCirculatorRepository
            .Setup(x => x.Add(It.IsAny<Circulator>()));

        _mockCirculatorRepository
            .Setup(x => x.SaveChangesAsync())
            .Returns(Task.FromResult(0));

        // Act
        var result = await _circulatorService.UpdateCirculatorFromSheetAsync(signatureSheet, transcribableFields);

        // Assert
        Assert.IsTrue(result.IsSuccess);
        Assert.IsNotNull(result.Value);
        Assert.AreEqual("John Doe", result.Value.Name);
        Assert.AreEqual("123 Main St", result.Value.Address);
        Assert.AreEqual("Phoenix", result.Value.City);
        Assert.AreEqual("85005", result.Value.PostalCode);
        Assert.AreEqual("REG123", result.Value.RegistrationId);

        _mockCirculatorRepository.Verify(x => x.Add(It.IsAny<Circulator>()), Times.Once);
        _mockCirculatorRepository.Verify(x => x.SaveChangesAsync(), Times.AtLeastOnce);
    }

    [TestMethod]
    public async Task UpdateCirculatorFromSheet_WithExistingCirculator_ShouldUseExistingCirculator()
    {
        // Arrange
        var signatureSheet = CreateTestSignatureSheet();
        var transcribableFields = CreateTestTranscribableFields();
        var existingCirculator = new Circulator
        {
            Id = 123,
            Name = "John Doe",
            Address = "123 Main St",
            City = "Phoenix",
            PostalCode = "85005",
            RegistrationId = "REG123"
        };

        _mockCirculatorRepository
            .Setup(x => x.GetByCirculatorAndMatterId(existingCirculator, 1))
            .ReturnsAsync(existingCirculator);

        _mockCirculatorRepository
            .Setup(x => x.SaveChangesAsync())
            .Returns(Task.FromResult(0));

        // Act
        var result = await _circulatorService.UpdateCirculatorFromSheetAsync(signatureSheet, transcribableFields);

        // Assert
        Assert.IsTrue(result.IsSuccess);
        Assert.IsNotNull(result.Value);
        Assert.AreEqual(123, result.Value.Id);
        Assert.AreEqual("John Doe", result.Value.Name);

        _mockCirculatorRepository.Verify(x => x.Add(It.IsAny<Circulator>()), Times.Never);
        _mockCirculatorRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
    }

    [TestMethod]
    public async Task UpdateCirculatorFromSheet_WithoutCirculatorNameField_ShouldReturnFailure()
    {
        // Arrange
        var signatureSheet = CreateTestSignatureSheet();
        var transcribableFields = new List<TranscribableField>
        {
            new TranscribableField { Id = 1, Name = "Circulator Address" },
            new TranscribableField { Id = 2, Name = "Circulator City" }
        };

        // Act
        var result = await _circulatorService.UpdateCirculatorFromSheetAsync(signatureSheet, transcribableFields);

        // Assert
        Assert.IsFalse(result.IsSuccess);
        Assert.IsTrue(result.ErrorMessages.Contains("Circulator name transcribable field not found"));
    }

    [TestMethod]
    public async Task UpdateCirculatorFromSheet_WithUnreviewedNameField_ShouldReturnFailure()
    {
        // Arrange
        var signatureSheet = CreateTestSignatureSheet();
        signatureSheet.Fields.First(f => f.TranscribableField.Name == "Circulator Name").IsReviewed = false;
        var transcribableFields = CreateTestTranscribableFields();

        // Act
        var result = await _circulatorService.UpdateCirculatorFromSheetAsync(signatureSheet, transcribableFields);

        // Assert
        Assert.IsFalse(result.IsSuccess);
        Assert.IsTrue(result.ErrorMessages.Contains("Circulator name not found"));
    }

    [TestMethod]
    public async Task UpdateCirculatorFromSheet_WithoutAddressField_ShouldReturnFailure()
    {
        // Arrange
        var signatureSheet = CreateTestSignatureSheet();
        var transcribableFields = new List<TranscribableField>
        {
            new TranscribableField { Id = 1, Name = "Circulator Name" },
            new TranscribableField { Id = 2, Name = "Circulator City" }
        };

        // Act
        var result = await _circulatorService.UpdateCirculatorFromSheetAsync(signatureSheet, transcribableFields);

        // Assert
        Assert.IsFalse(result.IsSuccess);
        Assert.IsTrue(result.ErrorMessages.Contains("Circulator address transcribable field not found"));
    }

    [TestMethod]
    public async Task UpdateCirculatorFromSheet_WithoutCityField_ShouldReturnFailure()
    {
        // Arrange
        var signatureSheet = CreateTestSignatureSheet();
        var transcribableFields = new List<TranscribableField>
        {
            new TranscribableField { Id = 1, Name = "Circulator Name" },
            new TranscribableField { Id = 2, Name = "Circulator Address" }
        };

        // Act
        var result = await _circulatorService.UpdateCirculatorFromSheetAsync(signatureSheet, transcribableFields);

        // Assert
        Assert.IsFalse(result.IsSuccess);
        Assert.IsTrue(result.ErrorMessages.Contains("Circulator city transcribable field not found"));
    }

    [TestMethod]
    public async Task UpdateCirculatorFromSheet_WithMismatchedRegistrationIds_ShouldNotSetRegistrationId()
    {
        // Arrange
        var signatureSheet = CreateTestSignatureSheet();
        signatureSheet.Fields.First(f => f.TranscribableField.Name == "Circulator ID Back").Value = "DIFFERENT";
        var transcribableFields = CreateTestTranscribableFields();

        _mockCirculatorRepository
            .Setup(x => x.GetByCirculatorAndMatterId(It.IsAny<Circulator>(), It.IsAny<int>()))
            .ReturnsAsync((Circulator?)null);

        _mockCirculatorRepository
            .Setup(x => x.SaveChangesAsync())
            .Returns(Task.FromResult(0));

        // Act
        var result = await _circulatorService.UpdateCirculatorFromSheetAsync(signatureSheet, transcribableFields);

        // Assert
        Assert.IsTrue(result.IsSuccess);
        Assert.IsNotNull(result.Value);
        Assert.IsNull(result.Value.RegistrationId);
    }

    [TestMethod]
    public async Task UpdateCirculatorFromSheet_WithOnlyFrontRegistrationId_ShouldUseFrontValue()
    {
        // Arrange
        var signatureSheet = CreateTestSignatureSheet();
        signatureSheet.Fields.First(f => f.TranscribableField.Name == "Circulator ID Back").IsReviewed = false;
        var transcribableFields = CreateTestTranscribableFields();

        _mockCirculatorRepository
            .Setup(x => x.GetByCirculatorAndMatterId(It.IsAny<Circulator>(), It.IsAny<int>()))
            .ReturnsAsync((Circulator?)null);

        _mockCirculatorRepository
            .Setup(x => x.SaveChangesAsync())
            .Returns(Task.FromResult(0));

        // Act
        var result = await _circulatorService.UpdateCirculatorFromSheetAsync(signatureSheet, transcribableFields);

        // Assert
        Assert.IsTrue(result.IsSuccess);
        Assert.IsNotNull(result.Value);
        Assert.AreEqual("REG123", result.Value.RegistrationId);
    }

    [TestMethod]
    public async Task UpdateCirculatorFromSheet_WithMultipleNameFields_ShouldPreferPrintField()
    {
        // Arrange
        var signatureSheet = CreateTestSignatureSheet();
        signatureSheet.Fields.Add(new SignatureSheetField
        {
            TranscribableField = new TranscribableField { Name = "Circulator Print Name" },
            Value = "Jane Smith",
            IsReviewed = true
        });

        var transcribableFields = CreateTestTranscribableFields();
        transcribableFields.Add(new TranscribableField { Id = 8, Name = "Circulator Print Name" });

        _mockCirculatorRepository
            .Setup(x => x.GetByCirculatorAndMatterId(It.IsAny<Circulator>(), It.IsAny<int>()))
            .ReturnsAsync((Circulator?)null);

        _mockCirculatorRepository
            .Setup(x => x.SaveChangesAsync())
            .Returns(Task.FromResult(0));

        // Act
        var result = await _circulatorService.UpdateCirculatorFromSheetAsync(signatureSheet, transcribableFields);

        // Assert
        Assert.IsTrue(result.IsSuccess);
        Assert.IsNotNull(result.Value);
        Assert.AreEqual("Jane Smith", result.Value.Name);
    }

    [TestMethod]
    public async Task UpdateCirculatorFromSheet_WithNullTranscribableFields_ShouldFetchFromRepository()
    {
        // Arrange
        var signatureSheet = CreateTestSignatureSheet();
        var transcribableFields = CreateTestTranscribableFields();

        _mockTranscribableFieldRepository
            .Setup(x => x.GetAllByTemplateIdAsync(signatureSheet.TemplateId))
            .ReturnsAsync(transcribableFields);

        _mockCirculatorRepository
            .Setup(x => x.GetByCirculatorAndMatterId(It.IsAny<Circulator>(), It.IsAny<int>()))
            .ReturnsAsync((Circulator?)null);

        _mockCirculatorRepository
            .Setup(x => x.SaveChangesAsync())
            .Returns(Task.FromResult(0));

        // Act
        var result = await _circulatorService.UpdateCirculatorFromSheetAsync(signatureSheet, null);

        // Assert
        Assert.IsTrue(result.IsSuccess);
        _mockTranscribableFieldRepository.Verify(x => x.GetAllByTemplateIdAsync(signatureSheet.TemplateId), Times.Once);
    }

    private SignatureSheet CreateTestSignatureSheet()
    {
        return new SignatureSheet
        {
            Id = 1,
            MatterId = 1,
            TemplateId = 1,
            CirculatorId = null,
            Fields = new List<SignatureSheetField>
            {
                new SignatureSheetField
                {
                    TranscribableField = new TranscribableField { Name = "Circulator Name" },
                    Value = "John Doe",
                    IsReviewed = true
                },
                new SignatureSheetField
                {
                    TranscribableField = new TranscribableField { Name = "Circulator Address" },
                    Value = "123 Main St",
                    IsReviewed = true
                },
                new SignatureSheetField
                {
                    TranscribableField = new TranscribableField { Name = "Circulator City" },
                    Value = "Phoenix, CO 85005",
                    IsReviewed = true
                },
                new()
                {
                    TranscribableField = new TranscribableField { Name = "Circulator ID Front" },
                    Value = "REG123",
                    IsReviewed = true
                },
                new()
                {
                    TranscribableField = new TranscribableField { Name = "Circulator ID Back" },
                    Value = "REG123",
                    IsReviewed = true
                }
            }
        };
    }

    private List<TranscribableField> CreateTestTranscribableFields()
    {
        return new List<TranscribableField>
        {
            new TranscribableField { Id = 1, Name = "Circulator Name" },
            new TranscribableField { Id = 2, Name = "Circulator Address" },
            new TranscribableField { Id = 3, Name = "Circulator City" },
            new TranscribableField { Id = 4, Name = "Circulator ID Front" },
            new TranscribableField { Id = 5, Name = "Circulator ID Back" }
        };
    }
}