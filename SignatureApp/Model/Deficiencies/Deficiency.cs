using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Model.Authorization;
using Model.Interfaces;
using Model.Matters;
using Model.Rules;
using Model.SignatureSheets;

namespace Model.Deficiencies;

public class Deficiency : IHaveId
{
    public int Id { get; set; }

    public int RecordId { get; set; }
    public RecordIdType RecordIdType { get; set; }

    // There are some rules that actually require two records to be a deficiency
    // Duplicate voters is one of them
    public int? OtherRecordId { get; set; }
    public RecordIdType? OtherRecordIdType { get; set; }

    [StringLength(4096)]
    public string? Note { get; set; }
    public int? WorkId { get; set; }

    public bool NeedsReview { get; set; }

    public bool? IsDeficient { get; set; }
    [StringLength(StringLengthConstants.EmailAddress)]
    public string? ReviewedBy { get; set; }
    public DateTime? ReviewedOn { get; set; }

    public int RuleId { get; set; }
    [JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    public Rule Rule { get; set; } = default!;

    public int MatterId { get; set; }
    [JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    public Matter Matter { get; set; } = default!;

    public int? UserId { get; set; }
    [JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    public User? User { get; set; }

    public int? SignatureSheetId { get; set; }

    [JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    public virtual SignatureSheet SignatureSheet { get; init; } = default!;
}