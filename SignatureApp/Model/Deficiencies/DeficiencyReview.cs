using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Model.Authorization;
using Model.Interfaces;
using Model.Matters;
using Model.Rules;
using Model.SignatureSheets;

namespace Model.Deficiencies;

public class DeficiencyReview : IHaveId, IAssignable
{
    public int Id { get; set; }
    public bool? IsDeficient { get; set; }

    public int RecordId { get; set; }
    public RecordIdType RecordIdType { get; set; }
    [StringLength(4096)]
    public string? Note { get; set; }

    public int RuleId { get; set; }
    [JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    public Rule Rule { get; set; } = default!;

    public int MatterId { get; set; }
    [JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    public Matter Matter { get; set; } = default!;

    public int? UserId { get; set; }

    [StringLength(StringLengthConstants.EmailAddress)]
    public string? AssignedTo { get; set; } // Note this is the denormalized email address of the user assigned to review

    public DateTime? AssignmentDate { get; set; }

    public DateTime? ReviewedDate { get; set; }

    [JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    public User? User { get; set; }

    public int? SignatureSheetId { get; set; }
    [JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    public virtual SignatureSheet? SignatureSheet { get; set; } = default!;
}