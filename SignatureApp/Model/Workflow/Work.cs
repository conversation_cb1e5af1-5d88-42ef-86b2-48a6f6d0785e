﻿using Model.Authorization;
using Model.Interfaces;
using Model.Matters;
using Model.SignatureSheets;
using System.Text.Json.Serialization;

namespace Model.Workflow
{
    public class Work : IHaveId, IAssignable
    {
        public int Id { get; set; }
        public int? UserId { get; set; }
        public int TaskId { get; set; }

        public DateTime LastStartDateTime { get; set; }
        public DateTime LastStopDateTime { get; set; }
        public double SecondsWorked { get; set; } // this should be a maximum of 5 minutes or 300s.
        public DateTime? AssignmentDate { get; set; }

        public WorkStatus WorkStatus { get; set; } = WorkStatus.Assigned;

        public int SheetNumber { get; set; }
        public int RowNumber { get; set; }
        public int FieldNumber { get; set; }

        [JsonIgnore]
        [Newtonsoft.Json.JsonIgnore]
        public User User { get; set; } = default!;

        [JsonIgnore]
        [Newtonsoft.Json.JsonIgnore]
        public Task Task { get; set; } = default!;

        public int MatterId { get; set; }
        [JsonIgnore]
        [Newtonsoft.Json.JsonIgnore]
        public Matter Matter { get; set; } = default!;

        public int SignatureSheetId { get; set; }
        [JsonIgnore]
        [Newtonsoft.Json.JsonIgnore]
        public SignatureSheet SignatureSheet { get; set; } = default!;

        public virtual IList<WorkField> WorkFields { get; set; } = default!;
    }
}