﻿using Model.ReferenceData;

namespace Model.Geocoding;

public interface IFullAddress 
{
    string? AddressLine { get; set; }
    string? City { get; set; }
    string State { get; set; }
    string? PostalCode { get; set; }
}


public class AddressInput : IFullAddress
{
    public string? AddressLine { get; set; }
    public string? City { get; set; }
    public string State { get; set; } = "AZ";
    public string? PostalCode { get; set; }
    public string Country { get; set; } = "US";

    public override string ToString()
    {
        return
            $"{(AddressLine != null ? AddressLine + ", " : "")}{(City != null ? City + ", " : "")}{State + " "}{(PostalCode != null ? PostalCode + ", " : "")}{Country}";
    }
}

public interface IStreetAddress
{
    string? StreetNumber { get; set; }
    string? Direction { get; set; }
    string? StreetName { get; set; }
    string? StreetType { get; set; }
}

public class ParsedAddress : IStreetAddress
{
    public string? AddressLine { get; set; }
    public string? City { get; set; }
    public UsState? State { get; set; }
    public string? PostalCode { get; set; }
    public string? StreetNumber { get; set; }
    public string? StreetType { get; set; }
    public string? Direction { get; set; }
    public string? StreetName { get; set; }
}