namespace Model.Geocoding.AzureMaps;

public class AzureMapsGeocodeResponse
{
    public Summary Summary { get; set; } = default!;
    public Results[]? Results { get; set; }
}

public class Summary
{
    public string? Query { get; set; }
    public string? QueryType { get; set; }
    public int QueryTime { get; set; }
    public int NumResults { get; set; }
    public int Offset { get; set; }
    public int TotalResults { get; set; }
    public int FuzzyLevel { get; set; }
}

public class Results
{
    public string? Type { get; set; }
    public string? Id { get; set; }
    public double Score { get; set; }
    public MatchConfidence? MatchConfidence { get; set; }
    public AzureMapsAddress Address { get; set; } = default!;
    public Position Position { get; set; } = default!;
    public Viewport Viewport { get; set; } = default!;
    public EntryPoints[]? EntryPoints { get; set; }
    public string? EntityType { get; set; }
    public BoundingBox? BoundingBox { get; set; }
    public DataSources? DataSources { get; set; }
}

public class MatchConfidence
{
    public double Score { get; set; }
}

public class AzureMapsAddress
{
    public string StreetNumber { get; set; } = default!;
    public string StreetName { get; set; } = default!;
    public string? Municipality { get; set; }
    public string? CountrySecondarySubdivision { get; set; }
    public string? CountrySubdivision { get; set; }
    public string? CountrySubdivisionName { get; set; }
    public string CountrySubdivisionCode { get; set; } = default!;
    public string? PostalCode { get; set; }
    public string? ExtendedPostalCode { get; set; }
    public string? CountryCode { get; set; }
    public string? Country { get; set; }
    public string? CountryCodeIso3 { get; set; }
    public string? FreeformAddress { get; set; }
    public string? LocalName { get; set; }
    public string? MunicipalitySubdivision { get; set; }
    public string? Neighbourhood { get; set; }
}

public class Position
{
    public double Lat { get; set; }
    public double Lon { get; set; }
}

public class Viewport
{
    public TopLeftPoint? TopLeftPoint { get; set; }
    public BtmRightPoint? BtmRightPoint { get; set; }
}

public class TopLeftPoint
{
    public double Lat { get; set; }
    public double Lon { get; set; }
}

public class BtmRightPoint
{
    public double Lat { get; set; }
    public double Lon { get; set; }
}

public class EntryPoints
{
    public string? Type { get; set; }
    public Position1? Position { get; set; }
}

public class Position1
{
    public double Lat { get; set; }
    public double Lon { get; set; }
}

public class BoundingBox
{
    public TopLeftPoint1? TopLeftPoint { get; set; }
    public BtmRightPoint1? BtmRightPoint { get; set; }
}

public class TopLeftPoint1
{
    public double Lat { get; set; }
    public double Lon { get; set; }
}

public class BtmRightPoint1
{
    public double Lat { get; set; }
    public double Lon { get; set; }
}

public class DataSources
{
    public Geometry? Geometry { get; set; }
}

public class Geometry
{
    public string? Id { get; set; }
}
