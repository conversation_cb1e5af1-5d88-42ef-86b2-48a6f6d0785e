﻿using Model.Interfaces;
using Model.Matters;
using System.ComponentModel.DataAnnotations;
using Model.Deficiencies;

namespace Model.Rules
{
    public class Rule : IHaveId
    {
        public Rule()
        {
        }

        public static Rule CreateTranscriptionRule(int matterId, int taskId, string ruleName)
        {
            return new Rule
            {
                MatterId = matterId,
                TaskId = taskId,
                RuleContextType = RuleContextType.Transcription,
                Name = ruleName,
                LeftHandExpression = string.Empty,
                OperationName = string.Empty,
                RightHandExpression = string.Empty,
            };
        }

        public static Rule CreateNonTaskRule(int matterId, string ruleName,
            RuleContextType ruleContextType, string lhs, string op, string rhs, bool needsReview = false)
        {
            return new Rule
            {
                MatterId = matterId,
                RuleContextType = ruleContextType,
                Name = ruleName,
                LeftHandExpression = lhs,
                OperationName = op,
                RightHandExpression = rhs,
                NeedsReview = needsReview,
            };
        }

        public static Rule CreateTaskRule(int matterId, int taskId, string ruleName, RuleContextType ruleContextType, string lhs, string op, string rhs)
        {
            return new Rule
            {
                MatterId = matterId,
                TaskId = taskId,
                RuleContextType = ruleContextType,
                Name = ruleName,
                LeftHandExpression = lhs,
                OperationName = op,
                RightHandExpression = rhs,
            };
        }



        public int Id { get; set; }

        [StringLength(200)]
        public string Name { get; set; } = default!;

        [StringLength(4000)]
        public string? Description { get; set; } = default!;

        [StringLength(200)]
        public string LeftHandExpression { get; set; } = default!;

        [StringLength(200)]
        public string OperationName { get; set; } = default!;

        [StringLength(200)]
        public string RightHandExpression { get; set; } = default!;

        public bool NeedsReview { get; set; }

        public RuleContextType RuleContextType { get; set; }
        public int? TaskId { get; set; }
        public Workflow.Task? Task { get; set; }

        public int MatterId { get; set; }
        public Matter Matter { get; set; } = default!;

        public virtual IList<Deficiency> Deficiencies { get; set; } = new List<Deficiency>();
    }
}
