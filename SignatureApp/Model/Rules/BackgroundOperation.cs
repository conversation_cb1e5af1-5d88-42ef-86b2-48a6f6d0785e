﻿using Model.Interfaces;
using System.ComponentModel.DataAnnotations;
using Model.Matters;

namespace Model.Rules
{
    public enum BackgroundOperationType
    {
        Unknown,
        RunRules,
        UploadSignatureSheets,
        UploadInvalidVoterFile,
        UploadInvalidCirculatorFile,
        DeleteVoterRegistrationFile,
        DeleteCirculatorFile,
    }
    public class BackgroundOperation : IHaveId, ITimeable
    {
        public int Id { get; set; }

        public ExecutionStatus ExecutionStatus { get; set; }
        public BackgroundOperationType OperationType { get; set; }

        [StringLength(StringLengthConstants.EmailAddress)]
        public string? StartedBy { get; set; }

        public DateTime? StartedOn { get; set; }

        public DateTime? EndedOn { get; set; }

        [StringLength(4000)]
        public string? Message { get; set; }
        public int? MatterId { get; set; }
        public virtual Matter? Matter { get; set; }
    }

    public interface ITimeable
    {
        [StringLength(StringLengthConstants.EmailAddress)]
        public string? StartedBy { get; set; }
        public DateTime? StartedOn { get; set; }

        public DateTime? EndedOn { get; set; }
    }
}
