﻿using Model.Interfaces;
using System.Text.Json.Serialization;

namespace Model.SignatureSheets;

public class SignatureSheetColumn : IHaveId, IHaveSimpleBounds
{
    public int Id { get; set; }

    public int SignatureSheetTableId { get; set; }
    [JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    public SignatureSheetTable SignatureSheetTable { get; set; } = default!;

    public int ColumnIndex { get; set; }

    public decimal Left { get; set; }
    public decimal Top { get; set; }
    public decimal Right { get; set; }
    public decimal Bottom { get; set; }

    public bool IsMissing { get; set; }
}
