﻿using Model.Interfaces;
using Model.Templates;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Model.SignatureSheets;

public class SignatureSheetField : IHaveId, IHaveSimpleBounds, IReviewAuditEntity
{
    public int Id { get; set; }
    public int SignatureSheetId { get; set; }

    [JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    public SignatureSheet SignatureSheet { get; set; } = default!;

    public int TranscribableFieldId { get; set; }

    public TranscribableField TranscribableField { get; set; } = default!;

    [StringLength(StringLengthConstants.FieldValue)]
    public string? Value { get; set; }

    public bool IsMissing { get; set; }
    public Validity Validity { get; set; }

    public decimal Left { get; set; }
    public decimal Top { get; set; }
    public decimal Right { get; set; }
    public decimal Bottom { get; set; }

    public int Page { get; set; }
    public bool IsReviewed { get; set; }
    public DateTime? ReviewedOn { get ; set ; }
    [StringLength(StringLengthConstants.EmailAddress)]
    public string? ReviewedBy { get; set; }
    // public bool IsParsed { get; set; }  //false if we can't find it

}
