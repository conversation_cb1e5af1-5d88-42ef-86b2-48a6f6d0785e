﻿using Model.Interfaces;
using Model.Templates;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace Model.SignatureSheets;

public class SignatureSheetCell : IHaveId, IHaveSimpleBounds, IReviewAuditEntity
{
    public int Id { get; set; }

    public int SignatureSheetRowId { get; set; }
    [JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    public SignatureSheetRow SignatureSheetRow { get; set; } = default!;

    public int TemplateSignatureColumnId { get; set; }
    public TemplateSignatureColumn TemplateSignatureColumn { get; set; } = default!;

    public decimal Left { get; set; }
    public decimal Top { get; set; }
    public decimal Right { get; set; }
    public decimal Bottom { get; set; }

    [StringLength(StringLengthConstants.CellValue)]
    public string? Value { get; set; }

    public bool IsMissing { get; set; }
    public Validity Validity { get; set; }
    public bool IsReviewed { get; set; }
    public DateTime? ReviewedOn { get; set; }
    [StringLength(StringLengthConstants.EmailAddress)]
    public string? ReviewedBy { get; set; }
    public int ColumnIndex { get; set; }
    public int RowIndex { get; set; }

    [NotMapped]
    public int ColumnSpan { get; set; } = 1;
    [NotMapped]
    public int RowSpan { get; set; } = 1;

}
