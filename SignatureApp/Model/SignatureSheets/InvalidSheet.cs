using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Model.Interfaces;
using Model.Matters;
using Model.Templates;

namespace Model.SignatureSheets;

public class InvalidSheet: IHaveId, IAssignable
{
    public int Id { get; set; }
    public int MatterId { get; set; }
    [JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    public Matter Matter { get; set; } = default!;

    public int TemplateId { get; set; }

    [JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    public Template Template { get; set; } = default!;

    public int SheetNumber { get; set; }

    public int? UserId { get; set; }

    [StringLength(StringLengthConstants.EmailAddress)]
    public string? AssignedTo { get; set; } // Note this is the denormalized email address of the user assigned to this invalid sheet.

    public DateTime? AssignmentDate { get; set; }

    public InvalidSheetStatus Status { get; set; }

    public string Filename { get; set; } = default!;

    public int? ValidSignatureSheetId { get; set; }
    public int SignatureSheetUploadId { get; set; }
    public virtual SignatureSheetUpload SignatureSheetUpload { get; set; } = default!;
}
