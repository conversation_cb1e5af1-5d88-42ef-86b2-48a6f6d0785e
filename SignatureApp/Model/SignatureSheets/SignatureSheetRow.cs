﻿using Model.ExternalDataSources;
using Model.Interfaces;
using Model.Templates;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Model.SignatureSheets;

public class SignatureSheetRow : IHaveId, IHaveSimpleBounds, IReviewAuditEntity
{
    public int Id { get; set; }
    public int SignatureSheetId { get; set; }

    [JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    public SignatureSheet SignatureSheet { get; set; } = default!;

    // This one is from the template
    public int TemplateSignatureTableId { get; set; }

    // This one is from the Sheet
    public int SignatureSheetTableId { get; set; }
    [JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    public SignatureSheetTable SignatureSheetTable { get; set; } = default!;

    [JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    public TemplateSignatureTable TemplateSignatureTable { get; set; } = default!;

    public int? RegisteredVoterId { get; set; }
    public RegisteredVoter? RegisteredVoter { get; set; }

    public int? SignatoryId { get; set; }
    public Signatory? Signatory { get; set; }

    public RegisteredVoterFlags RegisteredVoterFlags { get; set; }

    public decimal Left { get; set; }
    public decimal Top { get; set; }
    public decimal Right { get; set; }
    public decimal Bottom { get; set; }

    public bool IsMissing { get; set; }
    public Validity Validity { get; set; }
    public bool IsReviewed { get; set; }
    public DateTime? ReviewedOn { get ; set ; }
    [StringLength(StringLengthConstants.EmailAddress)]
    public string? ReviewedBy { get; set; }
    public int RowIndex { get; set; }
    public int RowNumber { get; set; }

    public virtual IList<SignatureSheetCell> Cells { get; set; } = new List<SignatureSheetCell>();
}
