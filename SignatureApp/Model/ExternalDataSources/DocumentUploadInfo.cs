﻿using System.ComponentModel.DataAnnotations;

namespace Model.ExternalDataSources;

public class DocumentUploadInfoByCounty : DocumentUploadInfo
{
    [StringLength(100)]
    public string County { get; set; } = default!;
}

public class DocumentUploadInfo
{
    public int TotalValidCount { get; set; }

    public int? LastUploadId { get; set; }

    [StringLength(StringLengthConstants.EmailAddress)]
    public string? LastUploadedBy { get; set; }
    public DateTime? LastUploadedOn { get; set; }
    public int UploadCount { get; set; }
}
