using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Model.Geocoding;
using Model.Interfaces;
using Model.ReferenceData;
using Model.SignatureSheets;

namespace Model.ExternalDataSources;

public class Circulator : IHaveId
{
    public int Id { get; set; }

    [StringLength(10)]
    public string? RegistrationId { get; set; } = default!;

    [StringLength(200)]
    public string Name { get; set; } = default!;

    [StringLength(100)]
    public string? Address { get; set; }

    [StringLength(StringLengthConstants.City)]
    public string? City { get; set; }

    public int UsStateId { get; set; } = 3;
    public UsState? UsState { get; set; }

    [StringLength(15)]
    public string? PostalCode { get; set; }

    [JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    public IList<SignatureSheet> SignatureSheets { get; set; } = new List<SignatureSheet>();
}