using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;
using Model.Geocoding;
using Model.Interfaces;
using Model.ReferenceData;
using Model.SignatureSheets;

namespace Model.ExternalDataSources;

public class Signatory: IHaveId, IStreetAddress
{
    public int Id { get; set; }

    [StringLength(StringLengthConstants.LastName)]
    public string LastName { get; set; } = default!;

    [StringLength(StringLengthConstants.FirstName)]
    public string FirstName { get; set; } = default!;

    [StringLength(100)]
    public string? StreetName { get; set; }

    [StringLength(15)]
    public string? Direction { get; set; }

    [StringLength(15)]
    public string? StreetNumber { get; set; }

    [StringLength(50)]
    public string? StreetType { get; set; }

    [StringLength(100)]
    public string? City { get; set; }

    public int UsStateId { get; set; } = 3;
    public UsState UsState { get; set; } = default!;

    [StringLength(15)]
    public string? PostalCode { get; set; }

    public DateTime DateSigned { get; set; }

    [Column(TypeName = "decimal(18,15)")]
    public decimal? Latitude { get; set; }
    [Column(TypeName = "decimal(18,15)")]
    public decimal? Longitude { get; set; }

    [StringLength(4000)]
    public string? GeocodeErrors { get; set; }

    [NotMapped]
    public string? FullAddress { get; set; }

    [JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    public IList<SignatureSheetRow> SignatureSheetRows { get; set; } = new List<SignatureSheetRow>();
}