using Model.Interfaces;
using Model.Rules;
using Model.SignatureSheets;
using Model.Workflow;

namespace Model.DTOs;

public class WorkDTO : IHaveExecutionStatus
{
    public ExecutionStatus ExecutionStatus { get; set; }
    public string Message { get; set; } = default!;
    public int WorkId { get; set; }
    public TaskType TaskTypeId { get; set; }
    public List<WorkFieldDTO> Fields { get; set; } = default!;
    public byte[] ImageBytes { get; set; } = default!;
    public string TaskName { get; set; } = default!;
    public string TaskDescription { get; set; } = default!;
    public bool CanBeInvalid { get; set; }
    public List<ViolationCriterion>? ViolationCriteria { get; set; } = new List<ViolationCriterion>();
    public int? ExternalDataRecordId { get; set; }
    public bool ShowWholeColumn { get; set; }
    public int MatterId { get; set; }
    public string MatterName { get; set; } = default!;
    public int SheetNumber { get; set; }
    public int RowNumber { get; set; }
    public int FieldNumber { get; set; }
    public RegisteredVoterFlags RegisteredVoterFlags { get; set; } = default!;
}