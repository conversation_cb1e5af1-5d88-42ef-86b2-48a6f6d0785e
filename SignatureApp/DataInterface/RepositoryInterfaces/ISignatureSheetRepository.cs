﻿using Model.ExternalDataSources;
using Model.SignatureSheets;

namespace DataInterface.RepositoryInterfaces;

public interface ISignatureSheetRepository : IBaseRepository<SignatureSheet>
{
    Task<int> GetCountByMatterIdAsync(int matterId);
    Task<(int min, int max)> GetMinMaxByMatterAsync(int matterId);
    Task<DocumentUploadInfo> GetUploadInfoByMatterIdAsync(int matterId);
    Task<List<SignatureSheet>> GetAllByMatterIdAsync(int matterId);
    Task<List<SignatureSheet>> GetByMatterTemplateAndFilenameAsync(int matterId, int templateId, string filename);
    Task<SignatureSheet?> GetByIdWithRowsAsync(int signatureSheetId);
    Task<List<SignatureSheet>> GetBySignatureSheetUploadIdAsync(int signatureSheetUploadId);
    Task<SignatureSheet?> GetByMatterAndSheetNumberAsync(int matterId, int sheetNumber);
    Task<int[]> GetSheetNumbersByMatterAsync(int matterId);
    Task<List<SignatureSheet>> GetAllFieldReviewedAsync();
}