﻿using Model.Authorization;
using Model.Workflow;
using System.Linq.Expressions;
using Model.DTOs;
using Task = System.Threading.Tasks.Task;

namespace DataInterface.RepositoryInterfaces
{
    public interface IWorkRepository : IBaseRepository<Work>
    {
        Task<Work?> GetPreviousWorkByUserIdAsync(DateTime? assignmentDate, int userId, int workId);

        Task<Work?> GetNextWorkFromTasksAsync(List<Model.Workflow.Task> tasks, int userId, int workId,
            DateTime? assignmentDate);

        Task<Work?> GetWorkWithTaskIncludedAsync(int workId);
        Task<Work?> GetNextUnassignedWorkAsync(List<Model.Workflow.Task> tasks, int userId);
        Task<Work?> GetNextFlaggedWorkAsync(int matterId, int userId);
        Task<bool> IsAllWorkCompleteAsync(int matterId);
        Task<int> GetCountByMatterIdAndExpressionAsync(int matterId, Expression<Func<Work, bool>> pred);
        Task<List<WorkMatterStatus>> GetAllWorkByMatterAndStatusAsync();
        Task<List<WorkTaskStatus>> GetAllWorkByMatterIdAndTaskAsync(int matterId);
        Task UpdateAllDurationOverLimitAsync();
        Task<(double totalSeconds, int totalFlagged, int totalCompleted)> GetTotalsByUserAsync(int userId);
        Task<Work?> GetBySheetIdAndFieldNumber(int sheetId, int transcribableFieldFieldNumber);
        Task<Work?> GetByFieldIdAsync(int fieldId);
        Task<Work?> GetByCellIdAsync(int cellId);
        Task<Work?> GetVoterRegistrationWorkByRowIdAsync(int matterId, int sheetId, int rowId);
        Task<bool> IsAllWorkBySheetIdCompleteAsync(int signatureSheetId);

        Task<List<Work>> GetAllForMatterAndSheetNumberAsync(int matterId, int sheetNumber,
            Expression<Func<Work, bool>>? pred = null);

        Task<List<Work>> GetAllWorkByMatterIdAsync(int matterId);
        Task<List<Work>> GetAllWorkByTaskId(int taskId);
        Task<List<Work>> GetAllForMatterSheetAndRowNumberAsync(int matterId, int sheetNumber, int rowNumber);
        Task<List<Work>> GetAllFieldsForMatterSheetAndPageNumberAsync(int matterId, int sheetNumber, int? pageNumber);
    }
}