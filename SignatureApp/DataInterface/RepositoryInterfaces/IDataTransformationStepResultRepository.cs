﻿using Model.DataTransformation;
using Model.SignatureSheets;

namespace DataInterface.RepositoryInterfaces;

public interface IDataTransformationStepResultRepository : IBaseRepository<DataTransformationStepResult>
{
    Task<List<DataTransformationStepResult>> GetByUploadIdAsync(int uploadId);
    Task<DataTransformationStepResult?> GetByUploadIdAndResultFilenameAsync(int uploadId, string newFilePath);
    Task<List<DataTransformationStepResult>> GetByMatterIdAsync(int matterId);
    Task<List<InvalidSheet>> GetInvalidSheetsByMatterIdAsync(int matterId);

}
