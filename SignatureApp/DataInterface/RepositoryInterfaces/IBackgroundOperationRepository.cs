﻿using Model.Rules;

namespace DataInterface.RepositoryInterfaces;

public interface IBackgroundOperationRepository : IBaseRepository<BackgroundOperation>
{
    Task<bool> DeleteByIdAsync(int runRulesId);
    Task<BackgroundOperation?> GetRunningJobIdAsync(int matterId, BackgroundOperationType operationType);
    Task<BackgroundOperation?> GetTimeSpanSinceOperationCompletedAsync(int matterId, BackgroundOperationType operationType);
    Task TerminateRunningJobsAsync(int matterId);
}
