﻿using Model.Deficiencies;

namespace DataInterface.RepositoryInterfaces
{
    public interface IDeficiencyReviewRepository : IBaseRepository<DeficiencyReview>
    {
        Task<int[]> GetAllNonReviewedIdsByMatterAsync(int matterId);
        Task<DeficiencyReview?> GetByDeficiencyAsync(Deficiency deficiency);
        Task UpdateReviewedStatusAsync(Deficiency deficiency, bool isDeficient, string? note);

        Task<DeficiencyReview?> GetNextByMatterIdAsync(int matterId, string userEmail);
        Task AssignDeficiencyReviewAsync(DeficiencyReview deficiencyReview, string userEmail);
    }
}
