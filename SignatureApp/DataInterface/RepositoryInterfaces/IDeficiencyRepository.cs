﻿using Model.Authorization;
using Model.Deficiencies;
using Model.Rules;
using Model.SignatureSheets;

namespace DataInterface.RepositoryInterfaces
{
    public interface IDeficiencyRepository : IBaseRepository<Deficiency>
    {
        Task<List<Deficiency>> GetAllByMatterAsync(int matterId);
        Task<List<Deficiency>> GetAllByMatterIncludeSheetAsync(int matterId);
        Task<List<Deficiency>> GetOnlyMatterDeficiencies(int matterId);
        Task<List<Deficiency>> GetAllRowDeficienciesAsync(int matterId);
        Task<List<Deficiency>> GetAllCellDeficienciesAsync(int matterId);
        Task<List<Deficiency>> GetAllSheetFieldDeficienciesAsync(int matterId);
        Task<List<Deficiency>> GetDeficienciesByRuleAsync(int matterId, int ruleId);
        Task<List<Deficiency>> GetDeficienciesByRecordAsync(int recordId, RecordIdType recordIdType);
        Task<Deficiency?> GetDeficiencyByRecordAndRuleAsync(int recordId, RecordIdType recordIdType, int ruleId);
        Task DeleteRulesGeneratedDeficienciesAsync(int matterId);
        Task<int> GetDeficiencyCountByUserAsync(int userId);
        Task<Deficiency?> UpdateReviewedStatusAsync(int deficiencyId, User user, bool isDeficient, string? note);
        Task<List<Deficiency>> GetAllRuleGeneratedDeficienciesByMatterIdAsync(int matterId);
        Task<List<DeficiencyReview>> GetAllDeficiencyReviewsByMatterIdAsync(int matterId);
        Task DeleteReviewedNonDeficienciesAsync(int matterId);
        Task UpdateReviewedDeficienciesAsync(int matterId);
        Task<List<DeficiencyAndRows>> GetRowDeficienciesForSheetDeficiencies(int matterId);
        Task<List<Deficiency>> GetRowDeficienciesForSheetDeficiency(int matterId, int sheetId, int ruleId);
        Task<List<Deficiency>> GetOnlySheetDeficiencies(int sheetId);
        Task<Deficiency?> GetDeficiencyAndSignatureSheetByIdAsync(int deficiencyId);
        Task<Deficiency?> GetByDeficiencyReviewAsync(DeficiencyReview deficiencyReview);
    }
}
