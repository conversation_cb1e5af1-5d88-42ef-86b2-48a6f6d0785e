﻿using Model.ExternalDataSources;

namespace DataInterface.RepositoryInterfaces;

public interface IRegisteredVoterRepository : IBaseRepository<RegisteredVoter>
{
    Task<int> GetCountByMatterIdAsync(int matterId);
    Task<int[]> GetUploadsByMatterIdAsync(int matterId);
    Task<List<DocumentUploadInfoByCounty>> GetUploadInfoByMatterIdAsync(int matterId);
    Task<(List<RegisteredVoter> Results, int TotalHits)> SearchByCriteriaAsync(int[] externalDbIds,
        RegisteredVoter registeredVoter, int maxTake = 10);

    Task<List<RegisteredVoter>> GetChunkFromDbOrderedByVoterId(int chunkSize, int maxVoterIdInChunkFromDb,
        int minVoterIdInChunkToDb, int maxVoterIdInChunkToDb);

    void UpdateRange(List<RegisteredVoter> listToUpdate);
}
