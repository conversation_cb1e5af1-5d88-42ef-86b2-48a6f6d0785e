﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Data.Migrations
{
    /// <inheritdoc />
    public partial class MarkInvalidValidAddIsMissing : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_SignatureSheetFields_SignatureSheetId",
                table: "SignatureSheetFields");

            migrationBuilder.DropIndex(
                name: "IX_SignatureSheetCells_SignatureSheetRowId",
                table: "SignatureSheetCells");

            migrationBuilder.AddColumn<bool>(
                name: "IsMissing",
                table: "SignatureSheetRows",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsMissing",
                table: "SignatureSheetFields",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsMissing",
                table: "SignatureSheetColumns",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsMissing",
                table: "SignatureSheetCells",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.CreateIndex(
                name: "IX_SignatureSheetFields_SignatureSheetId",
                table: "SignatureSheetFields",
                column: "SignatureSheetId")
                .Annotation("SqlServer:Include", new[] { "Bottom", "IsMissing", "IsReviewed", "Left", "Page", "ReviewedBy", "ReviewedOn", "Right", "Top", "TranscribableFieldId", "Validity", "Value" });

            migrationBuilder.CreateIndex(
                name: "IX_SignatureSheetCells_SignatureSheetRowId",
                table: "SignatureSheetCells",
                column: "SignatureSheetRowId")
                .Annotation("SqlServer:Include", new[] { "Bottom", "ColumnIndex", "IsMissing", "IsReviewed", "Left", "ReviewedBy", "ReviewedOn", "Right", "RowIndex", "TemplateSignatureColumnId", "Top", "Validity", "Value" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_SignatureSheetFields_SignatureSheetId",
                table: "SignatureSheetFields");

            migrationBuilder.DropIndex(
                name: "IX_SignatureSheetCells_SignatureSheetRowId",
                table: "SignatureSheetCells");

            migrationBuilder.DropColumn(
                name: "IsMissing",
                table: "SignatureSheetRows");

            migrationBuilder.DropColumn(
                name: "IsMissing",
                table: "SignatureSheetFields");

            migrationBuilder.DropColumn(
                name: "IsMissing",
                table: "SignatureSheetColumns");

            migrationBuilder.DropColumn(
                name: "IsMissing",
                table: "SignatureSheetCells");

            migrationBuilder.CreateIndex(
                name: "IX_SignatureSheetFields_SignatureSheetId",
                table: "SignatureSheetFields",
                column: "SignatureSheetId")
                .Annotation("SqlServer:Include", new[] { "Bottom", "IsReviewed", "Left", "Page", "ReviewedBy", "ReviewedOn", "Right", "Top", "TranscribableFieldId", "Validity", "Value" });

            migrationBuilder.CreateIndex(
                name: "IX_SignatureSheetCells_SignatureSheetRowId",
                table: "SignatureSheetCells",
                column: "SignatureSheetRowId")
                .Annotation("SqlServer:Include", new[] { "Bottom", "ColumnIndex", "IsReviewed", "Left", "ReviewedBy", "ReviewedOn", "Right", "RowIndex", "TemplateSignatureColumnId", "Top", "Validity", "Value" });
        }
    }
}
