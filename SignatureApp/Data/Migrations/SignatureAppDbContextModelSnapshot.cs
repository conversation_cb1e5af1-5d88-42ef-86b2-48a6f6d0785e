﻿// <auto-generated />
using System;
using Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Data.Migrations
{
    [DbContext(typeof(SignatureAppDbContext))]
    partial class SignatureAppDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Model.Authorization.Role", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.ToTable("Roles");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Name = "Admin"
                        },
                        new
                        {
                            Id = 2,
                            Name = "Manager"
                        },
                        new
                        {
                            Id = 4,
                            Name = "Unauthorized"
                        },
                        new
                        {
                            Id = 3,
                            Name = "Reviewer"
                        });
                });

            modelBuilder.Entity("Model.Authorization.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("FullName")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsValidated")
                        .HasColumnType("bit");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("PasswordSalt")
                        .HasColumnType("int");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<int>("RoleId")
                        .HasColumnType("int");

                    b.Property<string>("ShortName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("TotalDeficiencyCount")
                        .HasColumnType("int");

                    b.Property<int>("TotalFlaggedCount")
                        .HasColumnType("int");

                    b.Property<decimal>("TotalSecondsWorked")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("TotalWorkCount")
                        .HasColumnType("int");

                    b.Property<Guid?>("ValidationCode")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("WorkTotalsUpdatedOn")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasAlternateKey("Email");

                    b.HasIndex("RoleId");

                    b.ToTable("Users");

                    b.HasData(
                        new
                        {
                            Id = -1,
                            Email = "<EMAIL>",
                            FullName = "Test Admin",
                            IsDeleted = false,
                            IsValidated = true,
                            PasswordHash = "K4WsBfP1SP6KBrh72QQLZOGbEUBS7f3ZQAyuJI4wTHQ=",
                            PasswordSalt = 1,
                            PhoneNumber = "1",
                            RoleId = 1,
                            ShortName = "admin",
                            StartDate = new DateTime(2022, 3, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TotalDeficiencyCount = 0,
                            TotalFlaggedCount = 0,
                            TotalSecondsWorked = 0m,
                            TotalWorkCount = 0,
                            WorkTotalsUpdatedOn = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = -2,
                            Email = "<EMAIL>",
                            FullName = "Test Manager",
                            IsDeleted = false,
                            IsValidated = true,
                            PasswordHash = "+fkt6Hv5tz2aGpg3vCf3WHTG5qi9udrC+aJxq4TM150=",
                            PasswordSalt = 1,
                            PhoneNumber = "1",
                            RoleId = 2,
                            ShortName = "manager",
                            StartDate = new DateTime(2022, 3, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TotalDeficiencyCount = 0,
                            TotalFlaggedCount = 0,
                            TotalSecondsWorked = 0m,
                            TotalWorkCount = 0,
                            WorkTotalsUpdatedOn = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = -3,
                            Email = "<EMAIL>",
                            FullName = "Test Staff",
                            IsDeleted = false,
                            IsValidated = true,
                            PasswordHash = "VNwzJWdgdd2Jq0fT7jPoFS1Rl27vqTS/zEdOfCBf8bQ=",
                            PasswordSalt = 1,
                            PhoneNumber = "1",
                            RoleId = 3,
                            ShortName = "staff",
                            StartDate = new DateTime(2022, 3, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TotalDeficiencyCount = 0,
                            TotalFlaggedCount = 0,
                            TotalSecondsWorked = 0m,
                            TotalWorkCount = 0,
                            WorkTotalsUpdatedOn = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = -4,
                            Email = "<EMAIL>",
                            FullName = "System Reviewer",
                            IsDeleted = false,
                            IsValidated = true,
                            PasswordHash = "yxi6Inyo21vuh6Py4FyzCNFfs4f3CaNL0j3T0T0bcog=",
                            PasswordSalt = 1,
                            PhoneNumber = "1",
                            RoleId = 3,
                            ShortName = "system",
                            StartDate = new DateTime(2022, 3, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TotalDeficiencyCount = 0,
                            TotalFlaggedCount = 0,
                            TotalSecondsWorked = 0m,
                            TotalWorkCount = 0,
                            WorkTotalsUpdatedOn = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        });
                });

            modelBuilder.Entity("Model.Boundaries.Boundary", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<int>("UsStateId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UsStateId");

                    b.ToTable("Boundaries");
                });

            modelBuilder.Entity("Model.Boundaries.BoundaryOverlap", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("InnerBoundaryId")
                        .HasColumnType("int");

                    b.Property<int>("OuterBoundaryId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("InnerBoundaryId");

                    b.HasIndex("OuterBoundaryId", "InnerBoundaryId")
                        .IsUnique();

                    b.ToTable("BoundaryOverlaps");
                });

            modelBuilder.Entity("Model.Boundaries.BoundaryPoint", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("BoundaryId")
                        .HasColumnType("int");

                    b.Property<int>("Index")
                        .HasColumnType("int");

                    b.Property<decimal>("Latitude")
                        .HasColumnType("decimal(18,15)");

                    b.Property<decimal>("Longitude")
                        .HasColumnType("decimal(18,15)");

                    b.HasKey("Id");

                    b.HasIndex("BoundaryId");

                    b.ToTable("BoundaryPoints");
                });

            modelBuilder.Entity("Model.DataTransformation.DataTransformationStepResult", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("FunctionStepName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("InvocationId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int>("MatterId")
                        .HasColumnType("int");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<string>("Output")
                        .IsRequired()
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<DateTime>("OutputDateTime")
                        .HasColumnType("datetime2");

                    b.Property<long>("ResultStatusCode")
                        .HasColumnType("bigint");

                    b.Property<int>("SignatureSheetUploadId")
                        .HasColumnType("int");

                    b.Property<int>("TemplateId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("MatterId");

                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("MatterId"), new[] { "FunctionStepName", "InvocationId", "Message", "Output", "OutputDateTime", "ResultStatusCode", "SignatureSheetUploadId", "TemplateId" });

                    b.ToTable("DataTransformationStepResult");
                });

            modelBuilder.Entity("Model.DataTransformation.DataTransformationStepStart", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("FunctionStepName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Input")
                        .IsRequired()
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<string>("InvocationId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<int>("MatterId")
                        .HasColumnType("int");

                    b.Property<int>("SignatureSheetUploadId")
                        .HasColumnType("int");

                    b.Property<DateTime>("StartDateTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("TemplateId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("SignatureSheetUploadId");

                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("SignatureSheetUploadId"), new[] { "FunctionStepName", "Input", "InvocationId", "MatterId", "StartDateTime", "TemplateId" });

                    b.ToTable("DataTransformationStepStart");
                });

            modelBuilder.Entity("Model.Deficiencies.Deficiency", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool?>("IsDeficient")
                        .HasColumnType("bit");

                    b.Property<int>("MatterId")
                        .HasColumnType("int");

                    b.Property<bool>("NeedsReview")
                        .HasColumnType("bit");

                    b.Property<string>("Note")
                        .HasMaxLength(4096)
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("OtherRecordId")
                        .HasColumnType("int");

                    b.Property<int?>("OtherRecordIdType")
                        .HasColumnType("int");

                    b.Property<int>("RecordId")
                        .HasColumnType("int");

                    b.Property<int>("RecordIdType")
                        .HasColumnType("int");

                    b.Property<string>("ReviewedBy")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("ReviewedOn")
                        .HasColumnType("datetime2");

                    b.Property<int>("RuleId")
                        .HasColumnType("int");

                    b.Property<int?>("SignatureSheetId")
                        .HasColumnType("int");

                    b.Property<int?>("UserId")
                        .HasColumnType("int");

                    b.Property<int?>("WorkId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("MatterId");

                    b.HasIndex("RuleId");

                    b.HasIndex("SignatureSheetId");

                    b.HasIndex("UserId");

                    b.ToTable("Deficiencies");
                });

            modelBuilder.Entity("Model.Deficiencies.DeficiencyReview", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AssignedTo")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("AssignmentDate")
                        .HasColumnType("datetime2");

                    b.Property<bool?>("IsDeficient")
                        .HasColumnType("bit");

                    b.Property<int>("MatterId")
                        .HasColumnType("int");

                    b.Property<string>("Note")
                        .HasMaxLength(4096)
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("RecordId")
                        .HasColumnType("int");

                    b.Property<int>("RecordIdType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ReviewedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("RuleId")
                        .HasColumnType("int");

                    b.Property<int?>("SignatureSheetId")
                        .HasColumnType("int");

                    b.Property<int?>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("MatterId");

                    b.HasIndex("RuleId");

                    b.HasIndex("SignatureSheetId");

                    b.HasIndex("UserId");

                    b.ToTable("DeficiencyReviews");
                });

            modelBuilder.Entity("Model.ExternalDataSources.Circulator", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("City")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("PostalCode")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<string>("RegistrationId")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<int>("UsStateId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UsStateId");

                    b.ToTable("Circulators");
                });

            modelBuilder.Entity("Model.ExternalDataSources.DataProcessingLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("DataProcessingType")
                        .HasColumnType("int");

                    b.Property<DateTime>("DateTime")
                        .HasColumnType("datetime2");

                    b.Property<int?>("ExternalDataSourcePartId")
                        .HasColumnType("int");

                    b.Property<string>("Operation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("SignatureSheetUploadId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ExternalDataSourcePartId");

                    b.HasIndex("SignatureSheetUploadId");

                    b.ToTable("DataProcessingLogs");
                });

            modelBuilder.Entity("Model.ExternalDataSources.ExternalDataSource", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("County")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int?>("MatterId")
                        .HasColumnType("int");

                    b.Property<int?>("RuleId")
                        .HasColumnType("int");

                    b.Property<string>("RuleName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int?>("TotalParts")
                        .HasColumnType("int");

                    b.Property<int?>("UploadType")
                        .HasColumnType("int");

                    b.Property<string>("UploadedBy")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("UploadedOn")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("MatterId");

                    b.ToTable("ExternalDataSources");
                });

            modelBuilder.Entity("Model.ExternalDataSources.ExternalDataSourcePart", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ExternalDataSourceId")
                        .HasColumnType("int");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("OriginalUploadedFileName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int?>("PartNumber")
                        .HasColumnType("int");

                    b.Property<string>("UploadedBy")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("UploadedOn")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("ExternalDataSourceId");

                    b.ToTable("ExternalDataSourceParts");
                });

            modelBuilder.Entity("Model.ExternalDataSources.InvalidCirculator", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CirculatorId")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<string>("CirculatorName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("ExternalDataSourceId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ExternalDataSourceId");

                    b.ToTable("InvalidCirculators");
                });

            modelBuilder.Entity("Model.ExternalDataSources.InvalidVoter", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ApartmentNumber")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<int?>("BirthYear")
                        .HasColumnType("int");

                    b.Property<string>("City")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("DateOfBirth")
                        .HasColumnType("datetime2");

                    b.Property<string>("Direction")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<int>("ExternalDataSourceId")
                        .HasColumnType("int");

                    b.Property<int>("ExternalDataSourcePartId")
                        .HasColumnType("int");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsPoBoxAddress")
                        .HasColumnType("bit");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("MiddleName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(1)");

                    b.Property<string>("StreetAddress")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("StreetName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("StreetNumber")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<string>("StreetType")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("UsStateId")
                        .HasColumnType("int");

                    b.Property<int>("VoterId")
                        .HasColumnType("int");

                    b.Property<string>("ZipCode")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.HasKey("Id");

                    b.HasIndex("ExternalDataSourceId");

                    b.HasIndex("ExternalDataSourcePartId");

                    b.HasIndex("UsStateId");

                    b.ToTable("InvalidVoters");
                });

            modelBuilder.Entity("Model.ExternalDataSources.RegisteredCirculator", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CandidateOrBallotMeasure")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("CirculatorId")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<string>("CirculatorName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("CompanyWorkingFor")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime>("DateRegistered")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateUnregistered")
                        .HasColumnType("datetime2");

                    b.Property<int>("ElectionCycle")
                        .HasColumnType("int");

                    b.Property<int>("ExternalDataSourceId")
                        .HasColumnType("int");

                    b.Property<string>("Jurisdiction")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("OutOfState")
                        .HasColumnType("bit");

                    b.Property<bool>("Paid")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("ExternalDataSourceId");

                    b.ToTable("RegisteredCirculators");
                });

            modelBuilder.Entity("Model.ExternalDataSources.RegisteredVoter", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ApartmentNumber")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<int?>("BirthYear")
                        .HasColumnType("int");

                    b.Property<string>("City")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CongressionalDistrict")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("County")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime?>("DateOfBirth")
                        .HasColumnType("datetime2");

                    b.Property<string>("Direction")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<int>("ExternalDataSourceId")
                        .HasColumnType("int");

                    b.Property<int>("ExternalDataSourcePartId")
                        .HasColumnType("int");

                    b.Property<bool>("FedIdOnly")
                        .HasColumnType("bit");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsPoBoxAddress")
                        .HasColumnType("bit");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("LegislativeDistrict")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("MailingAddress1")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("MailingAddress2")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("MailingAddress3")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("MailingAddress4")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("MailingAddress5")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("MiddleName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Party")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<bool>("PermanentEarlyVoter")
                        .HasColumnType("bit");

                    b.Property<int>("Precinct")
                        .HasColumnType("int");

                    b.Property<DateTime>("RegistrationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(1)");

                    b.Property<string>("StreetAddress")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("StreetName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("StreetNumber")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<string>("StreetType")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Telephone")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<int>("UsStateId")
                        .HasColumnType("int");

                    b.Property<int>("VoterId")
                        .HasColumnType("int");

                    b.Property<string>("ZipCode")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.HasKey("Id");

                    b.HasIndex("ExternalDataSourceId");

                    b.HasIndex("ExternalDataSourcePartId");

                    b.HasIndex("UsStateId");

                    b.HasIndex("FirstName", "LastName", "StreetNumber", "Direction", "StreetName", "StreetType", "UsStateId");

                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("FirstName", "LastName", "StreetNumber", "Direction", "StreetName", "StreetType", "UsStateId"), new[] { "Id", "ApartmentNumber", "BirthYear", "City", "CongressionalDistrict", "County", "DateOfBirth", "ExternalDataSourceId", "ExternalDataSourcePartId", "FedIdOnly", "IsPoBoxAddress", "LegislativeDistrict", "MailingAddress1", "MailingAddress2", "MailingAddress3", "MailingAddress4", "MailingAddress5", "MiddleName", "Party", "PermanentEarlyVoter", "Precinct", "RegistrationDate", "Status", "StreetAddress", "Telephone", "VoterId", "ZipCode" });

                    b.ToTable("RegisteredVoters");
                });

            modelBuilder.Entity("Model.ExternalDataSources.Signatory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("City")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("DateSigned")
                        .HasColumnType("datetime2");

                    b.Property<string>("Direction")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("GeocodeErrors")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<decimal?>("Latitude")
                        .HasColumnType("decimal(18,15)");

                    b.Property<decimal?>("Longitude")
                        .HasColumnType("decimal(18,15)");

                    b.Property<string>("PostalCode")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<string>("StreetName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("StreetNumber")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<string>("StreetType")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("UsStateId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UsStateId");

                    b.ToTable("Signatories");
                });

            modelBuilder.Entity("Model.Matters.ExternalDataSourceMatter", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ExternalDataSourceId")
                        .HasColumnType("int");

                    b.Property<int>("MatterId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ExternalDataSourceId");

                    b.HasIndex("MatterId");

                    b.ToTable("ExternalDataSourceMatter");
                });

            modelBuilder.Entity("Model.Matters.Matter", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("AreTasksCreated")
                        .HasColumnType("bit");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DueDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsGoalDeficencies")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSheetReviewAdded")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<int>("NumberSignaturesRequired")
                        .HasColumnType("int");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("Matters");

                    b.HasData(
                        new
                        {
                            Id = -1,
                            AreTasksCreated = false,
                            IsActive = true,
                            IsGoalDeficencies = true,
                            IsSheetReviewAdded = true,
                            Name = "Test Matter Data",
                            NumberSignaturesRequired = 10,
                            Type = 0
                        });
                });

            modelBuilder.Entity("Model.Matters.MatterVariable", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("MatterId")
                        .HasColumnType("int");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("MatterId");

                    b.ToTable("MatterVariables");
                });

            modelBuilder.Entity("Model.ReferenceData.UsState", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Abbreviation")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.ToTable("UsStates");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Abbreviation = "AL",
                            Name = "Alabama"
                        },
                        new
                        {
                            Id = 2,
                            Abbreviation = "AK",
                            Name = "Alaska"
                        },
                        new
                        {
                            Id = 3,
                            Abbreviation = "AZ",
                            Name = "Arizona"
                        },
                        new
                        {
                            Id = 4,
                            Abbreviation = "AR",
                            Name = "Arkansas"
                        },
                        new
                        {
                            Id = 5,
                            Abbreviation = "CA",
                            Name = "California"
                        },
                        new
                        {
                            Id = 6,
                            Abbreviation = "CO",
                            Name = "Colorado"
                        },
                        new
                        {
                            Id = 7,
                            Abbreviation = "CT",
                            Name = "Connecticut"
                        },
                        new
                        {
                            Id = 8,
                            Abbreviation = "DE",
                            Name = "Delaware"
                        },
                        new
                        {
                            Id = 9,
                            Abbreviation = "DC",
                            Name = "District Of Columbia"
                        },
                        new
                        {
                            Id = 10,
                            Abbreviation = "FL",
                            Name = "Florida"
                        },
                        new
                        {
                            Id = 11,
                            Abbreviation = "GA",
                            Name = "Georgia"
                        },
                        new
                        {
                            Id = 12,
                            Abbreviation = "HI",
                            Name = "Hawaii"
                        },
                        new
                        {
                            Id = 13,
                            Abbreviation = "ID",
                            Name = "Idaho"
                        },
                        new
                        {
                            Id = 14,
                            Abbreviation = "IL",
                            Name = "Illinois"
                        },
                        new
                        {
                            Id = 15,
                            Abbreviation = "IN",
                            Name = "Indiana"
                        },
                        new
                        {
                            Id = 16,
                            Abbreviation = "IA",
                            Name = "Iowa"
                        },
                        new
                        {
                            Id = 17,
                            Abbreviation = "KS",
                            Name = "Kansas"
                        },
                        new
                        {
                            Id = 18,
                            Abbreviation = "KY",
                            Name = "Kentucky"
                        },
                        new
                        {
                            Id = 19,
                            Abbreviation = "LA",
                            Name = "Louisiana"
                        },
                        new
                        {
                            Id = 20,
                            Abbreviation = "ME",
                            Name = "Maine"
                        },
                        new
                        {
                            Id = 21,
                            Abbreviation = "MD",
                            Name = "Maryland"
                        },
                        new
                        {
                            Id = 22,
                            Abbreviation = "MA",
                            Name = "Massachusetts"
                        },
                        new
                        {
                            Id = 23,
                            Abbreviation = "MI",
                            Name = "Michigan"
                        },
                        new
                        {
                            Id = 24,
                            Abbreviation = "MN",
                            Name = "Minnesota"
                        },
                        new
                        {
                            Id = 25,
                            Abbreviation = "MS",
                            Name = "Mississippi"
                        },
                        new
                        {
                            Id = 26,
                            Abbreviation = "MO",
                            Name = "Missouri"
                        },
                        new
                        {
                            Id = 27,
                            Abbreviation = "MT",
                            Name = "Montana"
                        },
                        new
                        {
                            Id = 28,
                            Abbreviation = "NE",
                            Name = "Nebraska"
                        },
                        new
                        {
                            Id = 29,
                            Abbreviation = "NV",
                            Name = "Nevada"
                        },
                        new
                        {
                            Id = 30,
                            Abbreviation = "NH",
                            Name = "New Hampshire"
                        },
                        new
                        {
                            Id = 31,
                            Abbreviation = "NJ",
                            Name = "New Jersey"
                        },
                        new
                        {
                            Id = 32,
                            Abbreviation = "NM",
                            Name = "New Mexico"
                        },
                        new
                        {
                            Id = 33,
                            Abbreviation = "NY",
                            Name = "New York"
                        },
                        new
                        {
                            Id = 34,
                            Abbreviation = "NC",
                            Name = "North Carolina"
                        },
                        new
                        {
                            Id = 35,
                            Abbreviation = "ND",
                            Name = "North Dakota"
                        },
                        new
                        {
                            Id = 36,
                            Abbreviation = "OH",
                            Name = "Ohio"
                        },
                        new
                        {
                            Id = 37,
                            Abbreviation = "OK",
                            Name = "Oklahoma"
                        },
                        new
                        {
                            Id = 38,
                            Abbreviation = "OR",
                            Name = "Oregon"
                        },
                        new
                        {
                            Id = 39,
                            Abbreviation = "PA",
                            Name = "Pennsylvania"
                        },
                        new
                        {
                            Id = 40,
                            Abbreviation = "RI",
                            Name = "Rhode Island"
                        },
                        new
                        {
                            Id = 41,
                            Abbreviation = "SC",
                            Name = "South Carolina"
                        },
                        new
                        {
                            Id = 42,
                            Abbreviation = "SD",
                            Name = "South Dakota"
                        },
                        new
                        {
                            Id = 43,
                            Abbreviation = "TN",
                            Name = "Tennessee"
                        },
                        new
                        {
                            Id = 44,
                            Abbreviation = "TX",
                            Name = "Texas"
                        },
                        new
                        {
                            Id = 45,
                            Abbreviation = "UT",
                            Name = "Utah"
                        },
                        new
                        {
                            Id = 46,
                            Abbreviation = "VT",
                            Name = "Vermont"
                        },
                        new
                        {
                            Id = 47,
                            Abbreviation = "VA",
                            Name = "Virginia"
                        },
                        new
                        {
                            Id = 48,
                            Abbreviation = "WA",
                            Name = "Washington"
                        },
                        new
                        {
                            Id = 49,
                            Abbreviation = "WV",
                            Name = "West Virginia"
                        },
                        new
                        {
                            Id = 50,
                            Abbreviation = "WI",
                            Name = "Wisconsin"
                        },
                        new
                        {
                            Id = 51,
                            Abbreviation = "WY",
                            Name = "Wyoming"
                        });
                });

            modelBuilder.Entity("Model.Rules.BackgroundOperation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("EndedOn")
                        .HasColumnType("datetime2");

                    b.Property<int>("ExecutionStatus")
                        .HasColumnType("int");

                    b.Property<int?>("MatterId")
                        .HasColumnType("int");

                    b.Property<string>("Message")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<int>("OperationType")
                        .HasColumnType("int");

                    b.Property<string>("StartedBy")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("StartedOn")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("MatterId");

                    b.ToTable("BackgroundOperations");
                });

            modelBuilder.Entity("Model.Rules.Rule", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<string>("LeftHandExpression")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("MatterId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<bool>("NeedsReview")
                        .HasColumnType("bit");

                    b.Property<string>("OperationName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("RightHandExpression")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("RuleContextType")
                        .HasColumnType("int");

                    b.Property<int?>("TaskId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("MatterId");

                    b.HasIndex("TaskId");

                    b.ToTable("Rules");
                });

            modelBuilder.Entity("Model.SignatureSheets.InvalidSheet", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AssignedTo")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("AssignmentDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Filename")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("MatterId")
                        .HasColumnType("int");

                    b.Property<int>("SheetNumber")
                        .HasColumnType("int");

                    b.Property<int>("SignatureSheetUploadId")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<int>("TemplateId")
                        .HasColumnType("int");

                    b.Property<int?>("UserId")
                        .HasColumnType("int");

                    b.Property<int?>("ValidSignatureSheetId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("MatterId");

                    b.HasIndex("SignatureSheetUploadId");

                    b.HasIndex("TemplateId");

                    b.ToTable("InvalidSheets");
                });

            modelBuilder.Entity("Model.SignatureSheets.SignatureSheet", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("AreFieldsReviewed")
                        .HasColumnType("bit");

                    b.Property<int?>("CirculatorId")
                        .HasColumnType("int");

                    b.Property<string>("DeficiencyImageFilePath")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime?>("FieldsReviewedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsTableReviewed")
                        .HasColumnType("bit");

                    b.Property<int>("MatterId")
                        .HasColumnType("int");

                    b.Property<int>("SheetIndex")
                        .HasColumnType("int");

                    b.Property<int>("SheetNumber")
                        .HasColumnType("int");

                    b.Property<int>("SignatureSheetUploadId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("TableReviewedOn")
                        .HasColumnType("datetime2");

                    b.Property<int>("TemplateId")
                        .HasColumnType("int");

                    b.Property<int>("Validity")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CirculatorId");

                    b.HasIndex("MatterId");

                    b.HasIndex("SignatureSheetUploadId");

                    b.HasIndex("TemplateId");

                    b.ToTable("SignatureSheets");
                });

            modelBuilder.Entity("Model.SignatureSheets.SignatureSheetCell", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Bottom")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<int>("ColumnIndex")
                        .HasColumnType("int");

                    b.Property<bool>("IsMissing")
                        .HasColumnType("bit");

                    b.Property<bool>("IsReviewed")
                        .HasColumnType("bit");

                    b.Property<decimal>("Left")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<string>("ReviewedBy")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("ReviewedOn")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("Right")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<int>("RowIndex")
                        .HasColumnType("int");

                    b.Property<int>("SignatureSheetRowId")
                        .HasColumnType("int");

                    b.Property<int>("TemplateSignatureColumnId")
                        .HasColumnType("int");

                    b.Property<decimal>("Top")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<int>("Validity")
                        .HasColumnType("int");

                    b.Property<string>("Value")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("SignatureSheetRowId");

                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("SignatureSheetRowId"), new[] { "Bottom", "ColumnIndex", "IsMissing", "IsReviewed", "Left", "ReviewedBy", "ReviewedOn", "Right", "RowIndex", "TemplateSignatureColumnId", "Top", "Validity", "Value" });

                    b.HasIndex("TemplateSignatureColumnId");

                    b.ToTable("SignatureSheetCells");
                });

            modelBuilder.Entity("Model.SignatureSheets.SignatureSheetColumn", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Bottom")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<int>("ColumnIndex")
                        .HasColumnType("int");

                    b.Property<bool>("IsMissing")
                        .HasColumnType("bit");

                    b.Property<decimal>("Left")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<decimal>("Right")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<int>("SignatureSheetTableId")
                        .HasColumnType("int");

                    b.Property<decimal>("Top")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.HasKey("Id");

                    b.HasIndex("SignatureSheetTableId");

                    b.ToTable("SignatureSheetColumns");
                });

            modelBuilder.Entity("Model.SignatureSheets.SignatureSheetField", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Bottom")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<bool>("IsMissing")
                        .HasColumnType("bit");

                    b.Property<bool>("IsReviewed")
                        .HasColumnType("bit");

                    b.Property<decimal>("Left")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<int>("Page")
                        .HasColumnType("int");

                    b.Property<string>("ReviewedBy")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("ReviewedOn")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("Right")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<int>("SignatureSheetId")
                        .HasColumnType("int");

                    b.Property<decimal>("Top")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<int>("TranscribableFieldId")
                        .HasColumnType("int");

                    b.Property<int>("Validity")
                        .HasColumnType("int");

                    b.Property<string>("Value")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.HasKey("Id");

                    b.HasIndex("SignatureSheetId");

                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("SignatureSheetId"), new[] { "Bottom", "IsMissing", "IsReviewed", "Left", "Page", "ReviewedBy", "ReviewedOn", "Right", "Top", "TranscribableFieldId", "Validity", "Value" });

                    b.HasIndex("TranscribableFieldId");

                    b.ToTable("SignatureSheetFields");
                });

            modelBuilder.Entity("Model.SignatureSheets.SignatureSheetFormLine", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Bottom")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<decimal>("Left")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<decimal>("Right")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<int?>("SignatureSheetPageId")
                        .HasColumnType("int");

                    b.Property<int?>("TemplateFormLineId")
                        .HasColumnType("int");

                    b.Property<decimal>("Top")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<string>("Value")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.HasKey("Id");

                    b.HasIndex("SignatureSheetPageId");

                    b.HasIndex("TemplateFormLineId");

                    b.ToTable("SignatureSheetFormLines");
                });

            modelBuilder.Entity("Model.SignatureSheets.SignatureSheetPage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<float>("Height")
                        .HasColumnType("real");

                    b.Property<int>("PageNumber")
                        .HasColumnType("int");

                    b.Property<int?>("SignatureSheetId")
                        .HasColumnType("int");

                    b.Property<float>("Width")
                        .HasColumnType("real");

                    b.HasKey("Id");

                    b.HasIndex("SignatureSheetId");

                    b.ToTable("SignatureSheetPages");
                });

            modelBuilder.Entity("Model.SignatureSheets.SignatureSheetProcessing", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("MatterId")
                        .HasColumnType("int");

                    b.Property<int>("SheetNumber")
                        .HasColumnType("int");

                    b.Property<int?>("SignatureSheetUploadId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("MatterId", "SheetNumber");

                    b.ToTable("SignatureSheetProcessings");
                });

            modelBuilder.Entity("Model.SignatureSheets.SignatureSheetRow", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Bottom")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<bool>("IsMissing")
                        .HasColumnType("bit");

                    b.Property<bool>("IsReviewed")
                        .HasColumnType("bit");

                    b.Property<decimal>("Left")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<int>("RegisteredVoterFlags")
                        .HasColumnType("int");

                    b.Property<int?>("RegisteredVoterId")
                        .HasColumnType("int");

                    b.Property<string>("ReviewedBy")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("ReviewedOn")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("Right")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<int>("RowIndex")
                        .HasColumnType("int");

                    b.Property<int>("RowNumber")
                        .HasColumnType("int");

                    b.Property<int?>("SignatoryId")
                        .HasColumnType("int");

                    b.Property<int>("SignatureSheetId")
                        .HasColumnType("int");

                    b.Property<int>("SignatureSheetTableId")
                        .HasColumnType("int");

                    b.Property<int>("TemplateSignatureTableId")
                        .HasColumnType("int");

                    b.Property<decimal>("Top")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<int>("Validity")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("RegisteredVoterId");

                    b.HasIndex("SignatoryId");

                    b.HasIndex("SignatureSheetId");

                    b.HasIndex("SignatureSheetTableId");

                    b.HasIndex("TemplateSignatureTableId");

                    b.HasIndex("IsReviewed", "Validity");

                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("IsReviewed", "Validity"), new[] { "TemplateSignatureTableId", "SignatoryId", "SignatureSheetId" });

                    b.ToTable("SignatureSheetRows");
                });

            modelBuilder.Entity("Model.SignatureSheets.SignatureSheetTable", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Bottom")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<decimal>("Left")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<decimal>("Right")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<int>("SignatureSheetId")
                        .HasColumnType("int");

                    b.Property<decimal>("Top")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.HasKey("Id");

                    b.HasIndex("SignatureSheetId");

                    b.ToTable("SignatureSheetTables");
                });

            modelBuilder.Entity("Model.SignatureSheets.SignatureSheetUpload", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("DownloadUrl")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("FileName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("MatterId")
                        .HasColumnType("int");

                    b.Property<int>("TemplateId")
                        .HasColumnType("int");

                    b.Property<string>("UploadedBy")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("UploadedOn")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("MatterId");

                    b.HasIndex("TemplateId");

                    b.ToTable("SignatureSheetUploads");
                });

            modelBuilder.Entity("Model.Templates.Template", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("FileName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<int?>("PageSize")
                        .HasColumnType("int");

                    b.Property<string>("UploadedBy")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("UploadedOn")
                        .HasColumnType("datetime2");

                    b.Property<int>("UsStateId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UsStateId");

                    b.ToTable("Templates");

                    b.HasData(
                        new
                        {
                            Id = -1,
                            FileName = "testFileName.pdf",
                            IsActive = true,
                            Name = "Test Template",
                            UploadedBy = "<EMAIL>",
                            UploadedOn = new DateTime(2022, 3, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            UsStateId = 3
                        });
                });

            modelBuilder.Entity("Model.Templates.TemplateFormLine", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Bottom")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<bool>("IsShiftable")
                        .HasColumnType("bit");

                    b.Property<decimal>("Left")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<int?>("LeftFieldId")
                        .HasColumnType("int");

                    b.Property<int?>("LeftLineId")
                        .HasColumnType("int");

                    b.Property<decimal>("Right")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<int?>("RightFieldId")
                        .HasColumnType("int");

                    b.Property<int?>("RightLineId")
                        .HasColumnType("int");

                    b.Property<int>("TemplatePageId")
                        .HasColumnType("int");

                    b.Property<decimal>("Top")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<int?>("TrainingModelId")
                        .HasColumnType("int");

                    b.Property<string>("Value")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.HasKey("Id");

                    b.HasIndex("TemplatePageId");

                    b.HasIndex("TrainingModelId");

                    b.ToTable("TemplateFormLines");
                });

            modelBuilder.Entity("Model.Templates.TemplateIgnoredWord", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("TemplateId")
                        .HasColumnType("int");

                    b.Property<string>("Word")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("TemplateId");

                    b.ToTable("TemplateIgnoredWords");
                });

            modelBuilder.Entity("Model.Templates.TemplatePage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<float>("Height")
                        .HasColumnType("real");

                    b.Property<int>("PageNumber")
                        .HasColumnType("int");

                    b.Property<int?>("TemplateId")
                        .HasColumnType("int");

                    b.Property<float>("Width")
                        .HasColumnType("real");

                    b.HasKey("Id");

                    b.HasIndex("TemplateId");

                    b.ToTable("TemplatePages");

                    b.HasData(
                        new
                        {
                            Id = -1,
                            Height = 8.5f,
                            PageNumber = 1,
                            TemplateId = -1,
                            Width = 11f
                        });
                });

            modelBuilder.Entity("Model.Templates.TemplateSignatureCell", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Bottom")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<int>("ColumnIndex")
                        .HasColumnType("int");

                    b.Property<decimal>("Left")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<decimal>("Right")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<int>("RowIndex")
                        .HasColumnType("int");

                    b.Property<int>("TemplateSignatureColumnId")
                        .HasColumnType("int");

                    b.Property<int>("TemplateSignatureRowId")
                        .HasColumnType("int");

                    b.Property<int>("TemplateSignatureTableId")
                        .HasColumnType("int");

                    b.Property<decimal>("Top")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<string>("Value")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.ToTable("TemplateSignatureCells");
                });

            modelBuilder.Entity("Model.Templates.TemplateSignatureColumn", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Bottom")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<bool?>("CanBeInvalid")
                        .HasColumnType("bit");

                    b.Property<int>("ColumnIndex")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<bool?>("IsAddress")
                        .HasColumnType("bit");

                    b.Property<bool?>("IsDate")
                        .HasColumnType("bit");

                    b.Property<bool?>("IsHandwritten")
                        .HasColumnType("bit");

                    b.Property<bool?>("IsName")
                        .HasColumnType("bit");

                    b.Property<bool?>("IsSignature")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSkipped")
                        .HasColumnType("bit");

                    b.Property<bool?>("IsVoterId")
                        .HasColumnType("bit");

                    b.Property<decimal>("Left")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<decimal>("Right")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<int>("TemplateSignatureTableId")
                        .HasColumnType("int");

                    b.Property<decimal>("Top")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.HasKey("Id");

                    b.HasIndex("TemplateSignatureTableId");

                    b.ToTable("TemplateSignatureColumns");
                });

            modelBuilder.Entity("Model.Templates.TemplateSignatureRow", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Bottom")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<decimal>("Left")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<decimal>("Right")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<int>("RowIndex")
                        .HasColumnType("int");

                    b.Property<int>("TemplateSignatureTableId")
                        .HasColumnType("int");

                    b.Property<decimal>("Top")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.HasKey("Id");

                    b.HasIndex("TemplateSignatureTableId");

                    b.ToTable("TemplateSignatureRows");
                });

            modelBuilder.Entity("Model.Templates.TemplateSignatureTable", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Bottom")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<decimal>("Left")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("NumberOfCols")
                        .HasColumnType("int");

                    b.Property<int>("NumberOfRows")
                        .HasColumnType("int");

                    b.Property<decimal>("Right")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<int?>("TemplateId")
                        .HasColumnType("int");

                    b.Property<decimal>("Top")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<int?>("TrainingModelId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("TemplateId");

                    b.HasIndex("TrainingModelId");

                    b.ToTable("TemplateSignatureTables");
                });

            modelBuilder.Entity("Model.Templates.TrainingModel", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ApiVersion")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("CreatedDateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.ToTable("TrainingModels");
                });

            modelBuilder.Entity("Model.Templates.TranscribableField", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Bottom")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<bool>("CanBeInvalid")
                        .HasColumnType("bit");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<int>("FieldNumber")
                        .HasColumnType("int");

                    b.Property<string>("GroupName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("InputType")
                        .HasColumnType("int");

                    b.Property<bool>("IsDate")
                        .HasColumnType("bit");

                    b.Property<bool>("IsHandwritten")
                        .HasColumnType("bit");

                    b.Property<bool>("IsPrefixed")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSignature")
                        .HasColumnType("bit");

                    b.Property<decimal>("Left")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<int?>("LeftLineId")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("PageNumber")
                        .HasColumnType("int");

                    b.Property<string>("Prefix")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<decimal>("Right")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<int?>("RightLineId")
                        .HasColumnType("int");

                    b.Property<int>("TemplatePageId")
                        .HasColumnType("int");

                    b.Property<decimal>("Top")
                        .HasPrecision(12, 6)
                        .HasColumnType("decimal(12,6)");

                    b.Property<int?>("TrainingModelId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("TemplatePageId");

                    b.HasIndex("TrainingModelId");

                    b.ToTable("TranscribableFields");

                    b.HasData(
                        new
                        {
                            Id = -1,
                            Bottom = 1.0m,
                            CanBeInvalid = true,
                            FieldNumber = 0,
                            InputType = 0,
                            IsDate = false,
                            IsHandwritten = true,
                            IsPrefixed = false,
                            IsSignature = false,
                            Left = 0.5m,
                            Name = "TestField",
                            PageNumber = 1,
                            Right = 1.0m,
                            TemplatePageId = -1,
                            Top = 0.5m
                        });
                });

            modelBuilder.Entity("Model.Workflow.GroupTaskAssignment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("StaffGroupId")
                        .HasColumnType("int");

                    b.Property<int>("TaskId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("StaffGroupId");

                    b.HasIndex("TaskId");

                    b.ToTable("GroupTaskAssignments");
                });

            modelBuilder.Entity("Model.Workflow.StaffGroup", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.ToTable("StaffGroups");

                    b.HasData(
                        new
                        {
                            Id = -1,
                            Name = "Test Staff Group"
                        });
                });

            modelBuilder.Entity("Model.Workflow.Task", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("CanBeInvalid")
                        .HasColumnType("bit");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<int?>("FirstColumnIndex")
                        .HasColumnType("int");

                    b.Property<int?>("LastColumnIndex")
                        .HasColumnType("int");

                    b.Property<int>("MatterId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Priority")
                        .HasColumnType("int");

                    b.Property<int?>("ShowFirstColumnIndex")
                        .HasColumnType("int");

                    b.Property<int?>("ShowLastColumnIndex")
                        .HasColumnType("int");

                    b.Property<bool?>("ShowSurroundingRows")
                        .HasColumnType("bit");

                    b.Property<bool?>("ShowWholeColumn")
                        .HasColumnType("bit");

                    b.Property<int>("TaskType")
                        .HasColumnType("int");

                    b.Property<int>("TemplateId")
                        .HasColumnType("int");

                    b.Property<int?>("TemplateSignatureTableId")
                        .HasColumnType("int");

                    b.Property<string>("TranscribableFieldIds")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("MatterId");

                    b.HasIndex("TemplateId");

                    b.HasIndex("TemplateSignatureTableId");

                    b.ToTable("Tasks");
                });

            modelBuilder.Entity("Model.Workflow.UserGroupAssignment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("MatterId")
                        .HasColumnType("int");

                    b.Property<int>("StaffGroupId")
                        .HasColumnType("int");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("MatterId");

                    b.HasIndex("StaffGroupId");

                    b.HasIndex("UserId");

                    b.ToTable("UserGroupAssignments");

                    b.HasData(
                        new
                        {
                            Id = -1,
                            MatterId = -1,
                            StaffGroupId = -1,
                            UserId = -3
                        });
                });

            modelBuilder.Entity("Model.Workflow.Work", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("AssignmentDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("FieldNumber")
                        .HasColumnType("int");

                    b.Property<DateTime>("LastStartDateTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("LastStopDateTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("MatterId")
                        .HasColumnType("int");

                    b.Property<int>("RowNumber")
                        .HasColumnType("int");

                    b.Property<double>("SecondsWorked")
                        .HasColumnType("float");

                    b.Property<int>("SheetNumber")
                        .HasColumnType("int");

                    b.Property<int>("SignatureSheetId")
                        .HasColumnType("int");

                    b.Property<int>("TaskId")
                        .HasColumnType("int");

                    b.Property<int?>("UserId")
                        .HasColumnType("int");

                    b.Property<int>("WorkStatus")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("MatterId");

                    b.HasIndex("SignatureSheetId");

                    b.HasIndex("TaskId");

                    b.HasIndex("UserId", "WorkStatus");

                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("UserId", "WorkStatus"), new[] { "SecondsWorked", "LastStartDateTime", "LastStopDateTime" });

                    b.ToTable("Works");
                });

            modelBuilder.Entity("Model.Workflow.WorkField", b =>
                {
                    b.Property<int>("WorkId")
                        .HasColumnType("int");

                    b.Property<int>("FieldId")
                        .HasColumnType("int");

                    b.Property<int>("FieldType")
                        .HasColumnType("int");

                    b.HasKey("WorkId", "FieldId");

                    b.ToTable("WorkFields");
                });

            modelBuilder.Entity("Model.Authorization.User", b =>
                {
                    b.HasOne("Model.Authorization.Role", "Role")
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");
                });

            modelBuilder.Entity("Model.Boundaries.Boundary", b =>
                {
                    b.HasOne("Model.ReferenceData.UsState", "UsState")
                        .WithMany()
                        .HasForeignKey("UsStateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("UsState");
                });

            modelBuilder.Entity("Model.Boundaries.BoundaryOverlap", b =>
                {
                    b.HasOne("Model.Boundaries.Boundary", "InnerBoundary")
                        .WithMany()
                        .HasForeignKey("InnerBoundaryId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Model.Boundaries.Boundary", "OuterBoundary")
                        .WithMany()
                        .HasForeignKey("OuterBoundaryId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("InnerBoundary");

                    b.Navigation("OuterBoundary");
                });

            modelBuilder.Entity("Model.Boundaries.BoundaryPoint", b =>
                {
                    b.HasOne("Model.Boundaries.Boundary", "Boundary")
                        .WithMany("BoundaryPoints")
                        .HasForeignKey("BoundaryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Boundary");
                });

            modelBuilder.Entity("Model.Deficiencies.Deficiency", b =>
                {
                    b.HasOne("Model.Matters.Matter", "Matter")
                        .WithMany()
                        .HasForeignKey("MatterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Model.Rules.Rule", "Rule")
                        .WithMany("Deficiencies")
                        .HasForeignKey("RuleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Model.SignatureSheets.SignatureSheet", "SignatureSheet")
                        .WithMany()
                        .HasForeignKey("SignatureSheetId");

                    b.HasOne("Model.Authorization.User", "User")
                        .WithMany("Deficiencies")
                        .HasForeignKey("UserId");

                    b.Navigation("Matter");

                    b.Navigation("Rule");

                    b.Navigation("SignatureSheet");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Model.Deficiencies.DeficiencyReview", b =>
                {
                    b.HasOne("Model.Matters.Matter", "Matter")
                        .WithMany()
                        .HasForeignKey("MatterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Model.Rules.Rule", "Rule")
                        .WithMany()
                        .HasForeignKey("RuleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Model.SignatureSheets.SignatureSheet", "SignatureSheet")
                        .WithMany()
                        .HasForeignKey("SignatureSheetId");

                    b.HasOne("Model.Authorization.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("Matter");

                    b.Navigation("Rule");

                    b.Navigation("SignatureSheet");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Model.ExternalDataSources.Circulator", b =>
                {
                    b.HasOne("Model.ReferenceData.UsState", "UsState")
                        .WithMany()
                        .HasForeignKey("UsStateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("UsState");
                });

            modelBuilder.Entity("Model.ExternalDataSources.DataProcessingLog", b =>
                {
                    b.HasOne("Model.ExternalDataSources.ExternalDataSourcePart", "ExternalDataSourcePart")
                        .WithMany("ExternalDataProcessingLogs")
                        .HasForeignKey("ExternalDataSourcePartId");

                    b.HasOne("Model.SignatureSheets.SignatureSheetUpload", "SignatureSheetUpload")
                        .WithMany("DataProcessingLogs")
                        .HasForeignKey("SignatureSheetUploadId");

                    b.Navigation("ExternalDataSourcePart");

                    b.Navigation("SignatureSheetUpload");
                });

            modelBuilder.Entity("Model.ExternalDataSources.ExternalDataSource", b =>
                {
                    b.HasOne("Model.Matters.Matter", "Matter")
                        .WithMany()
                        .HasForeignKey("MatterId");

                    b.Navigation("Matter");
                });

            modelBuilder.Entity("Model.ExternalDataSources.ExternalDataSourcePart", b =>
                {
                    b.HasOne("Model.ExternalDataSources.ExternalDataSource", "ExternalDataSource")
                        .WithMany("ExternalDataSourceParts")
                        .HasForeignKey("ExternalDataSourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ExternalDataSource");
                });

            modelBuilder.Entity("Model.ExternalDataSources.InvalidCirculator", b =>
                {
                    b.HasOne("Model.ExternalDataSources.ExternalDataSource", "ExternalDataSource")
                        .WithMany("InvalidRegisteredCirculators")
                        .HasForeignKey("ExternalDataSourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ExternalDataSource");
                });

            modelBuilder.Entity("Model.ExternalDataSources.InvalidVoter", b =>
                {
                    b.HasOne("Model.ExternalDataSources.ExternalDataSource", "ExternalDataSource")
                        .WithMany("InvalidRegisteredVoters")
                        .HasForeignKey("ExternalDataSourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Model.ExternalDataSources.ExternalDataSourcePart", "ExternalDataSourcePart")
                        .WithMany()
                        .HasForeignKey("ExternalDataSourcePartId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Model.ReferenceData.UsState", "UsState")
                        .WithMany()
                        .HasForeignKey("UsStateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ExternalDataSource");

                    b.Navigation("ExternalDataSourcePart");

                    b.Navigation("UsState");
                });

            modelBuilder.Entity("Model.ExternalDataSources.RegisteredCirculator", b =>
                {
                    b.HasOne("Model.ExternalDataSources.ExternalDataSource", "ExternalDataSource")
                        .WithMany("RegisteredCirculators")
                        .HasForeignKey("ExternalDataSourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ExternalDataSource");
                });

            modelBuilder.Entity("Model.ExternalDataSources.RegisteredVoter", b =>
                {
                    b.HasOne("Model.ExternalDataSources.ExternalDataSource", "ExternalDataSource")
                        .WithMany("RegisteredVoters")
                        .HasForeignKey("ExternalDataSourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Model.ExternalDataSources.ExternalDataSourcePart", "ExternalDataSourcePart")
                        .WithMany()
                        .HasForeignKey("ExternalDataSourcePartId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Model.ReferenceData.UsState", "UsState")
                        .WithMany()
                        .HasForeignKey("UsStateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ExternalDataSource");

                    b.Navigation("ExternalDataSourcePart");

                    b.Navigation("UsState");
                });

            modelBuilder.Entity("Model.ExternalDataSources.Signatory", b =>
                {
                    b.HasOne("Model.ReferenceData.UsState", "UsState")
                        .WithMany()
                        .HasForeignKey("UsStateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("UsState");
                });

            modelBuilder.Entity("Model.Matters.ExternalDataSourceMatter", b =>
                {
                    b.HasOne("Model.ExternalDataSources.ExternalDataSource", "ExternalDataSource")
                        .WithMany("ExternalDataSourceMatters")
                        .HasForeignKey("ExternalDataSourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Model.Matters.Matter", "Matter")
                        .WithMany("ExternalDataSourceMatters")
                        .HasForeignKey("MatterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ExternalDataSource");

                    b.Navigation("Matter");
                });

            modelBuilder.Entity("Model.Matters.MatterVariable", b =>
                {
                    b.HasOne("Model.Matters.Matter", "Matter")
                        .WithMany("Variables")
                        .HasForeignKey("MatterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Matter");
                });

            modelBuilder.Entity("Model.Rules.BackgroundOperation", b =>
                {
                    b.HasOne("Model.Matters.Matter", "Matter")
                        .WithMany()
                        .HasForeignKey("MatterId");

                    b.Navigation("Matter");
                });

            modelBuilder.Entity("Model.Rules.Rule", b =>
                {
                    b.HasOne("Model.Matters.Matter", "Matter")
                        .WithMany()
                        .HasForeignKey("MatterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Model.Workflow.Task", "Task")
                        .WithMany("Rules")
                        .HasForeignKey("TaskId");

                    b.Navigation("Matter");

                    b.Navigation("Task");
                });

            modelBuilder.Entity("Model.SignatureSheets.InvalidSheet", b =>
                {
                    b.HasOne("Model.Matters.Matter", "Matter")
                        .WithMany()
                        .HasForeignKey("MatterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Model.SignatureSheets.SignatureSheetUpload", "SignatureSheetUpload")
                        .WithMany()
                        .HasForeignKey("SignatureSheetUploadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Model.Templates.Template", "Template")
                        .WithMany()
                        .HasForeignKey("TemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Matter");

                    b.Navigation("SignatureSheetUpload");

                    b.Navigation("Template");
                });

            modelBuilder.Entity("Model.SignatureSheets.SignatureSheet", b =>
                {
                    b.HasOne("Model.ExternalDataSources.Circulator", "Circulator")
                        .WithMany("SignatureSheets")
                        .HasForeignKey("CirculatorId");

                    b.HasOne("Model.Matters.Matter", "Matter")
                        .WithMany()
                        .HasForeignKey("MatterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Model.SignatureSheets.SignatureSheetUpload", "SignatureSheetUpload")
                        .WithMany("SignatureSheets")
                        .HasForeignKey("SignatureSheetUploadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Model.Templates.Template", "Template")
                        .WithMany()
                        .HasForeignKey("TemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Circulator");

                    b.Navigation("Matter");

                    b.Navigation("SignatureSheetUpload");

                    b.Navigation("Template");
                });

            modelBuilder.Entity("Model.SignatureSheets.SignatureSheetCell", b =>
                {
                    b.HasOne("Model.SignatureSheets.SignatureSheetRow", "SignatureSheetRow")
                        .WithMany("Cells")
                        .HasForeignKey("SignatureSheetRowId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Model.Templates.TemplateSignatureColumn", "TemplateSignatureColumn")
                        .WithMany()
                        .HasForeignKey("TemplateSignatureColumnId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SignatureSheetRow");

                    b.Navigation("TemplateSignatureColumn");
                });

            modelBuilder.Entity("Model.SignatureSheets.SignatureSheetColumn", b =>
                {
                    b.HasOne("Model.SignatureSheets.SignatureSheetTable", "SignatureSheetTable")
                        .WithMany("Columns")
                        .HasForeignKey("SignatureSheetTableId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SignatureSheetTable");
                });

            modelBuilder.Entity("Model.SignatureSheets.SignatureSheetField", b =>
                {
                    b.HasOne("Model.SignatureSheets.SignatureSheet", "SignatureSheet")
                        .WithMany("Fields")
                        .HasForeignKey("SignatureSheetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Model.Templates.TranscribableField", "TranscribableField")
                        .WithMany()
                        .HasForeignKey("TranscribableFieldId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SignatureSheet");

                    b.Navigation("TranscribableField");
                });

            modelBuilder.Entity("Model.SignatureSheets.SignatureSheetFormLine", b =>
                {
                    b.HasOne("Model.SignatureSheets.SignatureSheetPage", "SignatureSheetPage")
                        .WithMany("FormLines")
                        .HasForeignKey("SignatureSheetPageId");

                    b.HasOne("Model.Templates.TemplateFormLine", "TemplateFormLine")
                        .WithMany()
                        .HasForeignKey("TemplateFormLineId");

                    b.Navigation("SignatureSheetPage");

                    b.Navigation("TemplateFormLine");
                });

            modelBuilder.Entity("Model.SignatureSheets.SignatureSheetPage", b =>
                {
                    b.HasOne("Model.SignatureSheets.SignatureSheet", "SignatureSheet")
                        .WithMany("Pages")
                        .HasForeignKey("SignatureSheetId");

                    b.Navigation("SignatureSheet");
                });

            modelBuilder.Entity("Model.SignatureSheets.SignatureSheetProcessing", b =>
                {
                    b.HasOne("Model.Matters.Matter", "Matter")
                        .WithMany()
                        .HasForeignKey("MatterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Matter");
                });

            modelBuilder.Entity("Model.SignatureSheets.SignatureSheetRow", b =>
                {
                    b.HasOne("Model.ExternalDataSources.RegisteredVoter", "RegisteredVoter")
                        .WithMany("SignatureRows")
                        .HasForeignKey("RegisteredVoterId");

                    b.HasOne("Model.ExternalDataSources.Signatory", "Signatory")
                        .WithMany("SignatureSheetRows")
                        .HasForeignKey("SignatoryId");

                    b.HasOne("Model.SignatureSheets.SignatureSheet", "SignatureSheet")
                        .WithMany("Rows")
                        .HasForeignKey("SignatureSheetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Model.SignatureSheets.SignatureSheetTable", "SignatureSheetTable")
                        .WithMany("Rows")
                        .HasForeignKey("SignatureSheetTableId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Model.Templates.TemplateSignatureTable", "TemplateSignatureTable")
                        .WithMany()
                        .HasForeignKey("TemplateSignatureTableId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("RegisteredVoter");

                    b.Navigation("Signatory");

                    b.Navigation("SignatureSheet");

                    b.Navigation("SignatureSheetTable");

                    b.Navigation("TemplateSignatureTable");
                });

            modelBuilder.Entity("Model.SignatureSheets.SignatureSheetTable", b =>
                {
                    b.HasOne("Model.SignatureSheets.SignatureSheet", "SignatureSheet")
                        .WithMany()
                        .HasForeignKey("SignatureSheetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SignatureSheet");
                });

            modelBuilder.Entity("Model.SignatureSheets.SignatureSheetUpload", b =>
                {
                    b.HasOne("Model.Matters.Matter", "Matter")
                        .WithMany()
                        .HasForeignKey("MatterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Model.Templates.Template", "Template")
                        .WithMany()
                        .HasForeignKey("TemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Matter");

                    b.Navigation("Template");
                });

            modelBuilder.Entity("Model.Templates.Template", b =>
                {
                    b.HasOne("Model.ReferenceData.UsState", "UsState")
                        .WithMany()
                        .HasForeignKey("UsStateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("UsState");
                });

            modelBuilder.Entity("Model.Templates.TemplateFormLine", b =>
                {
                    b.HasOne("Model.Templates.TemplatePage", "TemplatePage")
                        .WithMany("FormLines")
                        .HasForeignKey("TemplatePageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Model.Templates.TrainingModel", "TrainingModel")
                        .WithMany()
                        .HasForeignKey("TrainingModelId");

                    b.Navigation("TemplatePage");

                    b.Navigation("TrainingModel");
                });

            modelBuilder.Entity("Model.Templates.TemplateIgnoredWord", b =>
                {
                    b.HasOne("Model.Templates.Template", "Template")
                        .WithMany("IgnoredWords")
                        .HasForeignKey("TemplateId");

                    b.Navigation("Template");
                });

            modelBuilder.Entity("Model.Templates.TemplatePage", b =>
                {
                    b.HasOne("Model.Templates.Template", "Template")
                        .WithMany("Pages")
                        .HasForeignKey("TemplateId");

                    b.Navigation("Template");
                });

            modelBuilder.Entity("Model.Templates.TemplateSignatureColumn", b =>
                {
                    b.HasOne("Model.Templates.TemplateSignatureTable", "SignatureTable")
                        .WithMany("Columns")
                        .HasForeignKey("TemplateSignatureTableId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SignatureTable");
                });

            modelBuilder.Entity("Model.Templates.TemplateSignatureRow", b =>
                {
                    b.HasOne("Model.Templates.TemplateSignatureTable", "SignatureTable")
                        .WithMany("Rows")
                        .HasForeignKey("TemplateSignatureTableId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SignatureTable");
                });

            modelBuilder.Entity("Model.Templates.TemplateSignatureTable", b =>
                {
                    b.HasOne("Model.Templates.Template", "Template")
                        .WithMany("Tables")
                        .HasForeignKey("TemplateId");

                    b.HasOne("Model.Templates.TrainingModel", "TrainingModel")
                        .WithMany()
                        .HasForeignKey("TrainingModelId");

                    b.Navigation("Template");

                    b.Navigation("TrainingModel");
                });

            modelBuilder.Entity("Model.Templates.TranscribableField", b =>
                {
                    b.HasOne("Model.Templates.TemplatePage", "TemplatePage")
                        .WithMany("Fields")
                        .HasForeignKey("TemplatePageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Model.Templates.TrainingModel", "TrainingModel")
                        .WithMany()
                        .HasForeignKey("TrainingModelId");

                    b.Navigation("TemplatePage");

                    b.Navigation("TrainingModel");
                });

            modelBuilder.Entity("Model.Workflow.GroupTaskAssignment", b =>
                {
                    b.HasOne("Model.Workflow.StaffGroup", "StaffGroup")
                        .WithMany()
                        .HasForeignKey("StaffGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Model.Workflow.Task", "Task")
                        .WithMany()
                        .HasForeignKey("TaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("StaffGroup");

                    b.Navigation("Task");
                });

            modelBuilder.Entity("Model.Workflow.Task", b =>
                {
                    b.HasOne("Model.Matters.Matter", "Matter")
                        .WithMany()
                        .HasForeignKey("MatterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Model.Templates.Template", "Template")
                        .WithMany()
                        .HasForeignKey("TemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Model.Templates.TemplateSignatureTable", "SignatureTable")
                        .WithMany()
                        .HasForeignKey("TemplateSignatureTableId");

                    b.Navigation("Matter");

                    b.Navigation("SignatureTable");

                    b.Navigation("Template");
                });

            modelBuilder.Entity("Model.Workflow.UserGroupAssignment", b =>
                {
                    b.HasOne("Model.Matters.Matter", "Matter")
                        .WithMany()
                        .HasForeignKey("MatterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Model.Workflow.StaffGroup", "StaffGroup")
                        .WithMany()
                        .HasForeignKey("StaffGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Model.Authorization.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Matter");

                    b.Navigation("StaffGroup");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Model.Workflow.Work", b =>
                {
                    b.HasOne("Model.Matters.Matter", "Matter")
                        .WithMany()
                        .HasForeignKey("MatterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Model.SignatureSheets.SignatureSheet", "SignatureSheet")
                        .WithMany()
                        .HasForeignKey("SignatureSheetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Model.Workflow.Task", "Task")
                        .WithMany()
                        .HasForeignKey("TaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Model.Authorization.User", "User")
                        .WithMany("Works")
                        .HasForeignKey("UserId");

                    b.Navigation("Matter");

                    b.Navigation("SignatureSheet");

                    b.Navigation("Task");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Model.Workflow.WorkField", b =>
                {
                    b.HasOne("Model.Workflow.Work", "Work")
                        .WithMany("WorkFields")
                        .HasForeignKey("WorkId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Work");
                });

            modelBuilder.Entity("Model.Authorization.User", b =>
                {
                    b.Navigation("Deficiencies");

                    b.Navigation("Works");
                });

            modelBuilder.Entity("Model.Boundaries.Boundary", b =>
                {
                    b.Navigation("BoundaryPoints");
                });

            modelBuilder.Entity("Model.ExternalDataSources.Circulator", b =>
                {
                    b.Navigation("SignatureSheets");
                });

            modelBuilder.Entity("Model.ExternalDataSources.ExternalDataSource", b =>
                {
                    b.Navigation("ExternalDataSourceMatters");

                    b.Navigation("ExternalDataSourceParts");

                    b.Navigation("InvalidRegisteredCirculators");

                    b.Navigation("InvalidRegisteredVoters");

                    b.Navigation("RegisteredCirculators");

                    b.Navigation("RegisteredVoters");
                });

            modelBuilder.Entity("Model.ExternalDataSources.ExternalDataSourcePart", b =>
                {
                    b.Navigation("ExternalDataProcessingLogs");
                });

            modelBuilder.Entity("Model.ExternalDataSources.RegisteredVoter", b =>
                {
                    b.Navigation("SignatureRows");
                });

            modelBuilder.Entity("Model.ExternalDataSources.Signatory", b =>
                {
                    b.Navigation("SignatureSheetRows");
                });

            modelBuilder.Entity("Model.Matters.Matter", b =>
                {
                    b.Navigation("ExternalDataSourceMatters");

                    b.Navigation("Variables");
                });

            modelBuilder.Entity("Model.Rules.Rule", b =>
                {
                    b.Navigation("Deficiencies");
                });

            modelBuilder.Entity("Model.SignatureSheets.SignatureSheet", b =>
                {
                    b.Navigation("Fields");

                    b.Navigation("Pages");

                    b.Navigation("Rows");
                });

            modelBuilder.Entity("Model.SignatureSheets.SignatureSheetPage", b =>
                {
                    b.Navigation("FormLines");
                });

            modelBuilder.Entity("Model.SignatureSheets.SignatureSheetRow", b =>
                {
                    b.Navigation("Cells");
                });

            modelBuilder.Entity("Model.SignatureSheets.SignatureSheetTable", b =>
                {
                    b.Navigation("Columns");

                    b.Navigation("Rows");
                });

            modelBuilder.Entity("Model.SignatureSheets.SignatureSheetUpload", b =>
                {
                    b.Navigation("DataProcessingLogs");

                    b.Navigation("SignatureSheets");
                });

            modelBuilder.Entity("Model.Templates.Template", b =>
                {
                    b.Navigation("IgnoredWords");

                    b.Navigation("Pages");

                    b.Navigation("Tables");
                });

            modelBuilder.Entity("Model.Templates.TemplatePage", b =>
                {
                    b.Navigation("Fields");

                    b.Navigation("FormLines");
                });

            modelBuilder.Entity("Model.Templates.TemplateSignatureTable", b =>
                {
                    b.Navigation("Columns");

                    b.Navigation("Rows");
                });

            modelBuilder.Entity("Model.Workflow.Task", b =>
                {
                    b.Navigation("Rules");
                });

            modelBuilder.Entity("Model.Workflow.Work", b =>
                {
                    b.Navigation("WorkFields");
                });
#pragma warning restore 612, 618
        }
    }
}
