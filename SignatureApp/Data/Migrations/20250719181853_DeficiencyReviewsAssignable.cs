﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Data.Migrations
{
    /// <inheritdoc />
    public partial class DeficiencyReviewsAssignable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "AssignedOn",
                table: "InvalidSheets",
                newName: "AssignmentDate");

            migrationBuilder.AddColumn<int>(
                name: "UserId",
                table: "InvalidSheets",
                type: "int",
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Note",
                table: "DeficiencyReviews",
                type: "nvarchar(max)",
                maxLength: 4096,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200,
                oldNullable: true);

            migrationBuilder.AlterColumn<bool>(
                name: "IsDeficient",
                table: "DeficiencyReviews",
                type: "bit",
                nullable: true,
                oldClrType: typeof(bool),
                oldType: "bit");

            migrationBuilder.AddColumn<string>(
                name: "AssignedTo",
                table: "DeficiencyReviews",
                type: "nvarchar(256)",
                maxLength: 256,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "AssignmentDate",
                table: "DeficiencyReviews",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ReviewedDate",
                table: "DeficiencyReviews",
                type: "datetime2",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "UserId",
                table: "InvalidSheets");

            migrationBuilder.DropColumn(
                name: "AssignedTo",
                table: "DeficiencyReviews");

            migrationBuilder.DropColumn(
                name: "AssignmentDate",
                table: "DeficiencyReviews");

            migrationBuilder.DropColumn(
                name: "ReviewedDate",
                table: "DeficiencyReviews");

            migrationBuilder.RenameColumn(
                name: "AssignmentDate",
                table: "InvalidSheets",
                newName: "AssignedOn");

            migrationBuilder.AlterColumn<string>(
                name: "Note",
                table: "DeficiencyReviews",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldMaxLength: 4096,
                oldNullable: true);

            migrationBuilder.AlterColumn<bool>(
                name: "IsDeficient",
                table: "DeficiencyReviews",
                type: "bit",
                nullable: false,
                defaultValue: false,
                oldClrType: typeof(bool),
                oldType: "bit",
                oldNullable: true);
        }
    }
}
