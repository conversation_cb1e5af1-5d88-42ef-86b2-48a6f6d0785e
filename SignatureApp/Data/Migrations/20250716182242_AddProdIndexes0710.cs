﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Data.Migrations
{
    /// <inheritdoc />
    public partial class AddProdIndexes0710 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_SignatureSheetRows_IsReviewed_Validity",
                table: "SignatureSheetRows");

            migrationBuilder.DropIndex(
                name: "IX_DataTransformationStepStart_MatterId",
                table: "DataTransformationStepStart");

            migrationBuilder.CreateIndex(
                name: "IX_SignatureSheetRows_IsReviewed_Validity",
                table: "SignatureSheetRows",
                columns: new[] { "IsReviewed", "Validity" })
                .Annotation("SqlServer:Include", new[] { "TemplateSignatureTableId", "SignatoryId", "SignatureSheetId" });

            migrationBuilder.CreateIndex(
                name: "IX_DataTransformationStepStart_SignatureSheetUploadId",
                table: "DataTransformationStepStart",
                column: "SignatureSheetUploadId")
                .Annotation("SqlServer:Include", new[] { "FunctionStepName", "Input", "InvocationId", "MatterId", "StartDateTime", "TemplateId" });

            migrationBuilder.CreateIndex(
                name: "IX_DataTransformationStepResult_MatterId",
                table: "DataTransformationStepResult",
                column: "MatterId")
                .Annotation("SqlServer:Include", new[] { "FunctionStepName", "InvocationId", "Message", "Output", "OutputDateTime", "ResultStatusCode", "SignatureSheetUploadId", "TemplateId" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_SignatureSheetRows_IsReviewed_Validity",
                table: "SignatureSheetRows");

            migrationBuilder.DropIndex(
                name: "IX_DataTransformationStepStart_SignatureSheetUploadId",
                table: "DataTransformationStepStart");

            migrationBuilder.DropIndex(
                name: "IX_DataTransformationStepResult_MatterId",
                table: "DataTransformationStepResult");

            migrationBuilder.CreateIndex(
                name: "IX_SignatureSheetRows_IsReviewed_Validity",
                table: "SignatureSheetRows",
                columns: new[] { "IsReviewed", "Validity" })
                .Annotation("SqlServer:Include", new[] { "RegisteredVoterId", "SignatoryId", "SignatureSheetId" });

            migrationBuilder.CreateIndex(
                name: "IX_DataTransformationStepStart_MatterId",
                table: "DataTransformationStepStart",
                column: "MatterId")
                .Annotation("SqlServer:Include", new[] { "FunctionStepName", "Input", "InvocationId", "SignatureSheetUploadId", "StartDateTime", "TemplateId" });
        }
    }
}
