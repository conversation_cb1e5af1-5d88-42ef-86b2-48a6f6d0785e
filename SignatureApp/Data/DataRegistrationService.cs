using Data.Repositories;
using DataInterface.RepositoryInterfaces;
using DataInterface.ServiceInterfaces;
using Microsoft.Extensions.DependencyInjection;
using Service;

namespace Data;

public class DataRegistrationService
{
    public static void RegisterRepositories(IServiceCollection services)
    {
        // The one service in the data layer
        services.AddScoped<IFileService, AzureBlobFileService>();

        //ExternalData
        services.AddScoped<ICirculatorRepository, CirculatorRepository>();
        services.AddScoped<IExternalDataSourceRepository, ExternalDataSourceRepository>();
        services.AddScoped<IDataProcessingLogRepository, DataProcessingLogRepository>();
        services.AddScoped<IRegisteredVoterRepository, RegisteredVoterRepository>();
        services.AddScoped<IRegisteredCirculatorRepository, RegisteredCirculatorRepository>();
        services.AddScoped<IInvalidVoterRepository, InvalidVoterRepository>();
        services.AddScoped<IInvalidCirculatorRepository, InvalidCirculatorRepository>();

        //Authorization
        services.AddScoped<IRoleRepository, RoleRepository>();
        services.AddScoped<IUserRepository, UserRepository>();

        //Matters
        services.AddScoped<IMatterRepository, MatterRepository>();
        services.AddScoped<IMatterVariableRepository, MatterVariableRepository>();

        //SignatureSheets
        services.AddScoped<IInvalidSheetRepository, InvalidSheetRepository>();
        services.AddScoped<ISignatoryRepository, SignatoryRepository>();
        services.AddScoped<ISignatureSheetRepository, SignatureSheetRepository>();
        services.AddScoped<ISignatureSheetPageRepository, SignatureSheetPageRepository>();
        services.AddScoped<ISignatureSheetFormLineRepository, SignatureSheetFormLineRepository>();
        services.AddScoped<ISignatureSheetTableRepository, SignatureSheetTableRepository>();
        services.AddScoped<ISignatureSheetColumnRepository, SignatureSheetColumnRepository>();
        services.AddScoped<ISignatureSheetCellRepository, SignatureSheetCellRepository>();
        services.AddScoped<ISignatureSheetRowRepository, SignatureSheetRowRepository>();
        services.AddScoped<ISignatureSheetFieldRepository, SignatureSheetFieldRepository>();
        services.AddScoped<ISignatureSheetProcessingRepository, SignatureSheetProcessingRepository>();
        services.AddScoped<ISignatureSheetUploadRepository, SignatureSheetUploadRepository>();

        //Template
        services.AddScoped<ITemplateRepository, TemplateRepository>();
        services.AddScoped<ITemplateIgnoredWordRepository, TemplateIgnoredWordRepository>();
        services.AddScoped<ITemplatePageRepository, TemplatePageRepository>();
        services.AddScoped<ITemplateFormLineRepository, TemplateFormLineRepository>();
        services.AddScoped<ITranscribableFieldRepository, TranscribableFieldRepository>();
        services.AddScoped<ITrainingModelRepository, TrainingModelRepository>();
        services.AddScoped<ITemplateSignatureColumnRepository, TemplateSignatureColumnRepository>();
        services.AddScoped<ITemplateSignatureRowRepository, TemplateSignatureRowRepository>();
        services.AddScoped<ITemplateSignatureCellRepository, TemplateSignatureCellRepository>();
        services.AddScoped<ITemplateSignatureTableRepository, TemplateSignatureTableRepository>();

        //Workflow
        services.AddScoped<IGroupTaskAssignmentRepository, GroupTaskAssignmentRepository>();
        services.AddScoped<IStaffGroupRepository, StaffGroupRepository>();
        services.AddScoped<ITaskRepository, TaskRepository>();
        services.AddScoped<IUserGroupAssignmentRepository, UserGroupAssignmentRepository>();
        services.AddScoped<IWorkRepository, WorkRepository>();
        services.AddScoped<IWorkFieldRepository, WorkFieldRepository>();

        //Rules
        services.AddScoped<IDeficiencyRepository, DeficiencyRepository>();
        services.AddScoped<IDeficiencyReviewRepository, DeficiencyReviewRepository>();
        services.AddScoped<IRuleRepository, RuleRepository>();
        services.AddScoped<IBackgroundOperationRepository, BackgroundOperationRepository>();

        //Boundary
        services.AddScoped<IBoundaryRepository, BoundaryRepository>();
        services.AddScoped<IBoundaryPointRepository, BoundaryPointRepository>();
        services.AddScoped<IBoundaryOverlapRepository, BoundaryOverlapRepository>();

        //DataTransformationStep
        services.AddScoped<IDataTransformationStepStartRepository, DataTransformationStepStartRepository>();
        services.AddScoped<IDataTransformationStepResultRepository, DataTransformationStepResultRepository>();

        //ReferenceData
        services.AddScoped<IUsStateRepository, UsStateRepository>();
    }
}
