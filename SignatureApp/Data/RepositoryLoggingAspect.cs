using System.Diagnostics;
using Microsoft.Extensions.Logging;
using PostSharp.Aspects;
using PostSharp.Extensibility;
using PostSharp.Serialization;

namespace Data;

[PSerializable]
[MulticastAttributeUsage(MulticastTargets.Method, Inheritance = MulticastInheritance.Strict, AllowExternalAssemblies = true)]
public class RepositoryLoggingAspect : OnMethodBoundaryAspect
{
    private static bool _isLoggingEnabled;
    private static bool _logEnumerables;
    private static ILogger<RepositoryLoggingAspect>? _logger;

    public static void Initialize(bool loggingEnabled, bool logEnumerables, ILogger<RepositoryLoggingAspect> logger)
    {
        // This method can be used to set the logging state dynamically if needed.
        _isLoggingEnabled = loggingEnabled;
        _logEnumerables = logEnumerables;
        _logger = logger;
    }

    public override void OnEntry(MethodExecutionArgs args)
    {
        if (ShouldLog(args))
        {
            args.MethodExecutionTag = Stopwatch.StartNew();
        }
    }

    private static string GetArgumentsString(MethodExecutionArgs args)
    {
        var argsString = args.Arguments.Select(a =>
        {
            if (_logEnumerables && a is System.Collections.IEnumerable enumerable and not string)
            {
                return $"List[{string.Join(", ", enumerable.Cast<object>().Take(3))}, ...]";
            }

            return a?.ToString() ?? "null";
        });
        return string.Join(", ", argsString);
    }

    public override void OnExit(MethodExecutionArgs args)
    {
        if (ShouldLog(args))
        {
            var argsString = GetArgumentsString(args);
            if (args.MethodExecutionTag is Stopwatch stopwatch)
            {
                stopwatch.Stop();
                _logger?.LogInformation(
                    "Repository Aspect Logging: {Repository}.{RepositoryMethod}({arguments}) - Duration: {ElapsedMilliseconds} ms",
                    args.Method.DeclaringType?.Name, args.Method.Name, argsString, stopwatch.ElapsedMilliseconds);
            }
            else
            {
                _logger?.LogInformation(
                    $"{args.Instance.GetHashCode()} {args.Method.DeclaringType?.Name}.{args.Method.Name}({argsString})");
            }
        }
    }

    public override void OnException(MethodExecutionArgs args)
    {
        if (_isLoggingEnabled)
        {
            _logger?.LogInformation(
                $"[Exception] {args.Method.DeclaringType?.Name}.{args.Method.Name}: {args.Exception.Message}");
        }
    }

    private static bool ShouldLog(MethodExecutionArgs args)
    {
        return _isLoggingEnabled
               && !args.Method.IsSpecialName
               && args.Method.DeclaringType != null
               && !args.Method.DeclaringType.Name.StartsWith("EfCoreBaseRepository");
    }
}