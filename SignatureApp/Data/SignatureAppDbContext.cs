﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Model;
using Model.Authorization;
using Model.Boundaries;
using Model.DataTransformation;
using Model.Deficiencies;
using Model.ExternalDataSources;
using Model.Interfaces;
using Model.Matters;
using Model.ReferenceData;
using Model.Rules;
using Model.SignatureSheets;
using Model.Templates;
using Model.Workflow;

namespace Data;


public class SignatureAppDbContext : DbContext
{
    // Authorization
    public DbSet<User> Users => Set<User>();
    public DbSet<Role> Roles => Set<Role>();

    // External Data Sources
    public DbSet<Circulator> Circulators => Set<Circulator>();
    public DbSet<ExternalDataSource> ExternalDataSources => Set<ExternalDataSource>();
    public DbSet<ExternalDataSourcePart> ExternalDataSourceParts => Set<ExternalDataSourcePart>();
    public DbSet<ExternalDataSourceMatter> ExternalDataSourceMatter => Set<ExternalDataSourceMatter>();
    public DbSet<DataProcessingLog> DataProcessingLogs => Set<DataProcessingLog>();
    public DbSet<RegisteredVoter> RegisteredVoters => Set<RegisteredVoter>();
    public DbSet<RegisteredCirculator> RegisteredCirculators => Set<RegisteredCirculator>();
    public DbSet<InvalidVoter> InvalidVoters => Set<InvalidVoter>();
    public DbSet<InvalidCirculator> InvalidCirculators => Set<InvalidCirculator>();

    // Matters
    public DbSet<Matter> Matters => Set<Matter>();
    public DbSet<MatterVariable> MatterVariables => Set<MatterVariable>();

    // Reference
    public DbSet<UsState> UsStates => Set<UsState>();

    // Templates
    public DbSet<Template> Templates => Set<Template>();
    public DbSet<TemplatePage> TemplatePages => Set<TemplatePage>();
    public DbSet<TemplateFormLine> TemplateFormLines => Set<TemplateFormLine>();
    public DbSet<TemplateIgnoredWord> TemplateIgnoredWords => Set<TemplateIgnoredWord>();
    public DbSet<TranscribableField> TranscribableFields => Set<TranscribableField>();

    public DbSet<TemplateSignatureTable> TemplateSignatureTables => Set<TemplateSignatureTable>();
    public DbSet<TemplateSignatureColumn> TemplateSignatureColumns => Set<TemplateSignatureColumn>();
    public DbSet<TemplateSignatureRow> TemplateSignatureRows => Set<TemplateSignatureRow>();
    public DbSet<TemplateSignatureCell> TemplateSignatureCells => Set<TemplateSignatureCell>();
    public DbSet<TrainingModel> TrainingModels => Set<TrainingModel>();

    // Signature Sheets
    public DbSet<InvalidSheet> InvalidSheets => Set<InvalidSheet>();
    public DbSet<Signatory> Signatories => Set<Signatory>();
    public DbSet<SignatureSheet> SignatureSheets => Set<SignatureSheet>();
    public DbSet<SignatureSheetUpload> SignatureSheetUploads => Set<SignatureSheetUpload>();
    public DbSet<SignatureSheetProcessing> SignatureSheetProcessings => Set<SignatureSheetProcessing>();
    public DbSet<SignatureSheetPage> SignatureSheetPages => Set<SignatureSheetPage>();
    public DbSet<SignatureSheetFormLine> SignatureSheetFormLines => Set<SignatureSheetFormLine>();
    public DbSet<SignatureSheetField> SignatureSheetFields => Set<SignatureSheetField>();

    public DbSet<SignatureSheetTable> SignatureSheetTables => Set<SignatureSheetTable>();
    public DbSet<SignatureSheetColumn> SignatureSheetColumns => Set<SignatureSheetColumn>();
    public DbSet<SignatureSheetRow> SignatureSheetRows => Set<SignatureSheetRow>();
    public DbSet<SignatureSheetCell> SignatureSheetCells => Set<SignatureSheetCell>();

    // Workflow
    public DbSet<GroupTaskAssignment> GroupTaskAssignments => Set<GroupTaskAssignment>();
    public DbSet<StaffGroup> StaffGroups => Set<StaffGroup>();
    public DbSet<Model.Workflow.Task> Tasks => Set<Model.Workflow.Task>();
    public DbSet<UserGroupAssignment> UserGroupAssignments => Set<UserGroupAssignment>();
    public DbSet<Work> Works => Set<Work>();
    public DbSet<WorkField> WorkFields => Set<WorkField>();

    // Rules
    public DbSet<Deficiency> Deficiencies => Set<Deficiency>();
    public DbSet<DeficiencyReview> DeficiencyReviews => Set<DeficiencyReview>();
    public DbSet<Rule> Rules => Set<Rule>();
    public DbSet<BackgroundOperation> BackgroundOperations => Set<BackgroundOperation>();

    // Boundaries
    public DbSet<Boundary> Boundaries => Set<Boundary>();
    public DbSet<BoundaryPoint> BoundaryPoints => Set<BoundaryPoint>();
    public DbSet<BoundaryOverlap> BoundaryOverlaps => Set<BoundaryOverlap>();

    // Data Transformation
    public DbSet<DataTransformationStepStart> DataTransformationStepStart => Set<DataTransformationStepStart>();
    public DbSet<DataTransformationStepResult> DataTransformationStepResult => Set<DataTransformationStepResult>();

    public SignatureAppDbContext(DbContextOptions<SignatureAppDbContext> options) : base(options)
    {
    }

    //protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    //    => optionsBuilder.LogTo(message => Debug.WriteLine(message));

    // User defined functions
    // [DbFunction("SplitString", "dbo")] // Name of the SQL function and schema, needs migration
    // public static IQueryable<SplitStringResult> SplitString(string inputString, string delimiter)
    // {
    //     throw new NotSupportedException(); // This method is never executed client-side
    // }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Configure SplitStringResult as a keyless entity
        // this.AddUserDefinedFunction(modelBuilder);
        // modelBuilder.Entity<SplitStringResult>().HasNoKey();

        var startDate = new DateTime(2022, 3, 1);
        modelBuilder.Entity<User>().HasAlternateKey(u => u.Email);

        modelBuilder.Entity<DataTransformationStepResult>()
            .HasIndex(dtsr => new { dtsr.MatterId})
            .IncludeAllProperties(excludeExpression: dtsr => dtsr.Id);

        modelBuilder.Entity<DataTransformationStepStart>()
            .HasIndex(dtss => new { dtss.SignatureSheetUploadId})
            .IncludeAllProperties(excludeExpression: dtss => dtss.Id);

        modelBuilder.Entity<TranscribableField>(FixSimpleBounds);
        modelBuilder.Entity<TemplateFormLine>(FixSimpleBounds);

        modelBuilder.Entity<TemplateSignatureTable>(FixSimpleBounds);
        modelBuilder.Entity<TemplateSignatureColumn>(FixSimpleBounds);
        modelBuilder.Entity<TemplateSignatureRow>(FixSimpleBounds);
        modelBuilder.Entity<TemplateSignatureCell>(FixSimpleBounds);

        modelBuilder.Entity<RegisteredVoter>()
            .HasIndex(rv => new
            {
                rv.FirstName, rv.LastName,
                rv.StreetNumber, rv.Direction, rv.StreetName, rv.StreetType, rv.UsStateId
            })
            .IncludeAllProperties();

        modelBuilder.Entity<SignatureSheetProcessing>()
            .HasIndex(ssp => new { ssp.MatterId, ssp.SheetNumber });
        modelBuilder.Entity<SignatureSheetTable>(FixSimpleBounds);
        modelBuilder.Entity<SignatureSheetColumn>(FixSimpleBounds);
        modelBuilder.Entity<SignatureSheetCell>(FixSimpleBounds);
        modelBuilder.Entity<SignatureSheetCell>()
            .HasIndex(i => new { i.SignatureSheetRowId })
            .IncludeAllProperties(excludeExpression: i=>new { i.Id });

        modelBuilder.Entity<SignatureSheetRow>(FixSimpleBounds);
        modelBuilder.Entity<SignatureSheetRow>().HasMany(sr => sr.Cells).WithOne(sc => sc.SignatureSheetRow);
        modelBuilder.Entity<SignatureSheetRow>().HasIndex(sr=> new {sr.IsReviewed, sr.Validity})
            .IncludeProperties(sr=> new {sr.TemplateSignatureTableId, sr.SignatoryId, sr.SignatureSheetId});

        modelBuilder.Entity<SignatureSheetField>(FixSimpleBounds);
        modelBuilder.Entity<SignatureSheetField>()
            .HasIndex(ssf=> new { ssf.SignatureSheetId })
            .IncludeAllProperties(excludeExpression: ssf=> new { ssf.Id});

        modelBuilder.Entity<SignatureSheetFormLine>(FixSimpleBounds);

        modelBuilder.Entity<WorkField>().HasKey(wf => new { wf.WorkId, wf.FieldId });
        modelBuilder.Entity<Work>().HasIndex(w=> new { w.UserId, w.WorkStatus })
            .IncludeProperties(w=> new {w.SecondsWorked, w.LastStartDateTime, w.LastStopDateTime});

        modelBuilder.Entity<Role>()
            .HasData(
                new Role { Id = (int)RoleType.Admin, Name = Enum.GetName(RoleType.Admin)! },
                new Role { Id = (int)RoleType.Manager, Name = Enum.GetName(RoleType.Manager)! },
                new Role { Id = (int)RoleType.Unauthorized, Name = Enum.GetName(RoleType.Unauthorized)! },
                new Role { Id = (int)RoleType.Reviewer, Name = Enum.GetName(RoleType.Reviewer)! }
                );

        modelBuilder.Entity<User>()
            .HasData(
                new User { Email = "<EMAIL>", FullName = "Test Admin", ShortName = "admin", IsValidated = true, PasswordSalt = 1, RoleId = 1, PasswordHash = Utility.GetHash("admin", 1), Id = -1, PhoneNumber = "1", IsDeleted = false, StartDate = startDate },
                new User { Email = "<EMAIL>", FullName = "Test Manager", ShortName = "manager", IsValidated = true, PasswordSalt = 1, RoleId = 2, PasswordHash = Utility.GetHash("manager", 1), Id = -2, PhoneNumber = "1", IsDeleted = false, StartDate = startDate },
                new User { Email = "<EMAIL>", FullName = "Test Staff", ShortName = "staff", IsValidated = true, PasswordSalt = 1, RoleId = 3, PasswordHash = Utility.GetHash("staff", 1), Id = -3, PhoneNumber = "1", IsDeleted = false, StartDate = startDate },
                new User { Email = "<EMAIL>", FullName = "System Reviewer", ShortName = "system", IsValidated = true, PasswordSalt = 1, RoleId = 3, PasswordHash = Utility.GetHash("system_reviewer", 1), Id = -4, PhoneNumber = "1", IsDeleted = false, StartDate = startDate }
                );

        modelBuilder.Entity<UsState>()
            .HasData(global::Model.Geocoding.UsStates.usStates);

        modelBuilder.Entity<Matter>().HasData(CreateTestMatter());
        modelBuilder.Entity<StaffGroup>().HasData(CreateTestStaffGroup());
        modelBuilder.Entity<UserGroupAssignment>().HasData(CreateTestUserGroupAssignment());
        modelBuilder.Entity<Template>().HasData(CreateTestTemplate(startDate));
        modelBuilder.Entity<TemplatePage>().HasData(CreateTestTemplatePage());
        modelBuilder.Entity<TranscribableField>().HasData(CreateTestTranscribableField());
        modelBuilder.Entity<BoundaryOverlap>()
            .HasIndex(b => new { b.OuterBoundaryId, b.InnerBoundaryId })
            .IsUnique();
    }

    private static void FixSimpleBounds<T>(EntityTypeBuilder<T> x)
    where T : class, IHaveSimpleBounds
    {
        x.Property(t => t.Left).HasPrecision(12, 6);
        x.Property(t => t.Top).HasPrecision(12, 6);
        x.Property(t => t.Right).HasPrecision(12, 6);
        x.Property(t => t.Bottom).HasPrecision(12, 6);
    }

    private Matter CreateTestMatter()
    {
        return new Matter
        {
            Id = -1,
            Name = "Test Matter Data",
            NumberSignaturesRequired = 10,
            IsGoalDeficencies = true,
            IsActive = true,
        };
    }

    private StaffGroup CreateTestStaffGroup()
    {
        return new StaffGroup
        {
            Name = "Test Staff Group",
            Id = -1
        };
    }

    private UserGroupAssignment CreateTestUserGroupAssignment()
    {
        return new UserGroupAssignment
        {
            Id = -1,
            MatterId = -1,
            StaffGroupId = -1,
            UserId = -3
        };
    }

    private Template CreateTestTemplate(DateTime date) => new Template
    {
        Id = -1,
        FileName = "testFileName.pdf",
        Name = "Test Template",
        UploadedBy = "<EMAIL>",
        UploadedOn = date,
    };

    private TemplatePage CreateTestTemplatePage() => new TemplatePage
    {
        Id = -1,
        TemplateId = -1,
        Width = 11,
        Height = 8.5F,
        PageNumber = 1
    };

    private TranscribableField CreateTestTranscribableField() => new TranscribableField
    {
        Id = -1,
        IsHandwritten = true,
        Name = "TestField",
        PageNumber = 1,
        Left = 0.5m,
        Top = 0.5m,
        Right = 1.0m,
        Bottom = 1.0m,
        TemplatePageId = -1
    };
}
