using DataInterface.RepositoryInterfaces;
using Microsoft.EntityFrameworkCore;
using Model.Workflow;
using System.Linq.Expressions;
using Model.DTOs;

namespace Data.Repositories
{
    public class WorkRepository : EfCoreBaseRepository<Work>, IWorkRepository
    {
        const int MAX_ALLOWED_DURATION_SECONDS = 60 * 10;

        public WorkRepository(SignatureAppDbContext context) : base(context)
        {
        }

        public async Task<Work?> GetPreviousWorkByUserIdAsync(DateTime? assignmentDate, int userId, int workId)
        {
            return await _set
                .Include(x => x.Matter)
                .Include(x => x.Task)
                .Include(x => x.WorkFields)
                .Where(x => x.UserId == userId && x.AssignmentDate < assignmentDate)
                .OrderByDescending(x => x.AssignmentDate)
                .FirstOrDefaultAsync();
        }

        public async Task<Work?> GetWorkWithTaskIncludedAsync(int workId)
        {
            return await _set
                .Include(x => x.Matter)
                .Include(x => x.Task)
                .Include(x => x.WorkFields)
                .AsSplitQuery()
                .FirstOrDefaultAsync(x => x.Id == workId);
        }

        public async Task<Work?> GetNextUnassignedWorkAsync(List<Model.Workflow.Task> tasks, int userId)
        {
            return await NextUnassignedWorkAsync(tasks.Select(x => x.Id), userId);
        }

        public async Task<Work?> GetNextWorkFromTasksAsync(List<Model.Workflow.Task> tasks, int userId, int workId,
            DateTime? assignmentDate)
        {
            var nextAssignedWork = await _set
                .Include(x => x.Matter)
                .Include(x => x.Task)
                .Include(x => x.WorkFields)
                .AsSplitQuery()
                .Where(x => x.UserId == userId && x.AssignmentDate > assignmentDate)
                .OrderBy(x => x.AssignmentDate).FirstOrDefaultAsync();
            if (nextAssignedWork is not null) return nextAssignedWork;

            return await NextUnassignedWorkAsync(tasks.Select(x => x.Id), userId);
        }

        public async Task<Work?> GetNextFlaggedWorkAsync(int matterId, int userId)
        {
            return await _set
                .Include(x => x.Matter)
                .Include(x => x.Task)
                .Include(x => x.WorkFields)
                .AsSplitQuery()
                .Where(x => x.MatterId == matterId)
                .Where(x => x.WorkStatus == WorkStatus.Flagged
                            || (x.UserId == userId && x.WorkStatus == WorkStatus.Assigned))
                .OrderBy(x => x.AssignmentDate)
                .FirstOrDefaultAsync();
        }

        public async Task<bool> IsAllWorkBySheetIdCompleteAsync(int signatureSheetId)
        {
            return await _set.Where(x => x.SignatureSheetId == signatureSheetId)
                .AllAsync(x => x.WorkStatus == WorkStatus.Completed);
        }

        public async Task<bool> IsAllWorkCompleteAsync(int matterId)
        {
            return await _set.Where(x => x.MatterId == matterId)
                .AllAsync(x => x.WorkStatus == WorkStatus.Completed);
        }

        private async Task<Work?> NextUnassignedWorkAsync(IEnumerable<int> tasksIds, int userId)
        {
            return await _set
                .Include(x => x.Matter)
                .Include(x => x.Task)
                .Include(x => x.WorkFields)
                .AsSplitQuery()
                .Where(x => x.Matter.IsActive == true)
                .Where(x => x.WorkStatus == WorkStatus.None
                            || (x.UserId == userId &&
                                (x.WorkStatus == WorkStatus.Assigned || x.WorkStatus == WorkStatus.Break)))
                .Where(x => tasksIds.Contains(x.TaskId))
                .OrderBy(x => x.Matter.DueDate)
                .ThenBy(x => x.Matter.CreatedOn)
                .ThenBy(x => x.Task.Priority)
                .ThenBy(x => (x.SheetNumber * 10000) + (x.RowNumber * 100) + x.FieldNumber)
                .FirstOrDefaultAsync();
        }

        public async Task<int> GetCountByMatterIdAndExpressionAsync(int matterId, Expression<Func<Work, bool>> pred)
        {
            var query = _set.Where(pred);
            if (matterId != 0)
            {
                query = query.Where(x => x.MatterId == matterId);
            }

            return await query.CountAsync();
        }

        public async Task<List<WorkMatterStatus>> GetAllWorkByMatterAndStatusAsync()
        {
            var results = await _set
                .Include(x => x.Matter)
                .GroupBy(x => new { x.MatterId, x.WorkStatus })
                .Select(g => new WorkMatterStatus()
                {
                    MatterId = g.Key.MatterId,
                    WorkStatus = g.Key.WorkStatus,
                    Count = g.Count(),
                })
                .ToListAsync();
            return results;
        }

        public async Task<List<WorkTaskStatus>> GetAllWorkByMatterIdAndTaskAsync(int matterId)
        {
            return await _set
                .Include(x => x.Task)
                .Where(x => x.MatterId == matterId)
                .GroupBy(x => new { x.TaskId, x.WorkStatus })
                .Select(g => new WorkTaskStatus()
                {
                    TaskId = g.Key.TaskId,
                    TaskName = g.First().Task.Name,
                    WorkStatus = g.Key.WorkStatus,
                    Count = g.Count()
                })
                .ToListAsync();
        }

        public async System.Threading.Tasks.Task UpdateAllDurationOverLimitAsync()
        {
            await _set.Where(w => w.SecondsWorked > MAX_ALLOWED_DURATION_SECONDS)
                .ExecuteUpdateAsync(w => w.SetProperty(x => x.SecondsWorked, MAX_ALLOWED_DURATION_SECONDS));
        }

        public async Task<(double totalSeconds, int totalFlagged, int totalCompleted)> GetTotalsByUserAsync(int userId)
        {
            var workForUser = await _set.Where(w => w.UserId == userId)
                .Select(w => new { w.Id, w.SecondsWorked, w.WorkStatus })
                .ToListAsync();

            double totalSeconds = workForUser
                .Where(w => w.WorkStatus == WorkStatus.Completed)
                .Sum(w => w.SecondsWorked);
            int totalFlagged = workForUser
                .Count(w => w.WorkStatus == WorkStatus.Flagged);
            int totalCompleted = workForUser
                .Count(w => w.WorkStatus == WorkStatus.Completed);
            return (totalSeconds, totalFlagged, totalCompleted);
        }

        public Task<Work?> GetBySheetIdAndFieldNumber(int sheetId, int transcribableFieldFieldNumber)
        {
            return _set
                .FirstOrDefaultAsync(x =>
                    x.SignatureSheetId == sheetId && x.FieldNumber == transcribableFieldFieldNumber);
        }

        public Task<Work?> GetByFieldIdAsync(int fieldId)
        {
            return (
                from w in _set
                from field in w.WorkFields
                where field.FieldType == FieldType.SignatureSheetField && field.FieldId == fieldId
                select w
            ).SingleOrDefaultAsync();
        }

        public Task<Work?> GetByCellIdAsync(int cellId)
        {
            var query = (
                from w in _set.Include(w => w.WorkFields).Include(w => w.Task)
                from field in w.WorkFields
                where field.FieldType == FieldType.SignatureSheetCell && field.FieldId == cellId
                select w
            );
            return query.FirstOrDefaultAsync();
        }

        public async Task<Work?> GetVoterRegistrationWorkByRowIdAsync(int matterId, int sheetId, int rowId)
        {
            var templateId = await _context.SignatureSheets
                .Include(ss => ss.SignatureSheetUpload)
                .Where(ss => ss.Id == sheetId)
                .Select(ss => ss.SignatureSheetUpload.TemplateId)
                .SingleOrDefaultAsync();

            var rowNumber = await _context.SignatureSheetRows
                .Where(sr => sr.Id == rowId)
                .Select(sr => sr.RowNumber)
                .SingleOrDefaultAsync();

            var voterRegistrationType = TaskType.ExternalDataSourceVerification | TaskType.SignatureTableColumn;
            var voterRegistrationTask = _context.Tasks.Where(t => t.MatterId == matterId && t.TemplateId == templateId)
                .SingleOrDefault(t => t.TaskType == voterRegistrationType);
            if (voterRegistrationTask == null)
            {
                return null;
            }

            var work = await _set
                .SingleOrDefaultAsync(x =>
                    x.SignatureSheetId == sheetId && x.RowNumber == rowNumber && x.TaskId == voterRegistrationTask.Id);
            return work;
        }

        public async Task<List<Work>> GetAllForMatterAndSheetNumberAsync(int matterId, int sheetNumber,
            Expression<Func<Work, bool>>? predicate = null)
        {
            var query = _set
                .Include(w => w.Task)
                .Where(w => w.MatterId == matterId && w.SheetNumber == sheetNumber);
            if (predicate != null)
            {
                query = query.Where(predicate);
            }

            var work = await query
                .OrderBy(w => w.RowNumber).ThenBy(w => w.Task.FirstColumnIndex).ThenBy(w => w.FieldNumber)
                .ToListAsync();
            return work;
        }

        public async Task<List<Work>> GetAllForMatterSheetAndRowNumberAsync(int matterId, int sheetNumber,
            int rowNumber)
        {
            var work = await _set
                .Include(w => w.Task)
                .Where(w => w.MatterId == matterId && w.SheetNumber == sheetNumber && w.RowNumber == rowNumber)
                .OrderBy(w => w.RowNumber).ThenBy(w => w.Task.FirstColumnIndex)
                .ToListAsync();
            return work;
        }

        public async Task<List<Work>> GetAllFieldsForMatterSheetAndPageNumberAsync(int matterId, int sheetNumber,
            int? pageNumber = null)
        {
            IQueryable<Work> query;
            if (pageNumber == null)
            {
                query =
                    from w in _set
                    join t in _context.Tasks on w.TaskId equals t.Id
                    join tp in _context.TemplatePages on t.TemplateId equals tp.TemplateId
                    join tf in _context.TranscribableFields
                        on new { TemplatePageId = tp.Id, w.FieldNumber } equals new
                            { tf.TemplatePageId, tf.FieldNumber }
                    where w.MatterId == matterId && w.SheetNumber == sheetNumber && w.FieldNumber > 0
                    select w;
            }
            else
            {
                query =
                    from w in _set
                    join t in _context.Tasks on w.TaskId equals t.Id
                    join tp in _context.TemplatePages on t.TemplateId equals tp.TemplateId
                    join tf in _context.TranscribableFields
                        on new { TemplatePageId = tp.Id, w.FieldNumber } equals new
                            { tf.TemplatePageId, tf.FieldNumber }
                    where w.MatterId == matterId && w.SheetNumber == sheetNumber && tp.PageNumber == pageNumber &&
                          w.FieldNumber > 0
                    select w;
            }

            var work = await query
                .OrderBy(w => w.FieldNumber)
                .ToListAsync();
            return work;
        }

        public async Task<List<Work>> GetAllWorkByMatterIdAsync(int matterId)
        {
            var work = await _set
                .Include(w => w.Task)
                .Where(w => w.MatterId == matterId)
                .ToListAsync();
            return work;
        }

        public async Task<List<Work>> GetAllWorkByTaskId(int taskId)
        {
            var work = await _set
                .Include(w => w.Task)
                .Include(w => w.WorkFields)
                .Where(w => w.TaskId == taskId)
                .ToListAsync();
            return work;
        }
    }
}