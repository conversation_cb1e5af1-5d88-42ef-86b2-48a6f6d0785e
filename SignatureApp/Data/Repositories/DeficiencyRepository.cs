using Model.Rules;
using Microsoft.EntityFrameworkCore;
using DataInterface.RepositoryInterfaces;
using Model.Authorization;
using Model.Deficiencies;
using Model.SignatureSheets;

namespace Data.Repositories
{
    public class DeficiencyRepository : EfCoreBaseRepository<Deficiency>, IDeficiencyRepository
    {
        public DeficiencyRepository(SignatureAppDbContext context) : base(context)
        {
        }

        public async Task<List<Deficiency>> GetAllRuleGeneratedDeficienciesByMatterIdAsync(int matterId)
        {
            var ruleGeneratedDeficiencies = await _set
                .Where(d => d.MatterId == matterId
                            && d.UserId == null && d.ReviewedBy == null)
                .ToListAsync();
            return ruleGeneratedDeficiencies;
        }

        public async Task<List<DeficiencyReview>> GetAllDeficiencyReviewsByMatterIdAsync(int matterId)
        {
            return await _context.DeficiencyReviews
                .Where(d => d.MatterId == matterId)
                .ToListAsync();
        }

        public override async Task<Deficiency?> GetByIdAsync(int deficiencyId)
        {
            return await _set
                .Include(d => d.Rule)
                .Include(d => d.Matter)
                .SingleOrDefaultAsync(d => d.Id == deficiencyId);
        }

        public async Task<Deficiency?> GetDeficiencyAndSignatureSheetByIdAsync(int deficiencyId)
        {
            return await _set
                .Include(d => d.Rule)
                .Include(d => d.Matter)
                .Include(d => d.SignatureSheet)
                .Where(d => d.Id == deficiencyId)
                .SingleOrDefaultAsync();
        }

        public Task<Deficiency?> GetByDeficiencyReviewAsync(DeficiencyReview deficiencyReview)
        {
            return _set
                .Include(d => d.Rule)
                .Include(d => d.SignatureSheet)
                .SingleOrDefaultAsync(d =>
                    d.RecordIdType == deficiencyReview.RecordIdType &&
                    d.RecordId == deficiencyReview.RecordId &&
                    d.RuleId == deficiencyReview.RuleId);
        }

        public async Task<List<Deficiency>> GetAllByMatterAsync(int matterId)
        {
            return await _set
                .Include(x => x.User)
                .Include(x => x.Rule)
                .Include(x => x.SignatureSheet)
                .Where(x => x.MatterId == matterId)
                .ToListAsync();
        }

        public async Task<List<Deficiency>> GetOnlyMatterDeficiencies(int matterId)
        {
            return await _set
                .Include(x => x.User)
                .Include(x => x.Rule)
                .Where(x => x.MatterId == matterId && x.RecordIdType == RecordIdType.Matter)
                .ToListAsync();
        }
        // TODO this method currently does a huge join and is not efficient.
        // Consider optimizing it by only including necessary data.
        public async Task<List<Deficiency>> GetAllByMatterIncludeSheetAsync(int matterId)
        {
            return await _set
                .Include(x => x.User)
                .Include(x => x.Rule)
                .Include(x => x.SignatureSheet)
                .ThenInclude(ss => ss.Rows).ThenInclude(r => r.Cells)
                .Include(x => x.SignatureSheet)
                .ThenInclude(ss => ss.Fields)
                .AsSplitQuery()
                .Where(x => x.MatterId == matterId)
                .ToListAsync();
        }

        public async Task<List<Deficiency>> GetAllRowDeficienciesAsync(int matterId)
        {
            return await _set.Include(x => x.User)
                .Include(x => x.Rule)
                .Include(x => x.SignatureSheet)
                .Include(x => x.SignatureSheet.Rows)
                .ThenInclude(r => r.Cells)
                .Where(x => x.MatterId == matterId && x.RecordIdType == RecordIdType.SignatureSheetRow)
                .ToListAsync();
        }

        public async Task<List<Deficiency>> GetAllCellDeficienciesAsync(int matterId)
        {
            return await _set.Include(x => x.User)
                .Include(x => x.Rule)
                .Include(x => x.SignatureSheet)
                .Include(x => x.SignatureSheet.Rows)
                .ThenInclude(r => r.Cells)
                .Where(x => x.MatterId == matterId && x.RecordIdType == RecordIdType.SignatureSheetCell)
                .ToListAsync();
        }

        public async Task<List<Deficiency>> GetAllSheetFieldDeficienciesAsync(int matterId)
        {
            return await _set
                .Include(x => x.User)
                .Include(x => x.Rule)
                .Include(x => x.SignatureSheet)
                .Where(x => x.MatterId == matterId && x.RecordIdType == RecordIdType.SignatureSheetField)
                .ToListAsync();
        }

        public async Task<List<Deficiency>> GetDeficienciesByRuleAsync(int matterId, int ruleId)
        {
            return await _set.Include(x => x.User)
                .Include(x => x.Rule)
                .Include(x => x.SignatureSheet)
                .ThenInclude(ss => ss.Rows).ThenInclude(r => r.Cells)
                .Where(x => x.MatterId == matterId && x.RuleId == ruleId)
                .ToListAsync();
        }

        public async Task<List<Deficiency>> GetDeficienciesByRecordAsync(int recordId, RecordIdType recordIdType)
        {
            return await _set.Include(x => x.Rule)
                .Where(x => x.RecordIdType == recordIdType && x.RecordId == recordId)
                .ToListAsync();
        }

        public async Task<Deficiency?> GetDeficiencyByRecordAndRuleAsync(int recordId, RecordIdType recordIdType,
            int ruleId)
        {
            return await _set.Include(x => x.Rule)
                .Where(x => x.RecordIdType == recordIdType && x.RecordId == recordId && x.RuleId == ruleId)
                .SingleOrDefaultAsync();
        }

        public async Task DeleteRulesGeneratedDeficienciesAsync(int matterId)
        {
            await _set.Where(x => x.MatterId == matterId && x.UserId == null)
                .ExecuteDeleteAsync();
        }

        public async Task DeleteReviewedNonDeficienciesAsync(int matterId)
        {
            await (
                from d in _set
                join review in _context.DeficiencyReviews
                    on new { d.RuleId, d.RecordId, d.RecordIdType } equals new
                        { review.RuleId, review.RecordId, review.RecordIdType }
                where d.MatterId == matterId && d.UserId == null && review.IsDeficient == false
                select d).ExecuteDeleteAsync();
        }

        public async Task UpdateReviewedDeficienciesAsync(int matterId)
        {
            DateTime utcNow = DateTime.UtcNow;
            await (
                    from d in _set
                    join review in _context.DeficiencyReviews
                        on new { d.RuleId, d.RecordId, d.RecordIdType } equals new
                            { review.RuleId, review.RecordId, review.RecordIdType }
                    where d.MatterId == matterId && d.UserId == null && review.IsDeficient == true
                    select new { Deficiency = d, ReviewedBy = review.User != null ? review.User.Email : "" })
                .ExecuteUpdateAsync(setters => setters
                    .SetProperty(d => d.Deficiency.ReviewedBy, d => d.ReviewedBy)
                    .SetProperty(d => d.Deficiency.ReviewedOn, utcNow)
                    .SetProperty(d => d.Deficiency.IsDeficient, true));
        }

        public async Task<List<DeficiencyAndRows>> GetRowDeficienciesForSheetDeficiencies(int matterId)
        {
            // find all of the rule-based SignatureSheet deficiencies that haven't been reviewed
            var sheetDeficiencies = await _set
                .Where(d => d.MatterId == matterId && d.UserId == null
                                                   && d.RecordIdType == RecordIdType.SignatureSheet
                                                   && d.ReviewedBy == null)
                .ToListAsync();

            // find all of the SignatureSheetRows connected to those deficiencies
            var query = (
                from d in _set
                where d.MatterId == matterId && d.UserId == null
                                             && d.RecordIdType == RecordIdType.SignatureSheet
                join ss in _context.SignatureSheets on d.RecordId equals ss.Id
                join ssr in _context.SignatureSheetRows on ss.Id equals ssr.SignatureSheetId
                where ssr.Validity != Validity.Strikethrough
                select new { SignatureSheetId = ss.Id, SignatureSheetRowId = ssr.Id }
            );
            var rows = await query.ToListAsync();

            var groupedRows = rows
                .GroupBy(r => r.SignatureSheetId)
                .ToDictionary(g => g.Key, g => g.Select(x => x.SignatureSheetRowId).Distinct().ToArray());

            var result = sheetDeficiencies.Select(d => new DeficiencyAndRows
            {
                Deficiency = d,
                SignatureSheetRowIds = groupedRows.TryGetValue(d.RecordId, out var rowsForSheet) ? rowsForSheet : []
            }).ToList();
            return result;
        }

        public async Task<List<Deficiency>> GetRowDeficienciesForSheetDeficiency(int matterId, int sheetId, int ruleId)
        {
            var sheetDeficiencies = await _set
                .Where(d => d.MatterId == matterId && d.UserId == null
                                                   && d.SignatureSheetId == sheetId
                                                   && d.RuleId == ruleId)
                .ToListAsync();
            return sheetDeficiencies;
        }

        public async Task<List<Deficiency>> GetOnlySheetDeficiencies(int sheetId)
        {
            return await _set
                .Include(x => x.User)
                .Include(x => x.Rule)
                .Where(x => x.SignatureSheetId == sheetId && x.RecordIdType == RecordIdType.SignatureSheet)
                .ToListAsync();
        }

        public async Task<int> DeleteDeficiencyByRecordAndTaskAsync(int recordId, RecordIdType recordIdType, int taskId)
        {
            return await _set
                .Where(x => x.RecordIdType == recordIdType && x.RecordId == recordId)
                .Include(x => x.Rule)
                .Where(x => x.Rule.TaskId == taskId)
                .ExecuteDeleteAsync();
        }

        public async Task<int> GetDeficiencyCountByUserAsync(int userId)
        {
            return await _set.Where(d => d.UserId == userId).CountAsync();
        }

        public async Task<Deficiency?> UpdateReviewedStatusAsync(int deficiencyId, User user, bool isDeficient,
            string? note)
        {
            var deficiency = await _set.SingleOrDefaultAsync(d => d.Id == deficiencyId);
            if (deficiency == null)
            {
                return null;
            }

            deficiency.Note = note;
            deficiency.IsDeficient = isDeficient;
            deficiency.ReviewedBy = user.Email;
            deficiency.ReviewedOn = DateTime.UtcNow;
            SetModified(deficiency);
            return deficiency;
        }
    }
}