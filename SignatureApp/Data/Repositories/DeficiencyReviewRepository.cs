﻿using Microsoft.EntityFrameworkCore;
using DataInterface.RepositoryInterfaces;
using Model.Deficiencies;

namespace Data.Repositories
{
    public class DeficiencyReviewRepository : EfCoreBaseRepository<DeficiencyReview>, IDeficiencyReviewRepository
    {
        public DeficiencyReviewRepository(SignatureAppDbContext context) : base(context)
        {
        }

        public async Task<int[]> GetAllNonReviewedIdsByMatterAsync(int matterId)
        {
            return await _set
                .Include(dr => dr.Rule)
                .Where(dr => dr.MatterId == matterId &&
                             (dr.AssignedTo == null || dr.ReviewedDate == null))
                .Select(dr => dr.Id)
                .OrderBy(drId => drId)
                .ToArrayAsync();
        }
        public async Task<DeficiencyReview?> GetByDeficiencyAsync(Deficiency deficiency)
        {
            return await _set
                .Include(dr => dr.<PERSON>)
                .Include(dr => dr.User)
                .Include(dr => dr.SignatureSheet)
                .FirstOrDefaultAsync(dr =>
                    dr.RecordId == deficiency.RecordId &&
                    dr.RecordIdType == deficiency.RecordIdType &&
                    dr.RuleId == deficiency.RuleId);
        }

        public async Task<DeficiencyReview?> GetNextByMatterIdAsync(int matterId, string userEmail)
        {
            // get the next deficiency review in the matter ordered by Id, either not assigned or assigned to the user
            return await _set
                .Include(dr => dr.Rule)
                .Where(dr => dr.MatterId == matterId &&
                             (dr.AssignedTo == null || (dr.AssignedTo == userEmail && dr.ReviewedDate == null)))
                .OrderBy(dr => dr.Id)
                .FirstOrDefaultAsync();
        }

        public async Task AssignDeficiencyReviewAsync(DeficiencyReview deficiencyReview, string userEmail)
        {
            var userId = await _context.Users
                .Where(u => u.Email == userEmail).Select(u => u.Id).FirstOrDefaultAsync();
            deficiencyReview.UserId = userId;
            deficiencyReview.AssignedTo = userEmail;
            deficiencyReview.AssignmentDate = DateTime.UtcNow;
        }

        public async Task UpdateReviewedStatusAsync(Deficiency deficiency, bool isDeficient, string? note)
        {
            var deficiencyReview = await GetByDeficiencyAsync(deficiency);
            if (deficiencyReview != null)
            {
                deficiencyReview.Note = note;
                deficiencyReview.IsDeficient = isDeficient;
                deficiencyReview.ReviewedDate = DateTime.UtcNow;
                _context.Entry(deficiencyReview).State = EntityState.Modified;
            }
        }

    }
}