using DataInterface.RepositoryInterfaces;
using Microsoft.EntityFrameworkCore;
using Model.Boundaries;

namespace Data.Repositories;

public class BoundaryRepository : EfCoreBaseRepository<Boundary>, IBoundaryRepository
{
    public BoundaryRepository(SignatureAppDbContext context) : base(context)
    {
    }

    public async Task<bool> DoesStateBoundaryNameExistAsync(int usStateId, string boundaryName)
    {
        var boundary = await _set
            .Where(b => EF.Functions.Like(b.Name, boundaryName)) // case-insensitive
            .FirstOrDefaultAsync();
        return boundary != null;
    }

    public async Task<Boundary?> GetByStateAndNameAsync(int usStateId, string boundaryName)
    {
        return await _set.Include(b => b.BoundaryPoints)
            .Where(b => EF.Functions.Like(b.Name, boundaryName)) // case-insensitive
            .FirstOrDefaultAsync();
    }

    public async Task<Boundary?> GetByStateTypeAndNameAsync(int usStateId, BoundaryType boundaryType, string boundaryName)
    {
        return await _set.Include(b => b.BoundaryPoints)
            .Where(b => b.UsStateId == usStateId && b.Type == boundaryType && b.Name == boundaryName)
            .SingleOrDefaultAsync();
    }

    public async Task<List<Boundary>> GetAllByStateAndTypeAsync(int usStateId, BoundaryType boundaryType)
    {
        return await _set.Where(b => b.UsStateId == usStateId && b.Type == boundaryType)
            .OrderBy(b => b.Name)
            .ToListAsync();
    }
}