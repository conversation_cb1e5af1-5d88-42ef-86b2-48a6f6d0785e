﻿using DataInterface.RepositoryInterfaces;
using Microsoft.EntityFrameworkCore;
using Model.ExternalDataSources;

namespace Data.Repositories;

public class RegisteredCirculatorRepository : EfCoreBaseRepository<RegisteredCirculator>, IRegisteredCirculatorRepository
{
    public RegisteredCirculatorRepository(SignatureAppDbContext context) : base(context)
    {
    }

    public override async Task<List<RegisteredCirculator>> GetAllAsync()
    {
        return await _set.Include(c => c.ExternalDataSource)
            .OrderBy(c => c.Id).ToListAsync();
    }

    public async Task BulkInsertAsync(List<RegisteredCirculator> records)
    {
        await _context.BulkInsertAsync(records, p => p.PropagateValues(false));
    }

    public async Task<int> GetCountByMatterIdAsync(int matterId)
    {
        return await _set
            .Include(x => x.ExternalDataSource)
            .ThenInclude(eds => eds.ExternalDataSourceMatters)
            .Where(x => x.ExternalDataSource.ExternalDataSourceMatters.Any(edsm => edsm.MatterId == matterId))
            .CountAsync();
    }

    public async Task<DocumentUploadInfo> GetUploadInfoByMatterIdAsync(int matterId)
    {
        int count = await _set
            .Include(x => x.ExternalDataSource)
            .ThenInclude(eds => eds.ExternalDataSourceMatters)
            .Where(x => x.ExternalDataSource.ExternalDataSourceMatters.Any(edsm => edsm.MatterId == matterId))
            .CountAsync();

        var lastUploadedDataSource = await _set
            .Include(x => x.ExternalDataSource)
            .ThenInclude(eds => eds.ExternalDataSourceMatters)
            .Where(x => x.ExternalDataSource.ExternalDataSourceMatters.Any(edsm => edsm.MatterId == matterId))
            .Select(x => x.ExternalDataSource)
            .OrderByDescending(x => x.UploadedOn)
            .FirstOrDefaultAsync();

        return new DocumentUploadInfo
        {
            TotalValidCount = count,
            LastUploadId = lastUploadedDataSource?.Id,
            LastUploadedBy = lastUploadedDataSource?.UploadedBy,
            LastUploadedOn = lastUploadedDataSource?.UploadedOn,
        };
    }
    public async Task<List<RegisteredCirculator>> SearchByCriteriaAsync(int matterId, RegisteredCirculator circulator)
    {
        IQueryable<RegisteredCirculator> query = _set.Where(x => x.ExternalDataSource.ExternalDataSourceMatters.Any(edsm => edsm.MatterId == matterId));
        if (!string.IsNullOrEmpty(circulator.CirculatorName))
        {
            query = query.Where(x => x.CirculatorName != null);
            var parts = circulator.CirculatorName.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            foreach (var part in parts)
            {
                query = query.Where(x => x.CirculatorName!.Contains(part));
            }
        }

        if (!string.IsNullOrEmpty(circulator.CirculatorId))
        {
            query = query.Where(x => x.CirculatorId == circulator.CirculatorId);
        }

        if (!string.IsNullOrEmpty(circulator.CandidateOrBallotMeasure))
        {
            query = query.Where(x => x.CandidateOrBallotMeasure != null && x.CandidateOrBallotMeasure.Contains(circulator.CandidateOrBallotMeasure));
        }

        return await query.ToListAsync();
    }
}
