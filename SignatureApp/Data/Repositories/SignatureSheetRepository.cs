﻿using DataInterface;
using DataInterface.RepositoryInterfaces;
using Microsoft.EntityFrameworkCore;
using Model.ExternalDataSources;
using Model.SignatureSheets;

namespace Data.Repositories;

public class SignatureSheetRepository : AuditedSaveBaseRepository<SignatureSheet>, ISignatureSheetRepository
{
    public SignatureSheetRepository(
        SignatureAppDbContext context,
        IUserContext userContext)
        : base(context, userContext)
    {
    }

    public async Task<int> GetCountByMatterIdAsync(int matterId)
    {
        return await _set.Where(x => x.MatterId == matterId).CountAsync();
    }

    public async Task<(int min, int max)> GetMinMaxByMatterAsync(int matterId)
    {
        int min = await _set.Where(x => x.MatterId == matterId).MinAsync(ss => ss.SheetNumber);
        int max = await _set.Where(x => x.MatterId == matterId).MaxAsync(ss => ss.SheetNumber);
        return (min, max);
    }

    public async Task<int[]> GetSheetNumbersByMatterAsync(int matterId)
    {
        return await _set.Where(x => x.MatterId == matterId)
            .Select(ss => ss.SheetNumber)
            .OrderBy(num => num)
            .ToArrayAsync();
    }

    public async Task<List<SignatureSheet>> GetAllFieldReviewedAsync()
    {
        return await _set
            .Include(x => x.SignatureSheetUpload)
            .Include(x => x.Fields)
            .Where(ss => ss.AreFieldsReviewed)
            .ToListAsync();
    }

    public override async Task<List<SignatureSheet>> GetAllAsync()
    {
        var all = await _set.Include(x => x.Pages)
            .Include(x => x.Rows)
            .Include(x => x.Fields)
            .AsSplitQuery()
            .ToListAsync();
        return all;
    }

    public async Task<SignatureSheet?> GetByMatterAndSheetNumberAsync(int matterId, int sheetNumber)
    {
        var sheet = await _set
            .Include(x => x.Pages)
            .Include(x => x.Rows)
            .AsSplitQuery()
            .OrderBy(x=> x.Id)
            .LastOrDefaultAsync(x => x.MatterId == matterId && x.SheetNumber == sheetNumber);
        return sheet;
    }

    public async Task<DocumentUploadInfo> GetUploadInfoByMatterIdAsync(int matterId)
    {
        int sheetCount = await GetCountByMatterIdAsync(matterId);

        var lastUploaded = await _context.SignatureSheetUploads
            .Where(x => x.MatterId == matterId)
            .OrderByDescending(x => x.UploadedOn)
            .FirstOrDefaultAsync();

        var uploadCount = await _context.SignatureSheetUploads
            .Where(x => x.MatterId == matterId)
            .CountAsync();

        return new DocumentUploadInfo
        {
            TotalValidCount = sheetCount,
            LastUploadId = lastUploaded?.Id,
            LastUploadedBy = lastUploaded?.UploadedBy,
            LastUploadedOn = lastUploaded?.UploadedOn,
            UploadCount = uploadCount,
        };
    }

    public async Task<List<SignatureSheet>> GetAllByMatterIdAsync(int matterId)
    {
        return await _set
            .Include(x => x.Circulator)
            .Include(x => x.Template)
            .Include(x => x.Pages)
            .Include(x => x.Rows)
            .AsSplitQuery()
            .Where(x => x.MatterId == matterId)
            .ToListAsync();
    }

    public async Task<List<SignatureSheet>> GetByMatterTemplateAndFilenameAsync(int matterId, int templateId,
        string filename)
    {
        return await _set
            .Where(x => x.FileName == filename && x.MatterId == matterId && x.TemplateId == templateId)
            .Include(x => x.Fields)
            .Include(x => x.Rows)
            .ThenInclude(x => x.Cells)
            .AsSplitQuery()
            .ToListAsync();
    }

    public async Task<SignatureSheet?> GetByIdWithRowsAsync(int signatureSheetId)
    {
        return await _set.Include(x => x.Rows).FirstOrDefaultAsync(x => x.Id == signatureSheetId);
    }

    public async Task<List<SignatureSheet>> GetBySignatureSheetUploadIdAsync(int signatureSheetUploadId)
    {
        return await _set
            .Where(x => x.SignatureSheetUploadId == signatureSheetUploadId)
            .Include(x => x.Fields)
            .Include(x => x.Rows)
            .ThenInclude(x => x.Cells)
            .AsSplitQuery()
            .ToListAsync();
    }
}