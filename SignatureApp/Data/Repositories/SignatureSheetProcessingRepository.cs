using DataInterface.RepositoryInterfaces;
using Microsoft.EntityFrameworkCore;
using Model.SignatureSheets;

namespace Data.Repositories;

public class SignatureSheetProcessingRepository : EfCoreBaseRepository<SignatureSheetProcessing>,
    ISignatureSheetProcessingRepository
{
    public SignatureSheetProcessingRepository(SignatureAppDbContext context) : base(context)
    {
    }

    public async Task<bool> TryAdd(int matterId, int uploadId, int sheetNumber)
    {
        var existingRecord = _set
            .FirstOrDefault(x => x.MatterId == matterId && x.SheetNumber == sheetNumber);

        if (existingRecord != null)
        {
            return false;
        }

        _set.Add(
            new SignatureSheetProcessing
            {
                MatterId = matterId,
                SignatureSheetUploadId = uploadId,
                SheetNumber = sheetNumber,
            });

        await _context.SaveChangesAsync();
        return true;
    }
}