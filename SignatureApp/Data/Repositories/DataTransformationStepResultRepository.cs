﻿using DataInterface.RepositoryInterfaces;
using Microsoft.EntityFrameworkCore;
using Model.DataTransformation;
using Model.SignatureSheets;

namespace Data.Repositories;

public class DataTransformationStepResultRepository : EfCoreBaseRepository<DataTransformationStepResult>, IDataTransformationStepResultRepository
{
    public DataTransformationStepResultRepository(SignatureAppDbContext context) : base(context)
    {
    }

    public async Task<List<DataTransformationStepResult>> GetByUploadIdAsync(int uploadId)
    {
        return await _set.Where(dtsr => dtsr.SignatureSheetUploadId == uploadId).ToListAsync();
    }

    public async Task<DataTransformationStepResult?> GetByUploadIdAndResultFilenameAsync(int uploadId, string newFilePath)
    {
        return await _set.Where(dtsr => dtsr.SignatureSheetUploadId == uploadId
            && dtsr.Output == newFilePath).SingleOrDefaultAsync();
    }

    public async Task<List<InvalidSheet>> GetInvalidSheetsByMatterIdAsync(int matterId)
    {
        var uploads = await _context.SignatureSheetUploads
            .Where(x => x.MatterId == matterId)
            .ToDictionaryAsync(u => u.Id, u => u);

        var uploadIds = uploads.Keys.ToList();
        // get the sheets that have a result code of 0
        var query = _set
            .Where(x => uploadIds.Contains(x.SignatureSheetUploadId)
                        && x.FunctionStepName == "DocumentIntelligenceSubmitterFunction"
                        && x.ResultStatusCode == 0)
            .Select(x => new InvalidSheet
            {
                SignatureSheetUpload = uploads[x.SignatureSheetUploadId],
                MatterId = x.MatterId,
                TemplateId = x.TemplateId,
                Filename = x.Output
            });
        return await query.ToListAsync();

    }

    public async Task<List<DataTransformationStepResult>> GetByMatterIdAsync(int matterId)
    {
        return await _set.Where(dtss => dtss.MatterId == matterId).ToListAsync();
    }
}

