﻿using DataInterface.RepositoryInterfaces;
using Microsoft.EntityFrameworkCore;
using Model.ExternalDataSources;
using System.Diagnostics;

namespace Data.Repositories;

public class RegisteredVoterRepository : EfCoreBaseRepository<RegisteredVoter>, IRegisteredVoterRepository
{

    public RegisteredVoterRepository(SignatureAppDbContext context) : base(context)
    {
    }

    public async Task<List<RegisteredVoter>> GetChunkFromDbOrderedByVoterId(int chunkSize, int maxVoterIdInChunkFromDb, int minVoterIdInChunkToDb, int maxVoterIdInChunkToDb)
    {
        var sw = Stopwatch.StartNew();
        int minVoterId = Math.Max(maxVoterIdInChunkFromDb, minVoterIdInChunkToDb);
        var list = await _set
            .Where(rv => rv.VoterId >= minVoterId && rv.VoterId <= maxVoterIdInChunkToDb)
            .OrderBy(rv => rv.VoterId)
            .Take(chunkSize)
            .AsNoTracking()
            .ToListAsync();
        sw.Stop();
        Debug.WriteLine($"Got {list.Count} records in [{minVoterId}, {maxVoterIdInChunkToDb}], took {sw.Elapsed}");
        return list;
    }

    public async Task<int> GetCountByMatterIdAsync(int matterId)
    {
        return await _set
            .Include(x => x.ExternalDataSource)
            .ThenInclude(eds => eds.ExternalDataSourceMatters)
            .Where(x => x.ExternalDataSource.ExternalDataSourceMatters.Any(edsm => edsm.MatterId == matterId))
            .CountAsync();
    }

    public async Task<int[]> GetUploadsByMatterIdAsync(int matterId)
    {
        var results = await _context.ExternalDataSourceMatter
            .Include(x => x.ExternalDataSource)
            .Where(x => x.MatterId == matterId)
            .Where(x => x.ExternalDataSource.County != null)
            .Select(x => x.ExternalDataSourceId)
            .ToArrayAsync();
        return results;
    }

    public async Task<List<DocumentUploadInfoByCounty>> GetUploadInfoByMatterIdAsync(int matterId)
    {
        var results = await _context.ExternalDataSourceMatter
            .Include(x => x.ExternalDataSource)
            .Where(x => x.MatterId == matterId)
            .Where(x => x.ExternalDataSource.County != null)
            .GroupBy(x => new
            {
                x.ExternalDataSource.County,
                x.ExternalDataSource.UploadedBy,
                x.ExternalDataSource.UploadedOn,
            })
            .Select(g => new DocumentUploadInfoByCounty
            {
                County = g.Key.County!,
                TotalValidCount = g.Count(),
                LastUploadedBy = g.Key.UploadedBy,
                LastUploadedOn = g.Key.UploadedOn,
            }).ToListAsync();
        return results;
    }

    public async Task<(List<RegisteredVoter> Results, int TotalHits)> SearchByCriteriaAsync(int[] externalDbIds, RegisteredVoter voter, int maxTake = 10)
    {
        IQueryable<RegisteredVoter> query = _set.Include(x => x.UsState)
            .Where(x => externalDbIds.Contains(x.ExternalDataSourceId));
        if (!string.IsNullOrEmpty(voter.FirstName))
        {
            var isExactMatch = voter.FirstName.EndsWith(' ');
            var firstName = voter.FirstName.Trim();
            if (firstName.Contains(' '))
            {
                var parts = firstName.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                parts = parts.Where(p => p.Length > 1 && (p.Length != 2 || p[1] != '.')).ToArray();
                if (parts.Length == 1)
                {
                    firstName = parts[0];
                }
            }
            if (isExactMatch)
            {
                query = query.Where(x => x.FirstName == firstName);
            }
            else
            {
                query = query.Where(x => x.FirstName.StartsWith(firstName));
            }
        }
        if (!string.IsNullOrEmpty(voter.LastName))
        {
            var isExactMatch = voter.LastName.EndsWith(' ');
            var lastName = voter.LastName.Trim();
            if (isExactMatch)
            {
                query = query.Where(x => x.LastName == lastName);
            }
            else
            {
                query = query.Where(x => x.LastName.StartsWith(voter.LastName));
            }
        }
        if (!string.IsNullOrEmpty(voter.StreetAddress))
        {
            query = query.Where(x => x.StreetAddress != null && x.StreetAddress.StartsWith(voter.StreetAddress));
        }
        if (!string.IsNullOrEmpty(voter.StreetNumber))
        {
            query = query.Where(x => x.StreetNumber != null && x.StreetNumber == voter.StreetNumber);
        }
        if (voter.Direction != null)
        {
            query = query.Where(x => x.Direction != null && x.Direction == voter.Direction);
        }
        if (!string.IsNullOrEmpty(voter.StreetName))
        {
            query = query.Where(x => x.StreetName != null && x.StreetName.StartsWith(voter.StreetName));
        }
        if (!string.IsNullOrEmpty(voter.StreetType))
        {
            query = query.Where(x => x.StreetType != null && x.StreetType == voter.StreetType);
        }
        if (!string.IsNullOrEmpty(voter.City))
        {
            query = query.Where(x => x.City != null && x.City.StartsWith(voter.City));
        }
        if (voter.UsStateId > 0)
        {
            query = query.Where(x => x.UsStateId == voter.UsStateId);
        }
        var totalCount = await query.CountAsync();
        var records = await query.OrderBy(x => x.FirstName)
            .ThenBy(x => x.LastName)
            .Take(maxTake).ToListAsync();
        return (records, totalCount);
    }

    public void UpdateRange(List<RegisteredVoter> listToUpdate)
    {
        _context.UpdateRange(listToUpdate);
    }

    public override async Task BulkInsertAsync(IEnumerable<RegisteredVoter> t)
    {
        await _context.BulkInsertAsync(t, p => p.PropagateValues(false));
    }
}
