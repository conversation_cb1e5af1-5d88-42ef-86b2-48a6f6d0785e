﻿using DataInterface.RepositoryInterfaces;
using Microsoft.EntityFrameworkCore;
using Model.ExternalDataSources;
using Model.Matters;
using Model.Rules;
using Model.SignatureSheets;

namespace Data.Repositories
{
    public class SignatureSheetRowRepository : EfCoreBaseRepository<SignatureSheetRow>, ISignatureSheetRowRepository
    {
        public SignatureSheetRowRepository(SignatureAppDbContext context) : base(context)
        {
        }

        public override async Task<SignatureSheetRow?> GetByIdAsync(int signatureSheetRowId)
        {
            return await _set
                .Include(x => x.Signatory)
                .Include(x => x.SignatureSheet)
                .SingleOrDefaultAsync(x => x.Id == signatureSheetRowId);
        }

        public async Task<List<SignatureSheetRow>> GetAllValidByMatterIdAsync(int matterId)
        {
            return await _set
                .Include(ssr => ssr.SignatureSheet)
                .Include(ssr => ssr.RegisteredVoter)
                .Where(ssr => ssr.SignatureSheet.MatterId == matterId && ssr.Validity == Validity.Valid)
                .OrderBy(ssr => ssr.SignatureSheet.SheetNumber).ThenBy(ssr => ssr.RowNumber)
                .ToListAsync();
        }

        public async Task<List<SignatureSheetRow>> GetAllByTableIdAsync(int signatureSheetTableId)
        {
            return await _set
                .Include(sr => sr.SignatureSheet)
                .Where(sr => sr.SignatureSheetTableId == signatureSheetTableId)
                .ToListAsync();

        }

        public Task<List<SignatureSheetRow>> GetAllReviewedWithoutSignatoryAsync()
        {
            return _set
                .Include(x => x.SignatureSheet)
                .Include(x => x.Cells)
                .AsSplitQuery()
                .Where(sr => sr.SignatureSheet.IsTableReviewed == true
                    && sr.SignatoryId == null
                    && sr.Validity != Validity.Strikethrough)
                .ToListAsync();
        }

        public Task<List<SignatureSheetRow>> GetAllReviewedAsync()
        {
            return _set
                .Include(x => x.SignatureSheet)
                .Include(x => x.Cells)
                .AsSplitQuery()
                .Where(sr => sr.SignatureSheet.IsTableReviewed == true
                             && sr.Validity != Validity.Strikethrough)
                .ToListAsync();
        }

        public async Task<Signatory?> GetSignatoryByRowIdAsync(int signatureSheetRowId)
        {
            var row = await _set
                .Include(r => r.Signatory)
                .Where(r => r.Id == signatureSheetRowId)
                .SingleOrDefaultAsync();
            return row?.Signatory;
        }

        public async Task<List<SignatureSheetRow>> GetRowsAndCellsBySignatureSheetIdAsync(int signatureSheetId)
        {
            return await _set
                .Include(sr => sr.Signatory)
                .Include(sr => sr.RegisteredVoter)
                .Include(sr => sr.Cells)
                .ThenInclude(c => c.TemplateSignatureColumn)
                .AsSplitQuery()
                .Where(sr => sr.SignatureSheetId == signatureSheetId)
                .ToListAsync();
        }

        public async Task<SignatureSheetRow?> GetByIdIncludingSheetAndCellsAsync(int id)
        {
            return await _set
                .Include(x => x.TemplateSignatureTable)
                .Include(x => x.Cells)
                .Include(x => x.SignatureSheet).ThenInclude(ss => ss.Pages)
                .AsSplitQuery()
                .FirstOrDefaultAsync(x => x.Id == id);
        }

        public async Task<List<SignatureSheetRow>> GetByCellIdsWithSignatureTableColumnAsync(int[] cellIds)
        {
            var rows = await (
                from row in _set.Include(sr => sr.Cells).ThenInclude(c => c.TemplateSignatureColumn)
                from cell in row.Cells
                where cellIds.Contains(cell.Id)
                select row).ToListAsync();
            return rows.Distinct().ToList();
        }

        public class RowInfo
        {
            public int SignatureSheetId { get; set; }
            public int RowNumber { get; set; }
        }


        public async Task<List<ValidityCount>> GetRowsGroupedByValidityForMatterAsync(int matterId)
        {
            // ultimately we need to return a validity count
            var groupedRows = await _set
                .Where(x => x.SignatureSheet.MatterId == matterId)
                .Where(x => !x.IsMissing)
                .GroupBy(x => x.Validity)
                .Select(g => new { Validity = g.Key, Rows = g.Select(r => new RowInfo { SignatureSheetId = r.SignatureSheetId, RowNumber = r.RowNumber }).ToList() })
                .ToListAsync();
            // now get the signature sheets Id that are marked with deficiencies of type SignatureSheet
            var deficientSheets = await _context.Deficiencies
                .Where(d => d.MatterId == matterId && d.RecordIdType == RecordIdType.SignatureSheet)
                .Select(d => d.SignatureSheetId)
                .ToListAsync();
            var validRows = groupedRows.SingleOrDefault(v => v.Validity == Validity.Valid)?.Rows;
            var invalidRows = groupedRows.SingleOrDefault(v => v.Validity == Validity.Invalid)?.Rows;
            // There might not be any invalidRows, yet
            if (invalidRows == null)
            {
                invalidRows = new List<RowInfo>();
                var invalidGroup = new { Validity = Validity.Invalid, Rows = invalidRows };
                groupedRows.Add(invalidGroup);
            }
            // Now mark all of the rows that were valid that have signatureSheetIds in the list of deficientSheets as Invalid
            var validRowsLocal = (validRows ?? []).ToList();
            foreach (var row in validRowsLocal)
            {
                if (deficientSheets.Contains(row.SignatureSheetId))
                {
                    validRows?.Remove(row);
                    invalidRows?.Add(row);
                }
            }
            return groupedRows.Select(g => new ValidityCount { Validity = g.Validity, Count = g.Rows.Count }).ToList();
        }

        public async Task<List<List<SignatureSheetRow>>> GetDuplicateRegisteredVotersByMatterIdAsync(int matterId)
        {
            var rows = GetQueryRowsWithIncludes();
            var filteredRows = rows.Where(x => x.SignatureSheet.MatterId == matterId);
            var duplicates = await GetDuplicateRegisteredVoters(filteredRows);
            return duplicates;
        }

        public Task<List<List<SignatureSheetRow>>> GetTextualDuplicatesByMatterIdAsync(int matterId)
        {
            var rows = GetQueryRowsWithIncludes();
            var filteredRows = rows.Where(x => x.SignatureSheet.MatterId == matterId);
            var duplicates = GetTextualDuplicateVoters(filteredRows);
            return duplicates;
        }

        public async Task<List<List<SignatureSheetRow>>> GetDuplicateRegisteredVotersByOfficeAsync(string office)
        {
            var rows = GetQueryRowsWithIncludes();
            // TODO: needs to be filtered by office
            var filteredRows = rows;
            // var filteredRows = rows.Where(x => x.SignatureSheet.Office == office);
            var duplicates = await GetDuplicateRegisteredVoters(filteredRows);
            return duplicates;
        }

        private IQueryable<SignatureSheetRow> GetQueryRowsWithIncludes()
        {
            var rows = _set
                .Include(x => x.Signatory)
                .Include(x => x.SignatureSheet)
                .Include(x => x.TemplateSignatureTable.Columns)
                .Include(x => x.Cells)
                .AsSplitQuery();
            return rows;
        }

        private async Task<List<List<SignatureSheetRow>>> GetDuplicateRegisteredVoters(
            IQueryable<SignatureSheetRow> rowsQuery)
        {
            var query =
                from row in rowsQuery
                where row.RegisteredVoter != null && row.IsReviewed == true && row.Validity != Validity.Strikethrough
                group row by row.RegisteredVoterId
                into g
                where g.Count() > 1
                select g.ToList();
            return await query.ToListAsync();
        }

        private async Task<List<List<SignatureSheetRow>>> GetTextualDuplicateVoters(
            IQueryable<SignatureSheetRow> rowsQuery)
        {
            var query =
                from row in rowsQuery
                // An expression tree lambda may not contain a null propagating operator.
                where row != null && row.Signatory != null && row.Signatory.FirstName != null
                    && row.IsReviewed == true && row.Validity != Validity.Strikethrough
                // let firstNames = _context.SplitString(row.Signatory!.FirstName!, " ")
                //     .OrderByDescending(result => result.Value.Length).First()
                group row by new { FirstInitial = row.Signatory!.FirstName.FirstOrDefault(), row.Signatory!.LastName }
                into g
                where g.Count() > 1
                select g.ToList();
            return await query.ToListAsync();
        }
    }
}
