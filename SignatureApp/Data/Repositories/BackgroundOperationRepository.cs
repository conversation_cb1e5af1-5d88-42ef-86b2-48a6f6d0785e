﻿using DataInterface;
using DataInterface.RepositoryInterfaces;
using Microsoft.EntityFrameworkCore;
using Model.Rules;

namespace Data.Repositories;

public class BackgroundOperationRepository : AuditedSaveBaseRepository<BackgroundOperation>,
    IBackgroundOperationRepository
{
    public BackgroundOperationRepository(SignatureAppDbContext dbContext,
        IUserContext userContext) : base(dbContext, userContext)
    {
    }

    public async Task<bool> DeleteByIdAsync(int runRulesId)
    {
        var backgroundOperation = await GetByIdAsync(runRulesId);
        if (backgroundOperation != null)
        {
            _set.Remove(backgroundOperation);
            await _context.SaveChangesAsync();
        }

        return backgroundOperation != null;
    }

    public async Task<BackgroundOperation?> GetRunningJobIdAsync(int matterId, BackgroundOperationType operationType)
    {
        if (matterId <= 0)
        {
            throw new ArgumentException("Matter ID must be greater than zero.", nameof(matterId));
        }

        return await _set.Where(bo => bo.MatterId == matterId &&
                                      bo.OperationType == operationType &&
                                      bo.ExecutionStatus == ExecutionStatus.Running).FirstOrDefaultAsync();
    }

    public async Task<BackgroundOperation?> GetTimeSpanSinceOperationCompletedAsync(int matterId,
        BackgroundOperationType runRules)
    {
        var lastRun = await _set
            .Where(bo => bo.MatterId == matterId
                         && bo.OperationType == runRules
                         && bo.ExecutionStatus >= ExecutionStatus.Running)
            .OrderByDescending(bo => bo.StartedOn)
            .FirstOrDefaultAsync();
        return lastRun;
    }

    public async Task TerminateRunningJobsAsync(int matterId)
    {
        var runningJobs = _set
            .Where(bo => bo.MatterId == matterId && bo.ExecutionStatus == ExecutionStatus.Running);
        if (runningJobs.Any())
        {
            foreach (var job in runningJobs)
            {
                job.ExecutionStatus = ExecutionStatus.Failed;
                job.EndedOn = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();
        }
    }
}