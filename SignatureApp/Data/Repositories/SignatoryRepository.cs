using DataInterface.RepositoryInterfaces;
using Microsoft.EntityFrameworkCore;
using Model.ExternalDataSources;

namespace Data.Repositories;

public class SignatoryRepository: EfCoreBaseRepository<Signatory>, ISignatoryRepository
{
    public SignatoryRepository(SignatureAppDbContext context) : base(context)
    {
    }

    public async Task<Signatory?> GetByRowIdAsync(int signatureSheetRowId)
    {
        var row = await _context.SignatureSheetRows
            .Include(r => r.Signatory)
            .Where(r => r.Id == signatureSheetRowId)
            .SingleOrDefaultAsync();
        return row?.Signatory;
    }

    public Task<List<Signatory>> GetAllNonGeocodedSignatories()
    {
        return _set
            .Where(s => (s.Latitude == null || s.Longitude == null) && s.GeocodeErrors == null)
            .ToListAsync();
    }
}