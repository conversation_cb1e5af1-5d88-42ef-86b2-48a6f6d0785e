﻿using DataInterface;
using DataInterface.RepositoryInterfaces;
using Microsoft.EntityFrameworkCore;
using Model.DataTransformation;
using Model.SignatureSheets;

namespace Data.Repositories;

public class SignatureSheetUploadRepository : AuditedSaveBaseRepository<SignatureSheetUpload>, ISignatureSheetUploadRepository
{
    public SignatureSheetUploadRepository(SignatureAppDbContext context, IUserContext userContext) : base(context, userContext)
    {
    }


    public async Task<List<SignatureSheetUpload>> GetAllByMatterIdAsync(int matterId)
    {
        var uploads = await _set.Where(u => u.MatterId == matterId).ToListAsync();
        return uploads;
    }

    public async Task DeleteUploadByIdAsync(int uploadId)
    {
        await _context.Works.Where(w => w.SignatureSheet.SignatureSheetUploadId == uploadId).ExecuteDeleteAsync();
        await _context.DataProcessingLogs.Where(x => x.SignatureSheetUploadId == uploadId).ExecuteDeleteAsync();
        await _context.SignatureSheetCells.Where(x => x.SignatureSheetRow.SignatureSheet.SignatureSheetUploadId == uploadId).ExecuteDeleteAsync();
        await _context.SignatureSheetRows.Where(x => x.SignatureSheet.SignatureSheetUploadId == uploadId).ExecuteDeleteAsync();
        await _context.SignatureSheetFormLines.Where(x => x.SignatureSheetPage != null
            && x.SignatureSheetPage.SignatureSheet != null
            && x.SignatureSheetPage.SignatureSheet.SignatureSheetUploadId == uploadId)
            .ExecuteDeleteAsync();
        await _context.SignatureSheetFields.Where(x => x.SignatureSheet.SignatureSheetUploadId == uploadId).ExecuteDeleteAsync();
        await _context.SignatureSheetPages.Where(x => x.SignatureSheet != null && x.SignatureSheet.SignatureSheetUploadId == uploadId)
            .ExecuteDeleteAsync();
        await _context.SignatureSheetColumns.Where(x => x.SignatureSheetTable.SignatureSheet.SignatureSheetUploadId == uploadId)
            .ExecuteDeleteAsync();
        await _context.SignatureSheetTables.Where(x => x.SignatureSheet.SignatureSheetUploadId == uploadId).ExecuteDeleteAsync();
        await _context.SignatureSheetProcessings.Where(x => x.SignatureSheetUploadId == uploadId).ExecuteDeleteAsync();
        await _context.SignatureSheets.Where(x => x.SignatureSheetUploadId == uploadId).ExecuteDeleteAsync();
        await _set.Where(x => x.Id == uploadId).ExecuteDeleteAsync();
    }

    public async Task<bool> CheckUrlAlreadyDownloadedAsync(int matterId, string downloadUrl)
    {
        var results = await (
                from ds in _context.DataTransformationStepStart
                join dr in _context.DataTransformationStepResult
                on ds.SignatureSheetUploadId equals dr.SignatureSheetUploadId
                where ds.Input == downloadUrl
                && ds.MatterId == matterId
                && ds.FunctionStepName == "DownloadFilesToAzureFunction"
                && dr.FunctionStepName == "DocumentIntelligenceSubmitterFunction"
                && dr.ResultStatusCode == (int)DocumentIntelligenceResultStatus.Valid
                select ds
            ).ToListAsync();

        return results.Any();
    }

    public async Task<bool> IsValidAgainstExistingTemplate(int matterId, int newTemplateId)
    {
        // Get the most recent TemplateId for the matter
        var existingTemplateId = await _set
            .Where(x => x.MatterId == matterId)
            .OrderByDescending(x => x.UploadedOn)
            .Select(x => x.TemplateId)
            .FirstOrDefaultAsync();

        if (existingTemplateId == 0)
            return true;

        var templates = await _context.Templates
            .Where(t => t.Id == existingTemplateId || t.Id == newTemplateId)
            .ToListAsync();

        var currentTemplate = templates.FirstOrDefault(t => t.Id == existingTemplateId);
        var newTemplate = templates.FirstOrDefault(t => t.Id == newTemplateId);

        if (currentTemplate == null || newTemplate == null)
            return true;

        return currentTemplate.PageSize == newTemplate.PageSize &&
               currentTemplate.UsStateId == newTemplate.UsStateId;
    }

}

