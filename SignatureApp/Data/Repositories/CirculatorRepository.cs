using DataInterface.RepositoryInterfaces;
using Microsoft.EntityFrameworkCore;
using Model.ExternalDataSources;

namespace Data.Repositories;

public class CirculatorRepository : EfCoreBaseRepository<Circulator>, ICirculatorRepository
{
    public CirculatorRepository(SignatureAppDbContext context) : base(context)
    {
    }

    public async Task<Circulator?> GetByCirculatorAndMatterId(Circulator circulator, int matterId)
    {
        return await _set
            .Include(c => c.SignatureSheets)
            .FirstOrDefaultAsync(c => c.Name == circulator.Name
                  && c.Address == circulator.Address
                  && c.City == circulator.City
                  && c.UsStateId == circulator.UsStateId
                  && c.PostalCode == circulator.PostalCode
                  && c.SignatureSheets.Any(ss => ss.MatterId == matterId));
    }

    public async Task<List<Circulator>> GetAllByMatterIdAsync(int matterId)
    {
        return await _set
            .Include(c => c.SignatureSheets)
            .Where(c => c.SignatureSheets.Any(ss => ss.MatterId == matterId))
            .ToListAsync();
    }
}