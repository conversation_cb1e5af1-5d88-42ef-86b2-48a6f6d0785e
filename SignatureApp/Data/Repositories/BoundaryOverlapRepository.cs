using DataInterface.RepositoryInterfaces;
using Microsoft.EntityFrameworkCore;
using Model.Boundaries;

namespace Data.Repositories;

public class BoundaryOverlapRepository : EfCoreBaseRepository<BoundaryOverlap>, IBoundaryOverlapRepository
{
    public BoundaryOverlapRepository(SignatureAppDbContext context) : base(context)
    {
    }

    public Task<bool> DoesBoundaryOverlapAsync(int usStateId, string outerBoundaryName, string innerBoundaryName)
    {
        return _set
            .AnyAsync(x => x.OuterBoundary != null
                           && x.OuterBoundary.UsStateId == usStateId && x.OuterBoundary.Name == outerBoundaryName
                           && x.InnerBoundary != null
                           && x.OuterBoundary.UsStateId == usStateId && x.InnerBoundary.Name == innerBoundaryName);
    }
}