using DataInterface.RepositoryInterfaces;
using Microsoft.EntityFrameworkCore;
using Model.SignatureSheets;

namespace Data.Repositories;

public class InvalidSheetRepository : EfCoreBaseRepository<InvalidSheet>, IInvalidSheetRepository
{
    public InvalidSheetRepository(SignatureAppDbContext context) : base(context)
    {
    }

    public async Task<int> GetCountByMatterIdAsync(int matterId)
    {
        var query = _set.Where(s => s.MatterId == matterId)
            .Where(s => s.Status != InvalidSheetStatus.Replaced && s.Status != InvalidSheetStatus.MarkedAsValid);
        return await query.CountAsync();
    }

    public async Task<InvalidSheet?> GetNextInvalidSheetByMatterIdAsync(int matterId, string userEmail)
    {
        var query = _set.Where(s => s.MatterId == matterId)
            .Where(s => s.Status == InvalidSheetStatus.Invalid
                || s.Status == InvalidSheetStatus.Assigned && s.AssignedTo == userEmail)
            .OrderBy(s => s.SheetNumber);

        return await query.FirstOrDefaultAsync();
    }

    public async Task MarkInvalidAsReplacedAsync(int invalidSheetId, int validSheetId)
    {
        var invalidSheet = _set.First(s => s.Id == invalidSheetId);
        if (invalidSheet == null)
        {
            throw new InvalidOperationException($"Invalid sheet with ID {invalidSheetId} not found.");
        }

        invalidSheet.Status = InvalidSheetStatus.Replaced;
        invalidSheet.ValidSignatureSheetId = validSheetId;
        await _context.SaveChangesAsync();
    }
}