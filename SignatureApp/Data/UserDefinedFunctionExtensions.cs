using Microsoft.EntityFrameworkCore;

namespace Data;

public class SplitStringResult
{
    public string Value { get; set; } = string.Empty;
}

public static class UserDefinedFunctionExtensions
{
    /*
    public static void AddUserDefinedFunction(this SignatureAppDbContext context, ModelBuilder modelBuilder)
    {
        var splitStringMethodInfo = context.GetType().GetMethod(nameof(SignatureAppDbContext.SplitString),
            [typeof(string), typeof(string)]);
        if (splitStringMethodInfo != null)
        {
            modelBuilder.HasDbFunction(splitStringMethodInfo).HasName(nameof(SignatureAppDbContext.SplitString));
        }
    }
    */
}