using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using AutoMapper;
using Backend.Authentication;
using Model.Authorization;
using System.Security.Cryptography;
using Service.Email;
using Microsoft.AspNetCore.Authorization;
using Model;
using System.Text;
using Backend.DTO.Users;
using DataInterface.RepositoryInterfaces;

namespace Backend.Controllers;

[Route("api/[controller]")]
public class UsersController : BaseApiController
{
    private readonly IUserRepository _userRepository;
    private readonly IRoleRepository _roleRepository;
    private readonly IMapper _mapper;
    private readonly IEmailSender _emailSender;

    public UsersController(IUserRepository userRepository,
        IRoleRepository roleRepository,
        IMapper mapper,
        IEmailSender emailSender,
        ILogger<BaseApiController> logger)
        : base(logger)
    {
        _userRepository = userRepository;
        _roleRepository = roleRepository;
        _mapper = mapper;
        _emailSender = emailSender;
    }

    [HttpGet]
    [RoleCheck(RoleType.Reviewer, RoleType.Manager, RoleType.Admin)]
    public async Task<ActionResult<IEnumerable<UserDTO>>> GetUsers()
    {
        var users = await _userRepository.GetAllAsync();

        return Ok(_mapper.Map<IList<UserDTO>>(users));
    }

    [HttpGet("{id}")]
    [RoleCheck(RoleType.Reviewer, RoleType.Manager, RoleType.Admin)]
    public async Task<ActionResult<UserDTO>> GetUser(int id)
    {
        var user = await _userRepository.GetByIdAsync(id);

        if (user == null)
        {
            return NotFound();
        }

        var userDTO = _mapper.Map<UserDTO>(user);
        return userDTO;
    }

    /// <summary>
    /// Send an Email to the user. The email will contain the callbackUrl as the address to navigate to.
    /// </summary>
    /// <param name="request"></param>
    /// <param name="callbackUrl"></param>
    /// <returns></returns>
    [HttpPost("sendresetpassword")]
    [AllowAnonymous]
    public async Task<ActionResult> SendResetPasswordEmail(PasswordResetRequestDTO request, string callbackUrl)
    {
        var user = await _userRepository.GetByEmailAsync(request.Email);
        if (user == null)
        {
            return BadRequest("User not found");
        }

        var newValidationCode = Guid.NewGuid();
        user.ValidationCode = newValidationCode;

        _userRepository.SetModified(user);
        await _userRepository.SaveChangesAsync();

        var url = $"{callbackUrl}?token={newValidationCode}&email={user.Email}";
        var emailMessage = "Please click here to reset your password";

        var email = _emailSender.CreateEmailMessage(
            "Reset Your password",
            $"{emailMessage}: {callbackUrl}?token={newValidationCode}&email={user.Email}",
            CreateHtmlContent(url, emailMessage, "Reset Password"));

        var response =
            await _emailSender.SendEmail(email, new EmailAddress { Email = user.Email, Name = user.ShortName });

        return response.IsSuccess ? Ok() : BadRequest(response.ErrorMessages);
    }

    /// <summary>
    /// Reset a users password. It requires a valid token previously sent in an email.See SendResetPasswordEmail
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    [HttpPost("resetpassword")]
    [AllowAnonymous]
    public async Task<ActionResult> ResetUserPassword(ResetPasswordDTO dto)
    {
        var user = await _userRepository.GetByValidationCodeAsync(Guid.Parse(dto.Token));
        if (user == null)
        {
            _logger.LogWarning($"User not found for token: {dto.Token}");
            return NotFound("Password could not be reset.");
        }

        var newPasswordHash = Utility.GetHash(dto.Password, user.PasswordSalt);

        user.PasswordHash = newPasswordHash;
        user.ValidationCode = null;
        _userRepository.SetModified(user);

        try
        {
            await _userRepository.SaveChangesAsync();
        }
        catch (DbUpdateConcurrencyException)
        {
            _logger.LogError($"Concurrency error while resetting password for user: {user.Id}");
            throw;
        }

        return Ok();
    }

    /// <summary>
    /// Once a new users is Registered, they must be Validated and set their password. Users will be unable to log in without it.
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost("validate")]
    [AllowAnonymous]
    public async Task<ActionResult> ValidateUser([FromBody] ValidateUserDTO request)
    {
        var user = await _userRepository.GetByValidationCodeAsync(Guid.Parse(request.Token));

        if (user is null) return NotFound();

        user.IsValidated = true;
        user.FullName = request.FullName;
        user.ShortName = request.ShortName;
        user.PasswordHash = Utility.GetHash(request.Password, user.PasswordSalt);
        user.ValidationCode = null;

        _userRepository.SetModified(user);

        try
        {
            await _userRepository.SaveChangesAsync();
        }
        catch (DbUpdateConcurrencyException)
        {
            _logger.LogError($"Concurrency error while validating user: {user.Id}");
            throw;
        }

        return Ok();
    }

    /// <summary>
    /// Invites a user.  Email validation is required.
    /// </summary>
    /// <param name="userDTO"></param>
    /// <param name="callbackUrl">The base url sent in the validation email.</param>
    /// <returns></returns>
    [HttpPost("invite")]
    [RoleCheck(RoleType.Manager, RoleType.Admin)]
    public async Task<ActionResult<UserDTO>> CreateUser(InviteUserDTO userDTO, string callbackUrl)
    {
        var user = await _userRepository.GetByEmailAsync(userDTO.Email);
        if (user != null) return BadRequest("User already exists");

        var role = await _roleRepository.GetByIdAsync(userDTO.RoleId);
        if (role == null) return BadRequest("Role not found");

        user = new User
        {
            Email = userDTO.Email,
            Role = role,
            PasswordSalt = RandomNumberGenerator.GetInt32(1, 1000),
            PasswordHash = " ",
            ShortName = " ",
            FullName = " ",
            StartDate = DateTime.UtcNow,
            IsValidated = false,
            ValidationCode = Guid.NewGuid()
        };

        _userRepository.Add(user);
        await _userRepository.SaveChangesAsync();
        _logger.LogInformation($"User created: {user.Email}");

        var url = $"{callbackUrl}?token={user.ValidationCode}&email={user.Email}";
        var emailMessage = "Please click here to register your account";

        var email = _emailSender.CreateEmailMessage(
            "Verify your SW Petitions Account",
            $"{emailMessage}: {callbackUrl}?token={user.ValidationCode}&email={user.Email}",
            CreateHtmlContent(url, emailMessage, "Reset Password"));

        var response =
            await _emailSender.SendEmail(email, new EmailAddress { Email = user.Email, Name = user.ShortName });
        if (!response.IsSuccess)
        {
            _logger.LogError($"Failed to send email to {user.Email}: {string.Join(", ", response.ErrorMessages)}");
            return BadRequest(response.ErrorMessages);
        }

        _logger.LogInformation($"Email sent to {user.Email}");
        return CreatedAtAction(nameof(CreateUser), userDTO);
    }

    [HttpGet("members")]
    [RoleCheck(RoleType.Manager, RoleType.Admin)]
    public async Task<IEnumerable<GetMemberUserDTO>> GetMembers()
    {
        return (await _userRepository.GetAllMembersAsync())
            .Select(x => new GetMemberUserDTO
                { Id = x.Id, Email = x.Email, FullName = x.FullName, ShortName = x.ShortName });
    }

    [HttpGet("reviewers")]
    [RoleCheck(RoleType.Manager, RoleType.Admin)]
    public async Task<IEnumerable<GetReviewerUserDTO>> GetReviewers()
    {
        return (await _userRepository.GetAllReviewersAsync())
            .Select(x => new GetReviewerUserDTO
                { Id = x.Id, Email = x.Email, FullName = x.FullName, ShortName = x.ShortName });
    }

    /// <summary>
    /// Invites a list of users.  Email validation is required.  ALL users
    /// are created with a role of Reviewer.
    /// </summary>
    /// <param name="file">Uploaded CSV file</param>
    /// <param name="callBackUrl">The base url sent in the validation email.</param>
    /// <returns></returns>
    [HttpPost("reviewers")]
    [RoleCheck(RoleType.Manager, RoleType.Admin)]
    public async Task<ActionResult<int>> CreateReviewers(IFormFile file, [FromQuery] string callBackUrl)
    {
        if (!file.FileName.EndsWith(".csv") && !file.FileName.EndsWith(".txt"))
        {
            return BadRequest("Unrecognized File Format");
        }

        var errorBuilder = new StringBuilder();

        using var reader = new StreamReader(file.OpenReadStream());
        var usersCreated = 0;
        bool overallSuccess = true;

        var role = await _roleRepository.GetByIdAsync((int)RoleType.Reviewer);
        if (role == null) return BadRequest("Role not found");

        var emailAddresses = new List<string>();
        int lineNumber = 0;
        while (!reader.EndOfStream)
        {
            var row = await reader.ReadLineAsync();
            lineNumber++;
            if (string.IsNullOrWhiteSpace(row))
            {
                continue;
            }

            if (row.Contains(','))
                return BadRequest($"Invalid format on line {lineNumber}: multiple columns detected.");

            if (!IsValidEmail(row))
                return BadRequest($"Invalid email address on line {lineNumber}: {row}");

            emailAddresses.Add(row);
        }

        // Check for duplicate emails in the database
        var existingUsers = await _userRepository.GetAllReviewersAsync();
        var existingEmails = existingUsers.Select(u => u.Email).ToList();

        foreach (var emailAddress in emailAddresses)
        {
            User? user;
            if (existingEmails.Contains(emailAddress))
            {
                user = existingUsers.Find(u => u.Email == emailAddress);
                if (user == null)
                {
                    overallSuccess = false;
                    errorBuilder.AppendLine($"User not found for email: {emailAddress}");
                    continue;
                }
            }
            else
            {
                user = new User { RoleId = (int)RoleType.Reviewer, Email = emailAddress };
                user.Role = role;
                user.PasswordSalt = RandomNumberGenerator.GetInt32(1, 1000);
                user.PasswordHash = " ";
                user.ShortName = " ";
                user.FullName = " ";
                user.StartDate = DateTime.UtcNow;
                user.IsValidated = false;
                user.ValidationCode = Guid.NewGuid();

                _userRepository.Add(user);
                await _userRepository.SaveChangesAsync();
            }

            var validationCode = user.ValidationCode;
            var url = $"{callBackUrl}?token={validationCode}&email={user.Email}";
            var emailMessage = "Please click here to register your account";
            var htmlContent = CreateHtmlContent(url, emailMessage, "Reset Password");

            var email = _emailSender.CreateEmailMessage(
                "Verify your SW Petitions Account",
                $"{emailMessage}: {callBackUrl}?token={validationCode}&email={user.Email}",
                htmlContent);

            var response =
                await _emailSender.SendEmail(email, new EmailAddress { Email = user.Email, Name = user.ShortName });
            if (response.IsSuccess)
            {
                usersCreated++;
            }
            else
            {
                overallSuccess = false;
                errorBuilder.AppendLine($"{user.Email}: {response.ErrorMessages}");
            }
        }

        return overallSuccess ? CreatedAtAction(nameof(CreateReviewers), usersCreated) : BadRequest(errorBuilder);
    }

    private bool IsValidEmail(string email)
    {
        return System.Text.RegularExpressions.Regex.IsMatch(email,
            @"^[^@\s]+@[^@\s]+\.[^@\s]+$",
            System.Text.RegularExpressions.RegexOptions.IgnoreCase);
    }

    [HttpDelete("{id}")]
    [RoleCheck(RoleType.Admin, RoleType.Manager)]
    public async Task<IActionResult> DeleteUser(int id)
    {
        var user = await _userRepository.GetByIdAsync(id);
        if (user == null)
        {
            return NotFound();
        }

        _userRepository.Remove(user);
        await _userRepository.SaveChangesAsync();

        return NoContent();
    }

    public static string CreateHtmlContent(string callbackUrl, string message, string buttonMessage)
    {
        return $"<html><head><title>Verification</title></head><body><div " +
               $" style=\"color:#444444; font-size:12px; " +
               $"line-height:20px; padding:16px 16px 16px 16px; text-align:Center;\">" +
               $"{message}" +
               $"<p style=\"font-size:12px; line-height:20px;\">" +
               $"<a clicktracking=\"off\" class=\"button\" href=\"{callbackUrl}\" style=\"font-family:sans-serif;text-decoration:none;\">" +
               $"{buttonMessage}" +
               $"</a></p>" +
               $"<p>Validation Link: {callbackUrl}</p></div></body></html>";
    }
}