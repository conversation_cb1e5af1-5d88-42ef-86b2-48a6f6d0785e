﻿using AutoMapper;
using Backend.DTO;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Model.Matters;
using Backend.Authentication;
using Model.Authorization;
using DataInterface.RepositoryInterfaces;
using Service.ServiceModels;

namespace Backend.Controllers;

[Route("api/matters")] // {matterId}/variables
[RoleCheck(RoleType.Admin, RoleType.Manager)]
public class MatterVariablesController : BaseApiController
{
    private readonly IMatterVariableRepository _matterVariableRepository;
    private readonly IMapper _mapper;

    public MatterVariablesController(
        IMatterVariableRepository matterVariableRepository,
        IMapper mapper,
        ILogger<BaseApiController> logger)
        : base(logger)
    {
        _matterVariableRepository = matterVariableRepository;
        _mapper = mapper;
    }

    [HttpGet("{matterId}/variables")]
    public async Task<ActionResult<List<MatterVariableDTO>>> GetMatterVariables(int matterId)
    {
        var matterVariables = await _matterVariableRepository.GetByMatterIdAsync(matterId);

        return Ok(_mapper.Map<List<MatterVariableDTO>>(matterVariables));
    }


    [HttpPut("{matterId}/variables")]
    [RoleCheck(RoleType.Admin)]
    public async Task<ActionResult> UpsertMatterVariables(int matterId, List<MatterVariableDTO> variables)
    {
        if (variables.Count == 0)
        {
            return Ok();
        }

        foreach (var variable in variables)
        {
            if (variable.MatterVariableId is null)
            {
                var newMatterVariable = new MatterVariable
                {
                    Key = variable.Key,
                    Value = variable.Value,
                    MatterId = matterId
                };

                _matterVariableRepository.Add(newMatterVariable);
            }
            else
            {
                var matterVariableToUpdate =
                    await _matterVariableRepository.GetByIdAsync((int)variable.MatterVariableId);
                if (matterVariableToUpdate is null) continue;

                matterVariableToUpdate.Value = variable.Value;
                matterVariableToUpdate.Key = variable.Key;
                _matterVariableRepository.SetModified(matterVariableToUpdate);
            }
        }

        try
        {
            await _matterVariableRepository.SaveChangesAsync();
        }
        catch (DbUpdateConcurrencyException)
        {
            throw;
        }

        return Ok();
    }

    [HttpPost("{matterId}/variables/delete")]
    [RoleCheck(RoleType.Admin)]
    public async Task<ActionResult> DeleteMatterVariable([FromBody] List<int> variableIds)
    {
        foreach (var variableId in variableIds)
        {
            var matterVariable = await _matterVariableRepository.GetByIdAsync(variableId);
            if (matterVariable is null) continue;

            _matterVariableRepository.Remove(matterVariable);
        }

        int deletedCount = await _matterVariableRepository.SaveChangesAsync();
        if (deletedCount == variableIds.Count)
        {
            return Ok();
        }

        return StatusCode(StatusCodes.Status207MultiStatus);
    }
}