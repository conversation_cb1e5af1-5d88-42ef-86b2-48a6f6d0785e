using Backend.Authentication;
using DataInterface.RepositoryInterfaces;
using Microsoft.AspNetCore.Mvc;
using Model.Authorization;
using Model.Rules;

namespace Backend.Controllers;

[Route("api/[controller]")]
[RoleCheck(RoleType.Admin)]
public class BackgroundOperationController : BaseApiController
{
    private readonly IBackgroundOperationRepository _backgroundOperationRepository;

    public BackgroundOperationController(
        IBackgroundOperationRepository backgroundOperationRepository,
        ILogger<BaseApiController> logger
    )
        : base(logger)
    {
        _backgroundOperationRepository = backgroundOperationRepository;
    }

    [HttpGet("{backgroundId}")]
    public async Task<ActionResult<ExecutionStatus>> GetBackgroundOperation(int backgroundId)
    {
        var backgroundOperation = await _backgroundOperationRepository.GetByIdAsync(backgroundId);
        if (backgroundOperation == null)
        {
            return NotFound();
        }

        return Ok(backgroundOperation);
    }

    [HttpDelete("{backgroundId}")]
    public async Task<ActionResult<ExecutionStatus>> DeleteBackgroundOperation(int backgroundId)
    {
        var success = await _backgroundOperationRepository.DeleteByIdAsync(backgroundId);
        if (!success)
        {
            return NotFound();
        }

        return Ok(success);
    }
}