using AutoMapper;
using Backend.Authentication;
using Backend.DTO.ExternalDataSource;
using DataInterface.RepositoryInterfaces;
using Microsoft.AspNetCore.Mvc;
using Model.Authorization;
using Model.DTOs;
using Model.ExternalDataSources;
using Model.Matters;
using Service;

namespace Backend.Controllers;

[Route("api/[controller]")]
public class ExternalDataController : BaseApiController
{
    private readonly IExternalDataSourceRepository _externalDataSourceRepository;
    private readonly IMapper _mapper;
    private readonly IRegisteredVoterRepository _registeredVoterRepository;
    private readonly ITemplateSignatureColumnRepository _templateSignatureColumnRepository;
    private readonly IWorkRepository _workRepository;
    private readonly VoterRegistrationSearchService _voterRegistrationSearchService;


    public ExternalDataController(
        ILogger<BaseApiController> logger,
        IExternalDataSourceRepository externalDataSourceRepository,
        <PERSON><PERSON><PERSON><PERSON> mapper,
        IRegisteredVoterRepository registeredVoterRepository,
        ITemplateSignatureColumnRepository templateSignatureColumnRepository,
        IWorkRepository workRepository,
        VoterRegistrationSearchService voterRegistrationSearchService
    )
        : base(logger)
    {
        _externalDataSourceRepository = externalDataSourceRepository;
        _mapper = mapper;
        _registeredVoterRepository = registeredVoterRepository;
        _templateSignatureColumnRepository = templateSignatureColumnRepository;
        _workRepository = workRepository;
        _voterRegistrationSearchService = voterRegistrationSearchService;
    }

    [HttpGet]
    [RoleCheck(RoleType.Admin)]
    public async Task<ActionResult<List<ExternalDataSourceDTO>>> GetAllExternalDataSources(UploadType uploadType)
    {
        return _mapper.Map<List<ExternalDataSourceDTO>>(
            await _externalDataSourceRepository.GetAllByTypeAsync(uploadType));
    }

    [HttpGet("id/{externalDataSourceId}")]
    [RoleCheck(RoleType.Admin)]
    public async Task<ActionResult<ExternalDataSourceDTO>> GetExternalDataSourceById(int externalDataSourceId)
    {
        return _mapper.Map<ExternalDataSourceDTO>(
            await _externalDataSourceRepository.GetByIdAsync(externalDataSourceId));
    }

    [HttpGet("matter/{matterId}")]
    [RoleCheck(RoleType.Admin)]
    public async Task<ActionResult<List<ExternalDataSourceMatter>>> GetExternalDataSourceMatters(int matterId)
    {
        var externalDataSourceMatters = await _externalDataSourceRepository.GetByMatterIdAsync(matterId);
        return Ok(externalDataSourceMatters);
    }

    [HttpPost("matter/{matterId}/votersearch")]
    [RoleCheck(RoleType.Admin, RoleType.Manager, RoleType.Reviewer)]
    public async Task<ActionResult<GetVoterSearchDTO>> GetExternalDataSourceVoterSearch(int matterId,
        VoterSearchDTO? searchCriteria)
    {
        if (searchCriteria == null)
        {
            return BadRequest("Voter search input is required");
        }

        var counties = (await _registeredVoterRepository.GetUploadsByMatterIdAsync(matterId)).ToArray();
        var voterCriteria = _mapper.Map<RegisteredVoter>(searchCriteria);
        if (!string.IsNullOrWhiteSpace(voterCriteria.StreetAddress))
        {
            var address = AddressParsingService.ParseAddressLine1(voterCriteria.StreetAddress);
            voterCriteria.StreetNumber = address.StreetNumber;
            voterCriteria.Direction = address.Direction;
            voterCriteria.StreetName = address.StreetName;
            voterCriteria.StreetType = address.StreetType;
            if (!string.IsNullOrEmpty(address.StreetName))
            {
                voterCriteria.StreetAddress = null;
            }
        }

        var searchResults = await _registeredVoterRepository.SearchByCriteriaAsync(counties, voterCriteria, maxTake:100);
        var resultDtos = searchResults.Results.Select(result => _mapper.Map<VoterSearchDTO>(result));
        var responseDto = new GetVoterSearchDTO { TotalHits = searchResults.TotalHits, Results = resultDtos.ToList() };
        return Ok(responseDto);
    }

    [HttpPost("matter/{matterId}")]
    [RoleCheck(RoleType.Admin)]
    public async Task<ActionResult> PostExternalDataSourceMatters(int matterId, Dictionary<string, bool> dict)
    {
        var dataSourceIds = new List<int>();
        foreach (var kvp in dict)
        {
            if (kvp.Value)
            {
                if (int.TryParse(kvp.Key.Substring("isChecked".Length), out int dataSourceId))
                {
                    dataSourceIds.Add(dataSourceId);
                }
            }
        }

        await _externalDataSourceRepository.SetMatterId(matterId, dataSourceIds);
        return Ok();
    }

    [HttpGet("{workId}/{registeredVoterId}")]
    public async Task<ActionResult<GetExternalDataDTO>> GetWorkRegisteredVoterById(int workId, int registeredVoterId)
    {
        var work = await _workRepository.GetWorkWithTaskIncludedAsync(workId);
        if (work?.Task is null) return BadRequest("WorkId not associated to a task");
        var templateColumns = await _templateSignatureColumnRepository.GetAllByTemplateIdAsync(work.Task.TemplateId);

        var record = await _registeredVoterRepository.GetByIdAsync(registeredVoterId);
        if (record is null)
        {
            return NotFound();
        }

        return Ok(_voterRegistrationSearchService.TranslateRegisteredVoter(record, templateColumns));
    }


    [HttpPost("search/{workId}")]
    [RoleCheck(RoleType.Admin, RoleType.Manager, RoleType.Reviewer)]
    public async Task<ActionResult<GetExternalDataSearchDTO>> SearchExternalDataByWorkId(
        List<ExternalDataField>? dtos, int workId)
    {
        var work = await _workRepository.GetWorkWithTaskIncludedAsync(workId);
        if (work is null) return BadRequest("WorkId not associated to a task");
        var templateColumns = await _templateSignatureColumnRepository.GetAllByTemplateIdAsync(work.Task.TemplateId);
        if (dtos is null || dtos.Count == 0) return BadRequest("No fields found with request");

        var counties = (await _registeredVoterRepository.GetUploadsByMatterIdAsync(work.MatterId)).ToArray();
        RegisteredVoter voterCriteria =
            _voterRegistrationSearchService.GetRegisteredVoterFromDto(dtos, templateColumns);
        var searchResults = await _registeredVoterRepository.SearchByCriteriaAsync(counties, voterCriteria, maxTake:10);
        var response = new List<GetExternalDataDTO>();
        foreach (var result in searchResults.Results)
        {
            response.Add(_voterRegistrationSearchService.TranslateRegisteredVoter(result, templateColumns));
        }

        var responses = new GetExternalDataSearchDTO { TotalHits = searchResults.TotalHits, Results = response };
        return Ok(responses);
    }

    [HttpGet("{countyName}")]
    [RoleCheck(RoleType.Admin)]
    public async Task<ActionResult<bool>> GetMatchingCounty(string countyName)
    {
        var externalDataSourceMatters = await _externalDataSourceRepository.GetMatchingCountyAsync(countyName);
        return Ok(externalDataSourceMatters.Count > 0);
    }
}