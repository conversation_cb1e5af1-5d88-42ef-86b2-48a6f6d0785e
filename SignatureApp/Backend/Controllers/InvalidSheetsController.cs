using System.Text.Json;
using Backend.Authentication;
using DataInterface;
using DataInterface.RepositoryInterfaces;
using DataInterface.ServiceInterfaces;
using Microsoft.AspNetCore.Mvc;
using Model.Authorization;
using Model.Rules;
using Model.SignatureSheets;
using Model.Workflow;
using Service;
using Service.ServiceModels;

namespace Backend.Controllers;

[Route("api/invalidsheets")] // "{matterId}"
public class InvalidSheetsController : BaseApiController
{
    private readonly IBackgroundOperationRepository _backgroundOperationRepository;
    private readonly IFileService _fileService;
    private readonly IInvalidSheetRepository _invalidSheetRepository;
    private readonly IMatterRepository _matterRepository;
    private readonly ISignatureSheetRepository _signatureSheetRepository;
    private readonly ISignatureSheetUploadRepository _signatureSheetUploadRepository;
    private readonly ITemplateRepository _templateRepository;
    private readonly ITemplatePageRepository _templatePageRepository;
    private readonly IUserContext _userContext;
    private readonly InvalidSheetService _invalidSheetService;
    private readonly PdfManipulationService _pdfManipulationService;
    private readonly SignatureSheetHierarchyService _signatureSheetHierarchyService;
    private readonly WorkGenerationService _workGenerationService;
    private readonly SignatureSheetUploadService _signatureSheetUploadService;

    public InvalidSheetsController(
        IBackgroundOperationRepository backgroundOperationRepository,
        IFileService fileService,
        ILogger<BaseApiController> logger,
        IMatterRepository matterRepository,
        IInvalidSheetRepository invalidSheetRepository,
        ISignatureSheetRepository signatureSheetRepository,
        ISignatureSheetUploadRepository signatureSheetUploadRepository,
        ITemplateRepository templateRepository,
        ITemplatePageRepository templatePageRepository,
        IUserContext userContext,
        InvalidSheetService invalidSheetService,
        PdfManipulationService pdfManipulationService,
        SignatureSheetHierarchyService signatureSheetHierarchyService,
        SignatureSheetUploadService signatureSheetUploadService,
        WorkGenerationService workGenerationService
    )
        : base(logger)
    {
        _backgroundOperationRepository = backgroundOperationRepository;
        _fileService = fileService;
        _matterRepository = matterRepository;
        _invalidSheetRepository = invalidSheetRepository;
        _signatureSheetRepository = signatureSheetRepository;
        _signatureSheetUploadRepository = signatureSheetUploadRepository;
        _templateRepository = templateRepository;
        _templatePageRepository = templatePageRepository;
        _userContext = userContext;
        _invalidSheetService = invalidSheetService;
        _pdfManipulationService = pdfManipulationService;
        _signatureSheetHierarchyService = signatureSheetHierarchyService;
        _workGenerationService = workGenerationService;
        _signatureSheetUploadService = signatureSheetUploadService;
    }

    [HttpGet("{matterId}/count")]
    [RoleCheck(RoleType.Admin, RoleType.Manager)]
    public async Task<ActionResult<InvalidSheet[]>> GetInvalidCountByMatter(int matterId)
    {
        var invalidSheetCount = await _invalidSheetRepository.GetCountByMatterIdAsync(matterId);

        return Ok(invalidSheetCount);
    }

    /*
     // This should go away, we no longer get all Invalid sheets, instead just the "next" one
    [HttpGet("{matterId}/invalid")]
    [RoleCheck(RoleType.Admin, RoleType.Manager)]
    public async Task<ActionResult<InvalidSheet[]>> GetInvalidSheetsByMatterId(int matterId)
    {
        var invalidSheets = await _invalidSheetRepository.GetInvalidSheetsByMatterIdAsync(matterId);
        return Ok(invalidSheets);
    }
    */

    [HttpGet("{matterId}/next")]
    [RoleCheck(RoleType.Admin, RoleType.Manager)]
    public async Task<ActionResult<InvalidSheet>> GetNextInvalidSheetByMatterId(int matterId)
    {
        var invalidSheet =
            await _invalidSheetRepository.GetNextInvalidSheetByMatterIdAsync(matterId, _userContext.Username);
        if (invalidSheet == null)
        {
            return NotFound();
        }

        await _invalidSheetService.AssignInvalidSheetAsync(invalidSheet, _userContext.Username);
        return Ok(invalidSheet);
    }

    [HttpPost("{matterId}/markvalid/{invalidSheetId}")]
    [RoleCheck(RoleType.Admin, RoleType.Manager)]
    public async Task<ActionResult> MarkInvalidSheetAsValid(int matterId, int invalidSheetId,
        WhichPartsAreValidDTO whichPartsAreValid)
    {
        var invalidSheet = await _invalidSheetRepository.GetByIdAsync(invalidSheetId);
        if (invalidSheet == null)
        {
            return BadRequest($"Invalid Sheet with ID {invalidSheetId} not found");
        }

        var matter = await _matterRepository.GetByIdAsync(matterId);
        if (matter == null)
        {
            return BadRequest($"Matter {matterId} does not exist");
        }

        var stream = await GetUnverifiedFileStreamAsync(invalidSheet.Filename);
        if (stream == null)
        {
            return BadRequest($"Could not load {invalidSheet.Filename}");
        }

        var success = FileNameUtils.MatchPdfFile(invalidSheet.Filename,
            out (int matterId, int uploadId, int templateId, int pageNumber) info);
        if (!success)
        {
            return BadRequest($"File {invalidSheet.Filename} Doesn't match pattern");
        }

        var upload = await _signatureSheetUploadRepository.GetByIdAsync(info.uploadId);
        if (upload == null)
        {
            return BadRequest($"Upload {info.uploadId} doesn't exist");
        }

        var serviceResult = await _signatureSheetHierarchyService.SaveSignatureSheetHierarchyAsync(stream,
            invalidSheet.Filename, upload, whichPartsAreValid);
        if (!serviceResult.IsSuccess || serviceResult.Value == null || !serviceResult.Value.Any())
        {
            return BadRequest(serviceResult.ErrorMessages);
        }

        var sheet = serviceResult.Value.First();
        // if we were able to save the hierarchy, we can move the sheet to the new location
        var newFilename = $"{info.templateId}/{info.pageNumber:D6}.pdf";
        var oldFilename = invalidSheet.Filename;
        if (oldFilename.StartsWith("unverified/"))
        {
            oldFilename = oldFilename.Substring("unverified/".Length);
        }

        await _fileService.RenameFileStreamAsync("unverified", oldFilename, newFilename, $"matter{matterId}");

        await _invalidSheetService.MarkInvalidAsValid(invalidSheet, sheet, newFilename);
        await _signatureSheetRepository.SaveChangesAsync();

        _ = await _workGenerationService.SaveWorkFromSignatureSheet(invalidSheet.TemplateId, matterId, sheet,
            WorkStatus.Unavailable);

        return Ok(sheet.SheetNumber);
    }

    [HttpPost("{matterId}/markreplaced/{invalidSheetId}/{templateId}")]
    [RoleCheck(RoleType.Admin, RoleType.Manager)]
    [DisableRequestSizeLimit]
    public async Task<ActionResult> ReplaceInvalidSheet(IFormFile file, int matterId,
        int invalidSheetId, int templateId, CancellationToken ct,
        [FromServices] IServiceScopeFactory serviceScopeFactory)
    {
        var invalidSheet = await _invalidSheetRepository.GetByIdAsync(invalidSheetId);
        if (invalidSheet == null)
        {
            return BadRequest($"Invalid Sheet with ID {invalidSheetId} not found");
        }

        var matter = await _matterRepository.GetByIdAsync(matterId);
        if (matter == null)
        {
            return BadRequest($"Matter {matterId} does not exist");
        }

        var templatePages = await _templatePageRepository.GetByTemplateIdAsync(templateId);
        if (templatePages.Count == 0)
        {
            return BadRequest($"Template {templateId} does not exist");
        }

        SignatureSheetUpload signatureSheetUpload =
            await _signatureSheetUploadService.CreateSignatureSheetUploadAsync(file.FileName, templateId, matterId,
                file.FileName);
        var backgroundOperation = new BackgroundOperation
        {
            ExecutionStatus = ExecutionStatus.Running,
            MatterId = matterId,
            OperationType = BackgroundOperationType.UploadSignatureSheets,
        };
        _backgroundOperationRepository.Add(backgroundOperation);
        await _backgroundOperationRepository.SaveChangesAsync();

        var memStream = new MemoryStream();
        await file.CopyToAsync(memStream, ct);
        memStream.Position = 0;

        _ = System.Threading.Tasks.Task.Run(async () =>
        {
            using var scope = serviceScopeFactory.CreateScope();
            var backgroundOperationRepository =
                scope.ServiceProvider.GetRequiredService<IBackgroundOperationRepository>();
            var signatureSheetRepository =
                scope.ServiceProvider.GetRequiredService<ISignatureSheetRepository>();

            var invalidSheetRepository =
                scope.ServiceProvider.GetRequiredService<IInvalidSheetRepository>();
            try
            {
                var helper = new UploadSignatureSheetsControllerHelper(scope);
                memStream = await helper.GetSignatureSheetFileStream(memStream, file.FileName, templateId,
                    templatePages);
                var result = await helper.ProcessSignatureSheetFromUser(memStream, file.FileName, templateId, matterId,
                    signatureSheetUpload, ct);
                if (result.IsSuccess && result.Value != null && result.Value.Length != 0)
                {
                    memStream.Position = 0;
                    var newFilename = $"{templateId}/{result.Value.First():D6}.pdf";
                    string? error =
                        await _fileService.SaveFileStreamAsync($"matter{matterId}", newFilename, memStream, false, ct);
                    if (error != null)
                    {
                        backgroundOperation.ExecutionStatus = ExecutionStatus.Failed;
                        backgroundOperation.Message = error;
                        return;
                    }

                    var sheetNumber = result.Value.First();
                    var validSignatureSheet =
                        await signatureSheetRepository.GetByMatterAndSheetNumberAsync(matterId, sheetNumber);
                    if (validSignatureSheet == null)
                    {
                        backgroundOperation.ExecutionStatus = ExecutionStatus.Failed;
                        backgroundOperation.Message =
                            $"No Valid Signature Sheet with sheet number {sheetNumber} found for matter {matterId}";
                        return;
                    }

                    backgroundOperation.Message = JsonSerializer.Serialize(new
                    {
                        validSignatureSheet.Id,
                        sheetNumber
                    });
                    backgroundOperation.ExecutionStatus = ExecutionStatus.Succeeded;
                    await invalidSheetRepository.MarkInvalidAsReplacedAsync(invalidSheetId, validSignatureSheet.Id);
                }
            }
            catch (Exception ex)
            {
                backgroundOperation.ExecutionStatus = ExecutionStatus.Failed;
                backgroundOperation.Message = ex.Message;
            }
            finally
            {
                backgroundOperationRepository.SetModified(backgroundOperation);
                await backgroundOperationRepository.SaveChangesAsync();
            }
        }, ct);
        return Ok(backgroundOperation.Id);
    }

    [HttpGet("{matterId}/{invalidSheetId}/{pageNumber}")]
    [RoleCheck(RoleType.Admin, RoleType.Manager)]
    public async Task<ActionResult<byte[]>> GetInvalidSheetPageById(int invalidSheetId, int pageNumber)
    {
        var invalidSheet = await _invalidSheetRepository.GetByIdAsync(invalidSheetId);
        if (invalidSheet == null)
        {
            return BadRequest($"Invalid Sheet with ID {invalidSheetId} not found");
        }

        var template = await _templateRepository.GetByIdAsync(invalidSheet.TemplateId);
        if (template == null)
        {
            _logger.LogWarning("Template {TemplateId} not found for invalid sheet {invalidSheetId}",
                invalidSheet.TemplateId, invalidSheetId);
            return BadRequest("Template not found");
        }

        if (template.PageSize == null)
        {
            _logger.LogWarning("Template {TemplateId} has no page size for invalid sheet {invalidSheetId}",
                invalidSheet.TemplateId, invalidSheetId);
            return BadRequest("Template has no page size");
        }

        var pdfStream = await GetUnverifiedFileStreamAsync(invalidSheet.Filename);
        if (pdfStream == null)
        {
            _logger.LogWarning("File {Filename} not found for invalid sheet {invalidSheetId}", invalidSheet.Filename,
                invalidSheetId);
            return BadRequest("File not found");
        }

        var imageBytes =
            _pdfManipulationService.ConvertPdfPageToImage(pdfStream, template.PageSize.Value, pageNumber);
        return new FileContentResult(imageBytes, "image/png");
    }

    private async Task<Stream?> GetUnverifiedFileStreamAsync(string filename)
    {
        var containerName = "unverified";
        if (filename.StartsWith(containerName))
        {
            filename = filename.Substring(containerName.Length + 1);
        }

        var pdfStream = await _fileService.GetFileStreamAsync(containerName, filename);
        return pdfStream;
    }
}