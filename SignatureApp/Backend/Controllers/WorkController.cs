﻿using AutoMapper;
using Backend.Authentication;
using Backend.DTO;
using DataInterface;
using DataInterface.RepositoryInterfaces;
using DataInterface.ServiceInterfaces;
using Microsoft.AspNetCore.Mvc;
using Model.Authorization;
using Model.DTOs;
using Model.Matters;
using Model.Rules;
using Model.Workflow;
using Service;
using Service.ServiceModels;

namespace Backend.Controllers;

[Route("api/[controller]")]
public class WorkController : BaseApiController
{
    private readonly IMapper _mapper;
    private readonly IMatterRepository _matterRepository;
    private readonly ITaskRepository _taskRepository;
    private readonly IUserContext _userContext;
    private readonly IUserRepository _userRepository;
    private readonly IWorkRepository _workRepository;
    private readonly WorkService _workService;

    public WorkController(
        ILogger<BaseApiController> logger,
        IMapper mapper,
        IMatterRepository matterRepository,
        ITaskRepository taskRepository,
        IUserContext userContext,
        IUserRepository userRepository,
        IWorkRepository workRepository,
        WorkService workService
    )
        : base(logger)
    {
        _mapper = mapper;
        _matterRepository = matterRepository;
        _taskRepository = taskRepository;
        _userContext = userContext;
        _userRepository = userRepository;
        _workRepository = workRepository;
        _workService = workService;
    }

    [HttpGet("status")]
    public async Task<ActionResult<List<MatterWorkStatusDTO>>> GetWorkByMatterAndStatus()
    {
        var activeMatters = (await _matterRepository.GetAllActiveMattersAsync()).ToDictionary(x => x.Id);
        var activeMatterIds = activeMatters.Keys;
        List<WorkMatterStatus> matterWorkStatuses = await _workRepository.GetAllWorkByMatterAndStatusAsync();
        var dtos = _mapper.Map<List<MatterWorkStatusDTO>>(matterWorkStatuses)
            .Where(mws => activeMatterIds.Contains(mws.MatterId)).ToList();
        foreach (var mws in dtos)
        {
            Matter activeMatter = activeMatters[mws.MatterId];
            mws.MatterName = activeMatter.Name;
            mws.MatterDueDate = activeMatter.DueDate;
            mws.MatterCreatedOn = activeMatter.CreatedOn;
        }

        _workService.AddActiveMattersWithNoWork(activeMatters, dtos);
        return Ok(dtos.OrderBy(m => m.MatterDueDate).ThenBy(m => m.MatterCreatedOn));
    }

    [HttpGet("{matterId}/taskstatus")]
    public async Task<ActionResult<List<WorkTaskStatus>>> GetWorkByMatterIdAndTask(int matterId)
    {
        var work = (await _workRepository.GetAllWorkByMatterIdAndTaskAsync(matterId));
        return Ok(work);
    }


    [HttpGet("{workId}")]
    public async Task<ActionResult<GetWorkDTO>> GetWork(int workId)
    {
        var work = await _workRepository.GetWorkWithTaskIncludedAsync(workId);
        if (work is null) return BadRequest("Work Id not found");
        var workDto = await _workService.GetWorkDTOByWorkAsync(work);
        return GetActionResultFromWorkDto(workDto);
    }

    [HttpGet("next")]
    public async Task<ActionResult<GetWorkDTO>?> GetNextWork()
    {
        var user = await _userRepository.GetByEmailAsync(_userContext.Username);
        if (user is null) return NotFound("User not found");

        var tasks = await _taskRepository.GetAllByUserId(user.Id);

        Work? nextUnassignedWork = null;
        while (nextUnassignedWork == null)
        {
            nextUnassignedWork = await _workRepository.GetNextUnassignedWorkAsync(tasks, user.Id);
            if (nextUnassignedWork is null) return null;

            if (nextUnassignedWork.Task.TaskType.HasFlag(TaskType.SignatureTableColumn))
            {
                nextUnassignedWork = await _workService.CheckForNullOrPreviousStrikethrough(user, nextUnassignedWork);
            }
        }

        nextUnassignedWork.UserId = user.Id;
        nextUnassignedWork.WorkStatus = WorkStatus.Assigned;
        if (nextUnassignedWork.AssignmentDate is null)
        {
            nextUnassignedWork.AssignmentDate = DateTime.UtcNow;
        }

        await _workRepository.SaveChangesAsync();

        var workDto = await _workService.GetWorkDTOByWorkAsync(nextUnassignedWork);
        return GetActionResultFromWorkDto(workDto);
    }

    [HttpGet("{workId}/previous")]
    public async Task<ActionResult<GetWorkDTO>?> GetPreviousWork(int workId)
    {
        var user = await _userRepository.GetByEmailAsync(_userContext.Username);
        if (user is null) return NotFound("User not found");

        var currentWork = await _workRepository.GetByIdAsync(workId);
        if (currentWork is null) return NotFound($"Work not found for id {workId}");

        var previousWork =
            await _workRepository.GetPreviousWorkByUserIdAsync(currentWork.AssignmentDate, user.Id, currentWork.Id);
        if (previousWork is null) return null;

        var workDto = await _workService.GetWorkDTOByWorkAsync(previousWork);
        return GetActionResultFromWorkDto(workDto);
    }

    [HttpPut("{workId:int}")]
    public async Task<ActionResult<GetWorkDTO>> UpdateWork(FinishWorkDTO dto, int workId)
    {
        var user = await _userRepository.GetByEmailAsync(_userContext.Username);
        if (user is null) return NotFound("User not found");

        var work = await _workRepository.GetWorkWithTaskIncludedAsync(workId);
        if (work is null) return BadRequest("id not valid.");

        var isBreakedOrFlagged = await _workService.DtoIsFlaggedOrBreakAsync(work, dto, user);
        if (isBreakedOrFlagged) return Ok(dto);

        var task = await _taskRepository.GetByIdAsync(work.TaskId);
        if (task is null) return NotFound("Task not found");

        bool wasSuccessful = await _workService.UpdateWorkInternal(dto, user, work);
        if (!wasSuccessful) return NotFound("SignatureCell Not Found");

        var ruleResponse = await _workService.CheckWorkCompleteThenRunRulesAsync(work.MatterId, work.SignatureSheetId);
        if (!ruleResponse.IsSuccess)
        {
            _logger.LogWarning(string.Join("\n", ruleResponse.ErrorMessages));
        }

        return Ok(dto);
    }


    [HttpGet("flagged/{matterId:int}")]
    [RoleCheck(RoleType.Manager, RoleType.Admin)]
    public async Task<ActionResult<GetWorkDTO>> GetNextFlaggedWork(int matterId)
    {
        var user = await _userRepository.GetByEmailAsync(_userContext.Username);
        if (user is null) return NotFound($"User {_userContext.Username} not found");
        var matter = await _matterRepository.GetByIdAsync(matterId);
        if (matter is null) return NotFound($"Matter {matterId} not found");

        var flaggedWork = await _workRepository.GetNextFlaggedWorkAsync(matterId, user.Id);
        if (flaggedWork is null) return null!;

        flaggedWork.UserId = user.Id;
        flaggedWork.WorkStatus = WorkStatus.Assigned;
        if (flaggedWork.AssignmentDate is null)
        {
            flaggedWork.AssignmentDate = DateTime.UtcNow;
        }

        await _workRepository.SaveChangesAsync();

        var workDto = await _workService.GetWorkDTOByWorkAsync(flaggedWork);
        return GetActionResultFromWorkDto(workDto);
    }

    [HttpGet("flagged/{matterId:int}/count")]
    [RoleCheck(RoleType.Manager, RoleType.Admin)]
    public async Task<ActionResult<int>> GetFlaggedWorkCount(int matterId)
    {
        var user = await _userRepository.GetByEmailAsync(_userContext.Username);
        if (user is null) return NotFound("User not found");
        var matter = await _matterRepository.GetByIdAsync(matterId);
        if (matter is null) return NotFound($"Matter {matterId} not found");

        var flaggedWorkCount =
            await _workRepository.GetCountByMatterIdAndExpressionAsync(matterId,
                w => w.WorkStatus == WorkStatus.Flagged);

        return Ok(flaggedWorkCount);
    }

    [HttpGet("unavailable/{matterId:int}/{sheetNumber:int}/count")]
    [RoleCheck(RoleType.Manager, RoleType.Admin)]
    public async Task<ActionResult<int>> GetUnavailableWorkCount(int matterId, int sheetNumber)
    {
        var serviceResult = await _workService.GetNextUnavailableWorkAsync(matterId, sheetNumber);
        if (!serviceResult.IsSuccess || serviceResult.Value == null)
        {
            return NotFound(serviceResult.ErrorMessages);
        }
        return Ok(serviceResult.Value.Works.Count);
    }

    [HttpGet("unavailable/{matterId:int}/{sheetNumber:int}")]
    public async Task<ActionResult<UnreleasedWork>> GetUnavailableWork(int matterId,
        int sheetNumber)
    {
        var serviceResult = await _workService.GetNextUnavailableWorkAsync(matterId, sheetNumber);
        if (!serviceResult.IsSuccess || serviceResult.Value is null)
        {
            return NotFound(serviceResult.ErrorMessages);
        }

        return Ok(serviceResult.Value);
    }


    [HttpPost("unavailable/{matterId:int}/{sheetNumber:int}/release")]
    public async Task<ActionResult<UnreleasedWork>> ReleaseUnavailableWork(int matterId,
        int sheetNumber, int[] workIds)
    {
        // This should give us the same work that they are releasing
        var serviceResult = await _workService.GetNextUnavailableWorkAsync(matterId, sheetNumber);
        if (!serviceResult.IsSuccess || serviceResult.Value is null)
        {
            return NotFound(serviceResult.ErrorMessages);
        }

        var unavailableWorks = serviceResult.Value.Works;
        var workIdsToWork = unavailableWorks.ToDictionary(w => w.Id, w => w);

        bool isRowWork = false;
        bool isFieldWork = false;
        foreach (var workId in workIds)
        {
            var success = workIdsToWork.TryGetValue(workId, out var work);
            if (!success || work == null)
            {
                continue;
            }

            if (work.RowNumber > 0) { isRowWork = true; }
            if (work.FieldNumber > 0) { isFieldWork = true; }

            work.WorkStatus = WorkStatus.None;
            _workRepository.SetModified(work);
            unavailableWorks.Remove(work);
        }

        // if we are marking some of the row work as done, then we have reviewed the sheet
        // so also mark the sheet task as done
        if (isRowWork)
        {
            var sheetWork = (await _workRepository.GetAllForMatterAndSheetNumberAsync(
                    matterId, sheetNumber,
                    w => w.WorkStatus == WorkStatus.Unavailable
                         && (w.Task.TaskType & TaskType.SheetReview) > 0)
                ).SingleOrDefault();
            if (sheetWork is not null)
            {
                sheetWork.WorkStatus = WorkStatus.None;
                _workRepository.SetModified(sheetWork);
            }
        }

        // if we marked all of this row work as done, then get ALL of the row work and mark it ALL done
        if (isRowWork && !unavailableWorks.Any())
        {
            var unavailableRowWorks = await _workRepository.GetAllForMatterAndSheetNumberAsync(
                matterId, sheetNumber, w => w.WorkStatus == WorkStatus.Unavailable && w.RowNumber > 0);
            foreach (var rowWork in unavailableRowWorks)
            {
                rowWork.WorkStatus = WorkStatus.None;
                _workRepository.SetModified(rowWork);
            }
        }

        await _workRepository.SaveChangesAsync();

        if (!isFieldWork) // if we just finished the field work, there is nothing left to do
        {
            ServiceResult<UnreleasedWork> returnServiceResult = await _workService.GetNextUnavailableWorkAsync(matterId, sheetNumber);
            return Ok(returnServiceResult.Value);
        }

        return Ok(new UnreleasedWork { UnreleasedWorkType = UnreleasedWorkType.None, Works = []});
    }

    [HttpGet("preview/{matterId:int}/{sheetNumber:int}/rows")]
    public async Task<ActionResult<List<GetWorkDTO>>> GetWorkPreviewForRows(int matterId, int sheetNumber)
    {
        List<Work> works;
        var serviceResult = await _workService.GetNextUnavailableWorkAsync(matterId, sheetNumber);
        if (serviceResult.IsSuccess && serviceResult.Value is not null
            && (serviceResult.Value.UnreleasedWorkType == UnreleasedWorkType.FirstLastRow ||
                serviceResult.Value.UnreleasedWorkType == UnreleasedWorkType.OtherRows))
        {
            works = serviceResult.Value.Works;
        }
        else
        {
            var allWork = await _workRepository.GetAllForMatterAndSheetNumberAsync(matterId, sheetNumber);
            var result = await _workService.GetFirstAndLastRowsWorkAsync(matterId, sheetNumber, allWork);
            if (!result.IsSuccess || result.Value is null)
            {
                return NotFound(result.ErrorMessages);
            }

            works = result.Value.Works;
        }

        if (!works.Any())
        {
            return NotFound(
                $"No work found for matter {matterId}, sheet number {sheetNumber}");
        }

        var dtos = works.Where(w => w.Task.TaskType == TaskType.SignatureTableColumn)
            .Select(async work =>
            {
                var workDto = await _workService.GetWorkDTOByWorkAsync(work);
                var dtoToReturn = _mapper.Map<GetWorkDTO>(workDto);
                if (workDto.ImageBytes is { Length: > 0 })
                {
                    dtoToReturn.Image = new FileContentResult(workDto.ImageBytes, "image/png");
                }

                return dtoToReturn;
            })
            .Select(t => t.Result)
            .ToList();
        return Ok(dtos);
    }

    [HttpGet("preview/{matterId:int}/{sheetNumber:int}/fields")]
    public async Task<ActionResult<List<GetWorkDTO>>> GetWorkPreviewForFields(int matterId, int sheetNumber)
    {
        var works = await _workRepository.GetAllForMatterAndSheetNumberAsync(
            matterId, sheetNumber, w => w.FieldNumber > 0);
        if (!works.Any())
        {
            return NotFound(
                $"No field work found for matter {matterId}, sheet number {sheetNumber}");
        }

        var dtos = works.Select(async work =>
            {
                var workDto = await _workService.GetWorkDTOByWorkAsync(work);
                var dtoToReturn = _mapper.Map<GetWorkDTO>(workDto);
                if (workDto.ImageBytes is { Length: > 0 })
                {
                    dtoToReturn.Image = new FileContentResult(workDto.ImageBytes, "image/png");
                }

                return dtoToReturn;
            })
            .Select(t => t.Result)
            .ToList();
        return Ok(dtos);
    }

    private ActionResult<GetWorkDTO> GetActionResultFromWorkDto(WorkDTO workDto)
    {
        if (workDto.ExecutionStatus == ExecutionStatus.Failed)
        {
            return NotFound(workDto.Message);
        }

        var dtoToReturn = _mapper.Map<GetWorkDTO>(workDto);
        if (workDto.ImageBytes is { Length: > 0 })
        {
            dtoToReturn.Image = new FileContentResult(workDto.ImageBytes, "image/png");
        }

        return Ok(dtoToReturn);
    }
}