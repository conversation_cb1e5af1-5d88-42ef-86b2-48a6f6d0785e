using Backend.Authentication;
using Backend.DTO.SignatureSheets;
using DataInterface.RepositoryInterfaces;
using DataInterface.ServiceInterfaces;
using Microsoft.AspNetCore.Mvc;
using Model.Authorization;
using Model.DTOs;
using Model.Rules;
using Model.SignatureSheets;
using Service;
using System.Net;
using System.Text;
using System.Text.Json;

namespace Backend.Controllers;

[Route("api/upload/signaturesheets")]
[RoleCheck(RoleType.Admin)]
public class UploadSignatureSheetsController : BaseApiController
{
    private readonly IBackgroundOperationRepository _backgroundOperationRepository;
    private readonly IDataTransformationStepStartRepository _dataTransformationStepStartRepository;
    private readonly IDataTransformationStepResultRepository _dataTransformationStepResultRepository;
    private readonly IFileService _fileService;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IMatterRepository _matterRepository;
    private readonly IMatterVariableRepository _matterVariableRepository;
    private readonly IRuleRepository _ruleRepository;
    private readonly IServiceProvider _serviceProvider;
    private readonly ISignatureSheetProcessingRepository _signatureSheetProcessingRepository;
    private readonly ISignatureSheetUploadRepository _signatureSheetUploadRepository;
    private readonly ITaskRepository _taskRepository;
    private readonly ITemplateRepository _templateRepository;
    private readonly ITemplatePageRepository _templatePageRepository;
    private readonly InvalidSheetService _invalidSheetService;
    private readonly QueueService _queueService;
    private readonly RuleGenerationServiceFactory _ruleGenerationServiceFactory;
    private readonly SheetNumberService _sheetNumberService;
    private readonly SignatureSheetUploadService _signatureSheetUploadService;
    private readonly TaskGenerationService _taskGenerationService;
    private readonly UrlService _urlService;

    public UploadSignatureSheetsController(
        IBackgroundOperationRepository backgroundOperationRepository,
        IDataTransformationStepStartRepository dataTransformationStepStartRepository,
        IDataTransformationStepResultRepository dataTransformationStepResultRepository,
        IFileService fileService,
        IHttpClientFactory httpClientFactory,
        ILogger<BaseApiController> logger,
        IMatterRepository matterRepository,
        IMatterVariableRepository matterVariableRepository,
        IRuleRepository ruleRepository,
        IServiceProvider serviceProvider,
        ISignatureSheetProcessingRepository signatureSheetProcessingRepository,
        ISignatureSheetUploadRepository signatureSheetUploadRepository,
        ITaskRepository taskRepository,
        ITemplateRepository templateRepository,
        ITemplatePageRepository templatePageRepository,
        InvalidSheetService invalidSheetService,
        QueueService queueService,
        RuleGenerationServiceFactory ruleGenerationServiceFactory,
        SheetNumberService sheetNumberService,
        SignatureSheetUploadService signatureSheetUploadService,
        TaskGenerationService taskGenerationService,
        UrlService urlService)
        : base(logger)
    {
        _backgroundOperationRepository = backgroundOperationRepository;
        _dataTransformationStepStartRepository = dataTransformationStepStartRepository;
        _dataTransformationStepResultRepository = dataTransformationStepResultRepository;
        _fileService = fileService;
        _httpClientFactory = httpClientFactory;
        _matterRepository = matterRepository;
        _matterVariableRepository = matterVariableRepository;
        _queueService = queueService;
        _ruleGenerationServiceFactory = ruleGenerationServiceFactory;
        _sheetNumberService = sheetNumberService;
        _ruleRepository = ruleRepository;
        _serviceProvider = serviceProvider;
        _signatureSheetProcessingRepository = signatureSheetProcessingRepository;
        _signatureSheetUploadRepository = signatureSheetUploadRepository;
        _signatureSheetUploadService = signatureSheetUploadService;
        _taskRepository = taskRepository;
        _taskGenerationService = taskGenerationService;
        _templateRepository = templateRepository;
        _templatePageRepository = templatePageRepository;
        _invalidSheetService = invalidSheetService;
        _urlService = urlService;
    }

    [HttpPost("byuser")]
    [DisableRequestSizeLimit]
    public async Task<ActionResult> UploadSignatureSheetsByUser(IFormFile file, int templateId, int matterId,
        CancellationToken ct,
        [FromServices] IServiceScopeFactory serviceScopeFactory)
    {
        var templatePages = await _templatePageRepository.GetByTemplateIdAsync(templateId);
        if (templatePages.Count == 0)
        {
            return BadRequest($"Template {templateId} does not exist");
        }

        var matter = await _matterRepository.GetByIdAsync(matterId);
        if (matter == null)
        {
            return BadRequest($"Matter {matterId} does not exist");
        }

        bool isExistingUpload = await _signatureSheetUploadService.CheckExistingUploadAsync(matterId, file.FileName);
        if (isExistingUpload)
        {
            return BadRequest($"Upload {file.FileName} already exists");
        }


        SignatureSheetUpload signatureSheetUpload =
            await _signatureSheetUploadService.CreateSignatureSheetUploadAsync(file.FileName, templateId, matterId,
                file.FileName);

        var backgroundOperation = new BackgroundOperation
        {
            ExecutionStatus = ExecutionStatus.Running,
            MatterId = matterId,
            OperationType = BackgroundOperationType.UploadSignatureSheets,
        };
        _backgroundOperationRepository.Add(backgroundOperation);
        await _backgroundOperationRepository.SaveChangesAsync();
        
        var memStream = new MemoryStream();
        await file.CopyToAsync(memStream, ct);
        memStream.Position = 0;

        _ = Task.Run(async () =>
        {
            using var scope = serviceScopeFactory.CreateScope();
            var helper = new UploadSignatureSheetsControllerHelper(scope);
            var backgroundOperationRepository =
                scope.ServiceProvider.GetRequiredService<IBackgroundOperationRepository>();
            try
            {
                memStream = await helper.GetSignatureSheetFileStream(memStream, file.FileName, templateId,
                    templatePages);
                var result = await helper.ProcessSignatureSheetFromUser(memStream, file.FileName, templateId, matterId,
                    signatureSheetUpload, ct);
                if (result.IsSuccess && result.Value != null && result.Value.Any())
                {
                    memStream.Position = 0;
                    var newFilename = $"{templateId}/{result.Value.First():D6}.pdf";
                    string? error =
                        await _fileService.SaveFileStreamAsync($"matter{matterId}", newFilename, memStream, false, ct);
                    if (error != null)
                    {
                        backgroundOperation.ExecutionStatus = ExecutionStatus.Failed;
                        backgroundOperation.Message = error;
                        return;
                    }
                }

                backgroundOperation.Message = JsonSerializer.Serialize(result.Value);
                backgroundOperation.ExecutionStatus = ExecutionStatus.Succeeded;
            }
            catch (Exception ex)
            {
                backgroundOperation.ExecutionStatus = ExecutionStatus.Failed;
                backgroundOperation.Message = ex.Message;
            }
            finally
            {
                backgroundOperationRepository.SetModified(backgroundOperation);
                await backgroundOperationRepository.SaveChangesAsync();
            }
        });

        return Ok(backgroundOperation.Id);
    }

    [HttpPost("{matterId}/requeue/uncompleted")]
    public async Task<ActionResult> PostRequeueUncompleted(int matterId, CancellationToken ct)
    {
        var stuckSteps =
            await _dataTransformationStepStartRepository.GetStuckStepsWithNoResult(matterId,
                "SplitPdfsBlobTriggerFunction");
        if (!stuckSteps.Any())
        {
            return NoContent();
        }

        foreach (var step in stuckSteps)
        {
            _dataTransformationStepStartRepository.Remove(step);
        }

        await _dataTransformationStepStartRepository.SaveChangesAsync();

        foreach (var step in stuckSteps)
        {
            _logger.LogInformation($"Enqueuing {step.Input}");
            await _queueService.EnqueueDownloadedFileAsync("downloaded/" + step.Input);
        }

        return Ok();
    }

    [HttpGet("{matterId}/history")]
    public async Task<ActionResult<List<UploadHistoryDTO>>> GetUploadHistory(int matterId, CancellationToken ct)
    {
        var existingUploads = await _signatureSheetUploadRepository.GetAllByMatterIdAsync(matterId);
        var startedSteps = await _dataTransformationStepStartRepository.GetByMatterIdAsync(matterId);
        var stepResults = await _dataTransformationStepResultRepository.GetByMatterIdAsync(matterId);

        // Check for null or empty results
        if (!existingUploads.Any())
        {
            return Ok(new List<UploadHistoryDTO>()); // Return an empty list if no uploads exist
        }

        // Get active templates async
        var templates = await _templateRepository.GetAllActiveTemplatesAsync();

        // Convert to dict for lookup
        var templateDictionary = templates.ToDictionary(t => t.Id, t => t.Name);

        var uploadHistoryDTOs = existingUploads.Select(upload =>
        {
            var status = DataTransformationStepService.GetDataTransformationStatus(upload.Id,
                startedSteps.Where(step => step.SignatureSheetUploadId == upload.Id).ToList(),
                stepResults.Where(step => step.SignatureSheetUploadId == upload.Id).ToList()
            );
            return new UploadHistoryDTO
            {
                SignatureSheetUploadId = upload.Id,
                UploadUrl = upload.DownloadUrl,
                UploadedBy = upload.UploadedBy,
                UploadedOn = upload.UploadedOn.ToString(),
                TemplateName = templateDictionary.TryGetValue(upload.TemplateId, out var templateName)
                    ? templateName
                    : "Unknown Template",
                Status = status,
            };
        }).ToList();

        return Ok(uploadHistoryDTOs);
    }

    [HttpPost("start")] // When the user clicks the upload start button
    public async Task<ActionResult> StartUpload(
        CreateSignatureSheetUploadDTO createSignatureSheetUploadDto,
        CancellationToken ct)
    {
        var (matterId, templateId, downloadUrl) = createSignatureSheetUploadDto;

        if (string.IsNullOrEmpty(downloadUrl))
        {
            return BadRequest($"URL cannot be empty.");
        }

        if (!Uri.TryCreate(downloadUrl, UriKind.Absolute, out var uri))
        {
            return BadRequest($"Invalid URL.");
        }

        var template = await _templateRepository.GetByIdAsync(templateId);
        if (template == null)
        {
            return BadRequest($"Please select a template");
        }

        var matter = await _matterRepository.GetByIdAsync(matterId);
        if (matter == null)
        {
            return BadRequest($"Matter {matterId} does not exist");
        }

        bool isUrlAlreadyDownloaded =
            await _signatureSheetUploadRepository.CheckUrlAlreadyDownloadedAsync(matterId, downloadUrl);
        if (isUrlAlreadyDownloaded)
        {
            return StatusCode(409, "Download Url already exists.");
        }

        if (!_urlService.IsSasUrl(downloadUrl) && !_urlService.IsSoSUrl(downloadUrl))
        {
            return BadRequest($"URL is neither from the Secretary of State nor has a SAS query string.");
        }

        if (_urlService.IsSoSUrl(downloadUrl))
        {
            downloadUrl = _urlService.TransformSoSUrl(downloadUrl);
        }

        var existingUploads = await _signatureSheetUploadRepository.GetAllByMatterIdAsync(matterId);
        var fileNames = existingUploads.Select(u => u.FileName);
        if (fileNames.Contains(downloadUrl))
        {
            return BadRequest($"Upload {downloadUrl} already exists");
        }

        SignatureSheetUpload signatureSheetUpload =
            await _signatureSheetUploadService.CreateSignatureSheetUploadAsync(downloadUrl, templateId, matterId);

        var templatePages = await _templatePageRepository.GetByTemplateIdAsync(templateId);
        if (templatePages.Count == 0)
        {
            return BadRequest($"Template {templateId} pages do not exist");
        }

        // Check to see if we have already created Tasks for this Template, if not create them
        if (!matter.AreTasksCreated)
        {
            var tasks = await _taskRepository.GetAllByTemplateAndMatterId(templateId, matterId);
            if (tasks.Count == 0) // if it is not what happened?  Half of the tasks were created?
            {
                await _taskGenerationService.CreateTasksForTemplateAndMatterAsync(template, matter);
                await _taskRepository.SaveChangesAsync();
            }

            matter.AreTasksCreated = true;
            _matterRepository.SetModified(matter);
            await _matterRepository.SaveChangesAsync();
        }

        var ruleGenerationService = _ruleGenerationServiceFactory.Create(matter.Type);
        var created = await ruleGenerationService.CreateIfNecessaryAsync(matterId, templateId);
        if (created)
        {
            var matterVariables = await _ruleRepository.GetAllMatterVariablesInRulesAsync(matter.Id);
            await _matterVariableRepository.AddMatterVariablesIfNecessary(matter.Id, matterVariables);
            await _matterVariableRepository.SaveChangesAsync();
        }

        var httpClient = _httpClientFactory.CreateClient("IngressFunctionAppDownload");

        var downloadDto =
            new SignatureSheetDownloadDTO(signatureSheetUpload.Id, matterId, templateId, downloadUrl);
        var content = new StringContent(JsonSerializer.Serialize(downloadDto), Encoding.UTF8,
            "application/json");
        var response = await httpClient.PostAsync("api/DownloadFilesToAzureFunction", content, ct);
        if (!response.IsSuccessStatusCode)
        {
            return new ContentResult
            {
                StatusCode = (int)HttpStatusCode.InternalServerError,
                Content = await response.Content.ReadAsStringAsync(ct),
                ContentType = "text/plain"
            };
        }

        return Ok(signatureSheetUpload.Id);
    }

    // This is called from the data pipeline
    [HttpPost("pipeline")]
    public async Task<ActionResult> UploadSignatureSheetsFromPipeline([FromBody] UploadSignatureSheetDTO uploadDto,
        CancellationToken ct)
    {
        if (string.IsNullOrEmpty(uploadDto.FilePath))
        {
            return BadRequest($"File path cannot be empty.");
        }

        var signatureSheetUpload =
            await _signatureSheetUploadRepository.GetByIdAsync(uploadDto.SignatureSheetUploadId);
        if (signatureSheetUpload == null)
        {
            return BadRequest($"Upload {uploadDto.SignatureSheetUploadId} does not exist");
        }

        var template = await _templateRepository.GetByIdAsync(signatureSheetUpload.TemplateId);
        if (template == null)
        {
            return BadRequest($"Template {signatureSheetUpload.TemplateId} does not exist");
        }

        var matter = await _matterRepository.GetByIdAsync(signatureSheetUpload.MatterId);
        if (matter == null)
        {
            return BadRequest($"Matter {signatureSheetUpload.MatterId} does not exist");
        }

        var sheetNumber = _sheetNumberService.GetSheetNumberFromFilename(uploadDto.FilePath);
        if (sheetNumber == null || sheetNumber == 0)
        {
            return BadRequest($"Sheet number not found in file name {uploadDto.FilePath}");
        }

        var wasAddSuccessful = await _signatureSheetProcessingRepository.TryAdd(matter.Id, signatureSheetUpload.Id, sheetNumber.Value);
        if (!wasAddSuccessful)
        {
            return BadRequest($"Sheet number {sheetNumber} is already being processed");
        }

        // we create a scope even though we are not running a background operation here
        using (var scope = _serviceProvider.CreateScope())
        {
            var helper = new UploadSignatureSheetsControllerHelper(scope);
            var result = await helper.ProcessSignatureSheetFromPipeline(uploadDto.FilePath,
                sheetNumber.Value, signatureSheetUpload, ct);
            if (!result.IsSuccess)
            {
                await _invalidSheetService.AddInvalidSheetAsync(signatureSheetUpload, uploadDto.FilePath,
                    sheetNumber.Value);
                result.ErrorMessages.Insert(0, "ProcessSignatureSheetFromPipeline failed");
                return BadRequest(string.Join("\n", result.ErrorMessages));
            }
        }

        return Ok();
    }

    [HttpDelete("{uploadId}")]
    public async Task<ActionResult> DeleteLatestUpload(int uploadId, CancellationToken ct)
    {
        await _signatureSheetUploadRepository.DeleteUploadByIdAsync(uploadId);
        await _signatureSheetUploadRepository.SaveChangesAsync();

        return Ok();
    }

    [HttpGet("{matterId}/validate/{newTemplateId}")]
    public async Task<ActionResult<bool>> IsValidAgainstExistingTemplate(int matterId, int newTemplateId)
    {
        var isValid = await _signatureSheetUploadRepository.IsValidAgainstExistingTemplate(matterId, newTemplateId);
        return Ok(isValid);
    }
}