﻿using AutoMapper;
using Backend.Authentication;
using Backend.DTO.StaffGroups;
using Backend.DTO.Users;
using DataInterface.RepositoryInterfaces;
using Microsoft.AspNetCore.Mvc;
using Model.Authorization;
using Model.Workflow;

namespace Backend.Controllers;

[Route("api")] // matters/{matterId}/staffgroups
[RoleCheck(RoleType.Admin, RoleType.Manager)]
public class MatterStaffGroupsController : BaseApiController
{
    private readonly IStaffGroupRepository _staffGroupRepository;
    private readonly IGroupTaskAssignmentRepository _groupTaskAssignmentRepository;
    private readonly IUserGroupAssignmentRepository _userGroupAssignmentRepository;
    private readonly IMapper _mapper;

    public MatterStaffGroupsController(
        IStaffGroupRepository staffGroupRepository,
        IGroupTaskAssignmentRepository groupTaskAssignmentRepository,
        IUserGroupAssignmentRepository userGroupAssignmentRepository,
        <PERSON><PERSON><PERSON><PERSON> mapper,
        ILogger<BaseApiController> logger)
        : base(logger)
    {
        _staffGroupRepository = staffGroupRepository;
        _groupTaskAssignmentRepository = groupTaskAssignmentRepository;
        _userGroupAssignmentRepository = userGroupAssignmentRepository;
        _mapper = mapper;
    }

    [HttpGet("matters/{matterId}/staffgroups")]
    public async Task<ActionResult<IEnumerable<StaffGroupDTO>>> GetStaffGroupsByMatterId(int matterId)
    {
        var userGroups = await _userGroupAssignmentRepository.GetAllByMatterIdAsync(matterId);
        var staffGroups = userGroups.Select(x => new StaffGroupDTO { Name = x.StaffGroup.Name, Id = x.StaffGroupId });

        return Ok(staffGroups);
    }

    [HttpGet("matters/{matterId}/staffgroups/{staffGroupId}/users")]
    public async Task<ActionResult<List<UserDTO>>> GetUsersByStaffGroup(int matterId, int staffGroupId,
        bool assigned = true)
    {
        List<User> users;
        if (assigned)
        {
            users = await _userGroupAssignmentRepository.GetUsersByMatterAndStaffGroup(matterId, staffGroupId);
        }
        else
        {
            users = await _userGroupAssignmentRepository
                .GetUnassignedUsersByStaffGroupAndMatter(matterId, staffGroupId);
        }

        return Ok(_mapper.Map<List<UserDTO>>(users));
    }

    [HttpGet("matters/{matterId}/staffgroups/{staffGroupId}/users/stats")]
    public async Task<ActionResult<List<UserStatsDTO>>> GetUsersStatsByStaffGroup(int matterId, int staffGroupId)
    {
        var users = await _userGroupAssignmentRepository.GetUserStatusAsync(matterId, staffGroupId);
        var response = new List<UserStatsDTO>();
        foreach (var user in users)
        {
            var workerSpeed = user.TotalWorkCount > 0 ? (int)(user.TotalSecondsWorked / user.TotalWorkCount) : 0;
            var hoursWorked = (float)(user.TotalSecondsWorked / 3600m);
            var totalInReview = user.TotalFlaggedCount;

            response.Add(new UserStatsDTO
            {
                Id = user.Id,
                FullName = user.FullName,
                Email = user.Email,
                Deficiencies = user.TotalDeficiencyCount,
                WorkerSpeed = workerSpeed,
                HoursWorked = (float)Math.Round(hoursWorked, 1),
                InReview = totalInReview
            });
        }

        return Ok(response);
    }

    [HttpPost("matters/{matterId}/staffgroups/{staffGroupId}/users")]
    public async Task<ActionResult<int>> AddUsersToStaffGroup(int matterId, int staffGroupId,
        [FromBody] List<int> userIds)
    {
        foreach (var userId in userIds)
        {
            var userGroupAssignment = new UserGroupAssignment()
                { MatterId = matterId, StaffGroupId = staffGroupId, UserId = userId };
            var preExisting =
                await _userGroupAssignmentRepository.GetByMatterStaffGroupAndUserId(matterId, staffGroupId, userId);
            if (preExisting is null)
            {
                _userGroupAssignmentRepository.Add(userGroupAssignment);
            }
        }

        await _userGroupAssignmentRepository.SaveChangesAsync();

        return CreatedAtAction(nameof(AddUsersToStaffGroup), null);
    }

    [HttpDelete("matters/{matterId}/staffgroups/{staffGroupId}/users/{userId}")]
    public async Task<ActionResult> DeleteUserByStaffGroupId(int matterId, int staffGroupId, int userId)
    {
        var userGroupAssignments =
            await _userGroupAssignmentRepository.GetByMatterStaffGroupAndUserId(matterId, staffGroupId, userId);
        if (userGroupAssignments is null) return BadRequest("UserGroupAssignment Not Found");

        _userGroupAssignmentRepository.Remove(userGroupAssignments);
        await _userGroupAssignmentRepository.SaveChangesAsync();

        return Ok();
    }
}