using DataInterface.RepositoryInterfaces;
using DataInterface.ServiceInterfaces;
using Model.SignatureSheets;
using Model.Templates;
using Service;

namespace Backend.Controllers;

public class UploadSignatureSheetsControllerHelper
{
    private readonly IDataProcessingLogRepository _dataProcessingLogRepository;
    private readonly IFileService _fileService;
    private readonly IMatterRepository _matterRepository;
    private readonly IMatterVariableRepository _matterVariableRepository;
    private readonly IRuleRepository _ruleRepository;
    private readonly ITaskRepository _taskRepository;
    private readonly ITemplateRepository _templateRepository;
    private readonly ITemplateFormLineRepository _templateFormLineRepository;
    private readonly ITemplatePageRepository _templatePageRepository;
    private readonly ITranscribableFieldRepository _transcribableFieldRepository;
    private readonly PdfManipulationService _pdfManipulationService;
    private readonly RuleGenerationServiceFactory _ruleGenerationServiceFactory;
    private readonly SignatureSheetGeometryService _signatureSheetGeometryService;
    private readonly SignatureSheetHierarchyService _signatureSheetHierarchyService;
    private readonly SimpleFormRecognizerService _formRecognizerService;
    private readonly TaskGenerationService _taskGenerationService;
    private readonly WorkGenerationService _workGenerationService;

    public UploadSignatureSheetsControllerHelper(IServiceScope scope)
    {
        _dataProcessingLogRepository = scope.ServiceProvider.GetRequiredService<IDataProcessingLogRepository>();
        _fileService = scope.ServiceProvider.GetRequiredService<IFileService>();
        _formRecognizerService = scope.ServiceProvider.GetRequiredService<SimpleFormRecognizerService>();
        _matterRepository = scope.ServiceProvider.GetRequiredService<IMatterRepository>();
        _matterVariableRepository = scope.ServiceProvider.GetRequiredService<IMatterVariableRepository>();
        _ruleRepository = scope.ServiceProvider.GetRequiredService<IRuleRepository>();
        _ruleGenerationServiceFactory =
            scope.ServiceProvider.GetRequiredService<RuleGenerationServiceFactory>();

        _signatureSheetGeometryService = scope.ServiceProvider.GetRequiredService<SignatureSheetGeometryService>();
        _signatureSheetHierarchyService = scope.ServiceProvider.GetRequiredService<SignatureSheetHierarchyService>();
        _taskRepository = scope.ServiceProvider.GetRequiredService<ITaskRepository>();
        _taskGenerationService = scope.ServiceProvider.GetRequiredService<TaskGenerationService>();
        _templateRepository = scope.ServiceProvider.GetRequiredService<ITemplateRepository>();
        _templateFormLineRepository = scope.ServiceProvider.GetRequiredService<ITemplateFormLineRepository>();
        _templatePageRepository = scope.ServiceProvider.GetRequiredService<ITemplatePageRepository>();
        _transcribableFieldRepository = scope.ServiceProvider.GetRequiredService<ITranscribableFieldRepository>();
        _workGenerationService = scope.ServiceProvider.GetRequiredService<WorkGenerationService>();
        _pdfManipulationService = scope.ServiceProvider.GetRequiredService<PdfManipulationService>();
    }

    // This one was called from a direct upload
    internal async Task<ServiceResult<int[]>> ProcessSignatureSheetFromUser(
        Stream memStream,
        string fileName, int templateId, int matterId,
        SignatureSheetUpload signatureSheetUpload,
        CancellationToken ct)
    {
        List<int> sheetNumbers = [];
        int uploadId = signatureSheetUpload.Id;
        try
        {
            await LogSheetOpAsync(uploadId, "Process SignatureSheet started");

            var template = await _templateRepository.GetByIdAsync(templateId);
            if (template == null)
            {
                throw new Exception($"Template with id {templateId} not found");
            }

            await LogSheetOpAsync(uploadId, $"Got the template {templateId} for this SignatureSheet");

            var matter = await _matterRepository.GetByIdAsync(matterId);
            if (matter == null)
            {
                throw new Exception($"Matter with id {matterId} not found");
            }

            // Check to see if we have already created Tasks for this Template, if not create them
            if (!matter.AreTasksCreated)
            {
                var tasks = await _taskRepository.GetAllByTemplateAndMatterId(templateId, matterId);
                if (tasks.Count == 0) // if it is not what happened?  Half of the tasks were created?
                {
                    await _taskGenerationService.CreateTasksForTemplateAndMatterAsync(template, matter);
                    await LogSheetOpAsync(uploadId, $"Created tasks for the template {template.Name}");
                    await _taskRepository.SaveChangesAsync();
                }

                matter.AreTasksCreated = true;
                _matterRepository.SetModified(matter);
                await _matterRepository.SaveChangesAsync();
            }

            // If we didn't have Tasks for this template then we don't have Rules either, so add them
            var ruleGenerationService = _ruleGenerationServiceFactory.Create(matter.Type);
            var created = await ruleGenerationService.CreateIfNecessaryAsync(matterId, templateId);
            if (created)
            {
                var matterVariables = await _ruleRepository.GetAllMatterVariablesInRulesAsync(matterId);
                await _matterVariableRepository.AddMatterVariablesIfNecessary(matterId, matterVariables);
                await LogSheetOpAsync(uploadId,
                    $"Created rules for the template {template.Name}");
            }

            await LogSheetOpAsync(uploadId, $"Starting Save SignatureSheet Hierarchy");
            var result =
                await _signatureSheetHierarchyService.SaveSignatureSheetHierarchyAsync(memStream, fileName,
                    signatureSheetUpload);
            if (!result.IsSuccess || result.Value == null)
            {
                return ServiceResult<int[]>.Failed(result.ErrorMessages.ToArray());
            }

            var signatureSheets = result.Value;
            foreach (var sheet in signatureSheets)
            {
                sheetNumbers.Add(sheet.SheetNumber);
                var saveWorkResult =
                    await _workGenerationService.SaveWorkFromSignatureSheet(templateId, matterId, sheet);
                if (!saveWorkResult.IsSuccess)
                {
                    return ServiceResult<int[]>.Failed(saveWorkResult.ErrorMessages.ToArray());
                }
            }

            await LogSheetOpAsync(uploadId, $"Processing finished for file {fileName}");
        }
        catch (Exception ex)
        {
            await LogSheetOpAsync(uploadId, ex.ToString());
        }

        return ServiceResult<int[]>.Succeeded(sheetNumbers.ToArray());
    }

    // This one is called from the Data Pipeline
    internal async Task<ServiceResult<bool>> ProcessSignatureSheetFromPipeline(string filePath, int sheetNumber,
        SignatureSheetUpload upload, CancellationToken ct)
    {
        var matter = await _matterRepository.GetByIdAsync(upload.MatterId);
        if (matter == null)
        {
            return ServiceResult<bool>.Failed($"No matter with Id {upload.MatterId}");
        }

        var template = await _templateRepository.GetByIdAsync(upload.TemplateId);
        if (template == null)
        {
            return ServiceResult<bool>.Failed($"No template with Id {upload.TemplateId}");
        }

        //var templatePages = await _templatePageRepository.GetByTemplateIdAsync(upload.TemplateId);
        var transcribableFields = await _transcribableFieldRepository.GetNamedByTemplateIdAsync(upload.TemplateId);

        var result = new ServiceResult<bool>();
        string[] uriPathParts = filePath.Trim('/').Split('/');
        var containerName = uriPathParts.First(); // Most likely "unverified"
        var restOfPath = string.Join('/', uriPathParts.Skip(1));

        var stream = await _fileService.GetFileStreamAsync(containerName, restOfPath);
        if (stream == null)
        {
            return ServiceResult<bool>.Failed($"Unable to get the stream {containerName}/{restOfPath}");
        }

        using var memoryStream = new MemoryStream();
        await stream.CopyToAsync(memoryStream, ct);
        memoryStream.Position = 0;
        var formResults = await _formRecognizerService.RecognizeFormAsync(matter, memoryStream, isTemplate: false);
        if (formResults == null)
        {
            return ServiceResult<bool>.Failed($"Form recognizer returned null");
        }

        var templateFormLines = await _templateFormLineRepository.GetAllByTemplateIdAsync(upload.TemplateId);
        var transformResult = _signatureSheetGeometryService.Transform(
            matter.Type,
            template.Pages.ToDictionary(tp => tp.PageNumber),
            transcribableFields,
            template.Tables[0], formResults, upload, 0, sheetNumber, templateFormLines);
        if (!transformResult.IsSuccess || transformResult.Value == null)
        {
            return ServiceResult<bool>.Failed(transformResult.ErrorMessages.ToArray());
        }

        var sheetResults = transformResult.Value;
        bool rowsMatch = template.Tables[0].NumberOfRows == sheetResults.SignatureRows.Count;
        if (!rowsMatch)
        {
            result.ErrorMessages.Add(
                $"Rows do not match {template.Tables[0].NumberOfRows} != {sheetResults.SignatureRows.Count}");
        }

        bool colsMatch = template.Tables[0].NumberOfCols == sheetResults.SignatureColumns.Count;
        if (!colsMatch)
        {
            result.ErrorMessages.Add(
                $"Cols do not match {template.Tables[0].NumberOfCols} != {sheetResults.SignatureColumns.Count}");
        }

        result.Status = rowsMatch && colsMatch ? ResultStatus.Success : ResultStatus.Failure;
        if (!result.IsSuccess)
        {
            return result;
        }

        var newContainerName = $"matter{matter.Id}";

        var newFilename = $"{template.Id}/{sheetNumber:D6}.pdf";
        await _fileService.RenameFileStreamAsync(containerName, restOfPath, newFilename, newContainerName);
        sheetResults.SignatureSheet.FileName = newFilename;
        sheetResults.SignatureSheet.SheetNumber = sheetNumber;

        var signatureSheet = await _signatureSheetHierarchyService.SaveSingleSheetHierarchyAsync(
            sheetResults,
            template.Pages,
            upload
        );
        await _workGenerationService.SaveWorkFromSignatureSheet(template.Id, matter.Id, signatureSheet);

        return ServiceResult<bool>.Succeeded(true);
    }

    internal async Task<MemoryStream> GetSignatureSheetFileStream(MemoryStream memStream, string fileName,
        int templateId,
        List<TemplatePage> templatePages)
    {
        ArgumentNullException.ThrowIfNull(memStream, nameof(memStream));
        ArgumentException.ThrowIfNullOrWhiteSpace(fileName, nameof(fileName));

        if (fileName.EndsWith(".pdf"))
        {
            var template = await _templateRepository.GetByIdAsync(templateId);
            var supportedPageSize = (template?.PageSize == null || template?.PageSize == SupportedPageSize.Unknown)
                ? SupportedPageSize.Legal
                : template?.PageSize.Value;
            memStream = await _pdfManipulationService.AdjustPdfOrientationAsync(memStream, supportedPageSize);
            int pages = _pdfManipulationService.GetNumberOfPages(memStream);
            var isValidNumberOfPages = (pages % templatePages.Count) == 0;
            if (!isValidNumberOfPages)
            {
                throw new ArgumentException("Invalid number of pages");
            }
        }

        memStream.Position = 0; // Reset the position after copying
        return memStream;
    }

    private async Task LogSheetOpAsync(int uploadId, string message)
    {
        await _dataProcessingLogRepository.AddSignatureSheetOperationAsync(uploadId, message);
    }
}