using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Backend.Controllers;

[Route("api/[controller]")]
public class HealthCheckController : BaseApiController
{
    public HealthCheckController(ILogger<BaseApiController> logger) : base(logger)
    {
    }

    [HttpGet]
    [AllowAnonymous]
    public IActionResult Get()
    {
        _logger.LogInformation("HealthCheckController Get method called.");
        return Ok();
    }
}