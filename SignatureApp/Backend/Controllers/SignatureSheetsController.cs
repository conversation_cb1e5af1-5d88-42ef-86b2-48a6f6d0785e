using System.Drawing;
using AutoMapper;
using Backend.Authentication;
using Backend.DTO.SignatureSheets;
using DataInterface.RepositoryInterfaces;
using DataInterface.ServiceInterfaces;
using Microsoft.AspNetCore.Mvc;
using Model.Authorization;
using Model.SignatureSheets;
using Service;
using Service.ServiceModels;

namespace Backend.Controllers;

[Route("api/[controller]")]
public class SignatureSheetsController : BaseApiController
{
    private readonly IFileService _fileService;
    private readonly IMapper _mapper;
    private readonly IMatterRepository _matterRepository;
    private readonly ISignatureSheetCellRepository _signatureSheetCellRepository;
    private readonly ISignatureSheetColumnRepository _signatureSheetColumnRepository;
    private readonly ISignatureSheetFormLineRepository _signatureSheetFormLineRepository;
    private readonly ISignatureSheetRepository _signatureSheetRepository;
    private readonly ISignatureSheetRowRepository _signatureSheetRowRepository;
    private readonly ISignatureSheetTableRepository _signatureSheetTableRepository;
    private readonly ITemplateRepository _templateRepository;
    private readonly ITemplateSignatureColumnRepository _templateSignatureColumnRepository;
    private readonly PdfManipulationService _pdfManipulationService;
    private readonly SignatureSheetGeometryService _signatureSheetGeometryService;

    public SignatureSheetsController(
        IFileService fileService,
        ILogger<BaseApiController> logger,
        IMapper mapper,
        IMatterRepository matterRepository,
        ISignatureSheetCellRepository signatureSheetCellRepository,
        ISignatureSheetColumnRepository signatureSheetColumnRepository,
        ISignatureSheetFormLineRepository signatureSheetFormLineRepository,
        ISignatureSheetRepository signatureSheetRepository,
        ISignatureSheetRowRepository signatureSheetRowRepository,
        ISignatureSheetTableRepository signatureSheetTableRepository,
        ITemplateRepository templateRepository,
        ITemplateSignatureColumnRepository templateSignatureColumnRepository,
        PdfManipulationService pdfManipulationService,
        SignatureSheetGeometryService signatureSheetGeometryService)
        : base(logger)
    {
        _fileService = fileService;
        _mapper = mapper;
        _matterRepository = matterRepository;
        _signatureSheetCellRepository = signatureSheetCellRepository;
        _signatureSheetColumnRepository = signatureSheetColumnRepository;
        _signatureSheetFormLineRepository = signatureSheetFormLineRepository;
        _signatureSheetRepository = signatureSheetRepository;
        _signatureSheetRowRepository = signatureSheetRowRepository;
        _signatureSheetTableRepository = signatureSheetTableRepository;
        _templateRepository = templateRepository;
        _templateSignatureColumnRepository = templateSignatureColumnRepository;
        _pdfManipulationService = pdfManipulationService;
        _signatureSheetGeometryService = signatureSheetGeometryService;
    }

    [HttpGet("{matterId}/{sheetNumber}")]
    [RoleCheck(RoleType.Admin, RoleType.Manager, RoleType.Reviewer)]
    public async Task<ActionResult<SignatureSheetTableDTO>> GetByMatterAndSheetNumber(int matterId, int sheetNumber)
    {
        var signatureSheetTable =
            await _signatureSheetTableRepository.GetByMatterAndSheetNumberAsync(matterId, sheetNumber);
        if (signatureSheetTable?.SignatureSheet == null)
        {
            return NotFound($"SignatureSheet {sheetNumber} not found in matter {matterId}");
        }

        var templateId = signatureSheetTable.SignatureSheet.TemplateId;
        var template = await _templateRepository.GetByIdAsync(templateId);
        if (template?.PageSize == null)
        {
            return NotFound($"SignatureSheet {sheetNumber} in matter {matterId} has no template {templateId}");
        }

        var signatureSheetTableDto = _mapper.Map<SignatureSheetTableDTO>(signatureSheetTable);
        var page = signatureSheetTable.SignatureSheet.Pages[0];
        signatureSheetTableDto.PageNumber = page.PageNumber;
        signatureSheetTableDto.ImageAspectRatio = page.Width / page.Height;
        signatureSheetTableDto.TemplateId = templateId;
        var pdfStream = await _fileService.GetFileStreamAsync($"matter{matterId}",
            $"{templateId}/{sheetNumber:D6}.pdf");
        ArgumentNullException.ThrowIfNull(pdfStream);

        var imageBytes = _pdfManipulationService.ConvertPdfPageToImage(pdfStream, template.PageSize.Value);

        signatureSheetTableDto.SheetImage = new FileContentResult(imageBytes, "image/png");
        return Ok(signatureSheetTableDto);
    }

    [HttpPost("{matterId}/{sheetNumber}")]
    [RoleCheck(RoleType.Admin, RoleType.Manager, RoleType.Reviewer)]
    public async Task<ActionResult> PostAdjustedSheet(int matterId, int sheetNumber,
        [FromBody] SignatureSheetTableDTO newSignatureSheetTable)
    {
        if (newSignatureSheetTable.Rows == null)
        {
            return BadRequest($"Missing rows");
        }

        if (newSignatureSheetTable.Columns == null)
        {
            return BadRequest($"Missing columns");
        }

        var matter = await _matterRepository.GetByIdAsync(matterId);
        if (matter == null)
        {
            return BadRequest($"Matter {matterId} does not exist");
        }

        var oldSignatureSheetTable =
            await _signatureSheetTableRepository.GetByMatterAndSheetNumberAsync(matterId, sheetNumber);
        if (oldSignatureSheetTable?.SignatureSheet == null)
        {
            return BadRequest($"Template table for matter {matterId} and sheet {sheetNumber} does not exist");
        }

        var templateSignatureColumns =
            await _templateSignatureColumnRepository.GetAllByTemplateIdAsync(oldSignatureSheetTable.SignatureSheet
                .TemplateId);
        if (oldSignatureSheetTable.SignatureSheet == null)
        {
            return NotFound($"SignatureSheet {sheetNumber} not found in matter {matterId}");
        }

        var newRows = newSignatureSheetTable.Rows.Where(r => !r.IsMissing).ToList();
        var newCols = newSignatureSheetTable.Columns.Where(c => !c.IsMissing).ToList();

        if (!newRows.Any())
        {
            return BadRequest("Table Rows cannot be null");
        }

        if (!newCols.Any())
        {
            return BadRequest("Table Columns cannot be null");
        }

        var oldRows = oldSignatureSheetTable.Rows.Where(r => !r.IsMissing).ToList();
        var oldCols = oldSignatureSheetTable.Columns.Where(r => !r.IsMissing).ToList();
        var oldCells = await _signatureSheetCellRepository.GetAllByTableIdAsync(oldSignatureSheetTable.Id);


        int signatureSheetTableId = 0;
        foreach (var newCol in newCols)
        {
            var oldCol = oldCols.First(x => x.ColumnIndex == newCol.ColumnIndex);
            oldCol.Left = (decimal)newCol.Left;
            oldCol.Top = (decimal)newCol.Top;
            oldCol.Right = (decimal)newCol.Right;
            oldCol.Bottom = (decimal)newCol.Bottom;
            _signatureSheetColumnRepository.SetModified(oldCol);
            signatureSheetTableId = oldCol.SignatureSheetTableId;
        }

        var formLines = await GetSignatureSheetLinesForPage(newSignatureSheetTable);

        // Get the signatureSheet form lines for this page
        foreach (var newRow in newRows)
        {
            var oldRow = oldRows.SingleOrDefault(x => x.RowIndex == newRow.RowIndex);
            if (oldRow == null)
            {
                oldRow = new SignatureSheetRow
                {
                    SignatureSheet = oldSignatureSheetTable.SignatureSheet,
                    SignatureSheetTableId = oldSignatureSheetTable.Id,
                    TemplateSignatureTableId = signatureSheetTableId,
                    RowIndex = newRow.RowIndex,
                    RowNumber = newRow.RowNumber,
                };
                _signatureSheetRowRepository.Add(oldRow);
            }

            oldRow.Left = (decimal)newRow.Left;
            oldRow.Top = (decimal)newRow.Top;
            oldRow.Right = (decimal)newRow.Right;
            oldRow.Bottom = (decimal)newRow.Bottom;
            _signatureSheetRowRepository.SetModified(oldRow);

            foreach (var newCol in newCols)
            {
                var oldCell = oldCells.SingleOrDefault(c =>
                    c.RowIndex == newRow.RowIndex && c.ColumnIndex == newCol.ColumnIndex);
                if (oldCell == null)
                {
                    var cellTemplateColumnId = templateSignatureColumns
                        .First(x => x.ColumnIndex == newCol.ColumnIndex).Id;
                    oldCell = new SignatureSheetCell
                    {
                        SignatureSheetRow = oldRow,
                        TemplateSignatureColumnId = cellTemplateColumnId,
                        ColumnIndex = newCol.ColumnIndex,
                        RowIndex = newRow.RowIndex,
                    };
                    _signatureSheetCellRepository.Add(oldCell);
                }

                oldCell.Left = (decimal)newCol.Left;
                oldCell.Top = (decimal)newRow.Top;
                oldCell.Right = (decimal)newCol.Right;
                oldCell.Bottom = (decimal)newRow.Bottom;
                if (!oldCell.IsReviewed)
                {
                    oldCell.Value = "";
                }

                _signatureSheetCellRepository.SetModified(oldCell);
            }

            _signatureSheetGeometryService.AssignLinesToCellValues(oldRow, formLines);
        }

        await _signatureSheetCellRepository.SaveChangesAsync();
        return Ok();
    }


    private async Task<List<FormResultsLine>> GetSignatureSheetLinesForPage(
        SignatureSheetTableDTO newSignatureSheetTable)
    {
        var signatureSheetLines = await _signatureSheetFormLineRepository.GetAllBySignatureSheetIdAsync(
            newSignatureSheetTable.SignatureSheetId);
        var pageLines = signatureSheetLines
            .Where(l => l.SignatureSheetPage?.PageNumber == newSignatureSheetTable.PageNumber);
        List<FormResultsLine> formLines = pageLines
            .Select(ssl => new FormResultsLine()
            {
                BoundingPolygon = new List<PointF>()
                {
                    new PointF((float)ssl.Left, (float)ssl.Top),
                    new PointF((float)ssl.Right, (float)ssl.Top),
                    new PointF((float)ssl.Right, (float)ssl.Bottom),
                    new PointF((float)ssl.Left, (float)ssl.Bottom)
                },
                Content = ssl.Value,
            })
            .ToList();
        return formLines;
    }

    [HttpGet("{matterId}/sheetNumbers")]
    [RoleCheck(RoleType.Admin, RoleType.Manager, RoleType.Reviewer)]
    public async Task<ActionResult<int[]>> GetSheetNumbersByMatter(int matterId)
    {
        var sheetNumbers = await _signatureSheetRepository.GetSheetNumbersByMatterAsync(matterId);
        return Ok(sheetNumbers);
    }
}