using Backend.DTO.Boundaries;
using DataInterface.RepositoryInterfaces;
using Microsoft.AspNetCore.Mvc;
using Model.Boundaries;

namespace Backend.Controllers;

[Route("api/[controller]")]
public class BoundariesController : BaseApiController
{
    private readonly IBoundaryRepository _boundaryRepository;
    private readonly IBoundaryPointRepository _boundaryPointRepository;

    public BoundariesController(
        IBoundaryRepository boundaryRepository,
        IBoundaryPointRepository boundaryPointRepository,
        ILogger<BoundariesController> logger
    )
        : base(logger)
    {
        _boundaryRepository = boundaryRepository;
        _boundaryPointRepository = boundaryPointRepository;
    }

    [HttpHead("{stateId}/{boundaryName}")]
    public async Task<ActionResult<BoundaryPoint[]>> DoesBoundaryNameExist(int usStateId, string boundaryName)
    {
        var doesExist = await _boundaryRepository.DoesStateBoundaryNameExistAsync(usStateId, boundaryName);
        if (!doesExist)
        {
            return NotFound();
        }

        return NoContent();
    }

    [HttpGet("{usStateId}/{boundaryName}")]
    public async Task<ActionResult<IEnumerable<BoundaryPoint>>> GetByName(int usStateId, string boundaryName)
    {
        var boundary = await _boundaryRepository.GetByStateAndNameAsync(usStateId, boundaryName);
        if (boundary == null)
        {
            return NotFound();
        }

        return Ok(boundary.BoundaryPoints.OrderBy(bp => bp.Index));
    }

    [HttpPost]
    public async Task<ActionResult> Post(BoundariesDTO dto)
    {
        var boundary = new Boundary
        {
            Name = dto.Name,
            Type = dto.Type,
        };
        boundary.BoundaryPoints = dto.Boundaries
            .Select(point => new BoundaryPoint
                { Boundary = boundary, Latitude = point.Latitude, Longitude = point.Longitude })
            .ToList();

        _boundaryRepository.Add(boundary);
        await _boundaryPointRepository.BulkInsertAsync(boundary.BoundaryPoints);
        await _boundaryPointRepository.SaveChangesAsync();

        return Ok();
    }
}