﻿using AutoMapper;
using Backend.Authentication;
using DataInterface.RepositoryInterfaces;
using Microsoft.AspNetCore.Mvc;
using Model.Authorization;
using Model.ExternalDataSources;

namespace Backend.Controllers;

[Route("api/[controller]")]
[RoleCheck(RoleType.Admin)]
public class DataProcessingLogsController : BaseApiController
{
    private readonly IDataProcessingLogRepository _dataProcessingLogRepository;
    private readonly IMapper _mapper;

    public DataProcessingLogsController(
        ILogger<BaseApiController> logger,
        IDataProcessingLogRepository externalDataSourceRepository,
        IMapper mapper
    )
        : base(logger)
    {
        _dataProcessingLogRepository = externalDataSourceRepository;
        _mapper = mapper;
    }

    [HttpGet("externaldata/{externalDataSourcePartId}")]
    public async Task<ActionResult<List<DataProcessingLog>>> GetExternalDataSourceLogs(int externalDataSourcePartId)
    {
        var logs = await _dataProcessingLogRepository.GetLogsByExternalDataSourcePartIdAsync(externalDataSourcePartId);
        return Ok(logs);
    }

    [HttpGet("signaturesheet/{signatureSheetUploadId}")]
    public async Task<ActionResult<List<DataProcessingLog>>> GetSignatureSheetLogs(int signatureSheetUploadId)
    {
        var logs = await _dataProcessingLogRepository.GetLogsBySignatureSheetUploadIdAsync(signatureSheetUploadId);
        return Ok(logs);
    }
}