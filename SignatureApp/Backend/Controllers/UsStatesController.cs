using AutoMapper;
using Backend.Authentication;
using DataInterface.RepositoryInterfaces;
using Microsoft.AspNetCore.Mvc;
using Model.Authorization;
using Model.Boundaries;
using Model.ReferenceData;

namespace Backend.Controllers;

[Route("api/[controller]")]
[RoleCheck(RoleType.Admin)]
public class UsStatesController : BaseApiController
{
    private readonly IUsStateRepository _usStateRepository;
    private readonly IBoundaryRepository _boundaryRepository;

    public UsStatesController(
        IUsStateRepository usStateRepository,
        IBoundaryRepository boundaryRepository,
        IMapper mapper,
        ILogger<BaseApiController> logger)
        : base(logger)
    {
        _usStateRepository = usStateRepository;
        _boundaryRepository = boundaryRepository;
    }

    [HttpGet]
    public async Task<ActionResult<List<UsState>>> GetUsStates()
    {
        return await _usStateRepository.GetAllAsync();
    }

    [HttpGet("{stateId}")]
    public async Task<ActionResult<UsState>> GetUsStateById(int stateId)
    {
        var state = await _usStateRepository.GetByIdAsync(stateId);
        if (state is null) return BadRequest("State Id not found");
        return state;
    }

    [HttpGet("{stateId}/counties")]
    public async Task<ActionResult<List<Boundary>>> GetCountiesByState(int stateId)
    {
        return await _boundaryRepository.GetAllByStateAndTypeAsync(stateId, BoundaryType.County);
    }

    [HttpGet("{stateId}/cities")]
    public async Task<ActionResult<List<Boundary>>> GetCitiesByState(int stateId)
    {
        return await _boundaryRepository.GetAllByStateAndTypeAsync(stateId, BoundaryType.City);
    }

    [HttpGet("{stateId}/zipcodes")]
    public async Task<ActionResult<List<Boundary>>> GetZipCodesByState(int stateId)
    {
        return await _boundaryRepository.GetAllByStateAndTypeAsync(stateId, BoundaryType.ZipCode);
    }
}