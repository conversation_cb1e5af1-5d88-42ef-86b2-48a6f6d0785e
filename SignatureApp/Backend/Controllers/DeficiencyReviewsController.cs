using AutoMapper;
using Backend.Authentication;
using Backend.DTO.Deficiencies;
using DataInterface;
using DataInterface.RepositoryInterfaces;
using Microsoft.AspNetCore.Mvc;
using Model.Authorization;
using Model.Deficiencies;
using Service;

namespace Backend.Controllers;

[Route("api")] // matters/{matterId}/deficiency/reviews
[RoleCheck(RoleType.Admin)]
public class DeficiencyReviewsController : BaseApiController
{
    private readonly DeficiencyService _deficiencyService;
    private readonly IDeficiencyRepository _deficiencyRepository;
    private readonly IDeficiencyReviewRepository _deficiencyReviewRepository;
    private readonly IMapper _mapper;
    private readonly IUserContext _userContext;
    private readonly IUserRepository _userRepository;

    public DeficiencyReviewsController(
        DeficiencyService deficiencyService,
        IDeficiencyRepository deficiencyRepository,
        IDeficiencyReviewRepository deficiencyReviewRepository,
        ILogger<BaseApiController> logger,
        IMapper mapper,
        IUserContext userContext,
        IUserRepository userRepository
            )
        : base(logger)
    {
        _deficiencyService = deficiencyService;
        _deficiencyRepository = deficiencyRepository;
        _deficiencyReviewRepository = deficiencyReviewRepository;
        _mapper = mapper;
        _userContext = userContext;
        _userRepository = userRepository;
    }

    [HttpGet("matters/{matterId}/deficiency/reviews")]
    public async Task<ActionResult<int[]>> GetReviewableDeficiencyIdsByMatter(int matterId)
    {
        var deficiencyReviewIds = await _deficiencyReviewRepository.GetAllNonReviewedIdsByMatterAsync(matterId);
        return Ok(deficiencyReviewIds.ToArray());
    }

    [HttpGet("matters/{matterId}/deficiency/reviews/next")]
    public async Task<ActionResult<DeficiencyReviewDTO?>> GetNextDeficiencyReviewByMatterId(int matterId)
    {
        DeficiencyReview? deficiencyReview = null;
        Deficiency? deficiency = null;
        while (deficiency == null)
        {
            deficiencyReview = await _deficiencyReviewRepository.GetNextByMatterIdAsync(matterId, _userContext.Username);
            if (deficiencyReview == null)
            {
                return NotFound();
            }

            deficiency = await _deficiencyRepository.GetByDeficiencyReviewAsync(deficiencyReview);
            if (deficiency == null)
            {
                _deficiencyReviewRepository.Remove(deficiencyReview);
                await _deficiencyReviewRepository.SaveChangesAsync();
            }
        }

        if (deficiencyReview != null && (deficiencyReview.UserId == null || deficiencyReview.AssignedTo == null))
        {
            await _deficiencyReviewRepository.AssignDeficiencyReviewAsync(deficiencyReview, _userContext.Username);
            await _deficiencyReviewRepository.SaveChangesAsync();
        }
        var dto = _mapper.Map<DeficiencyReviewDTO>(deficiencyReview);
        dto.DeficiencyId = deficiency.Id;
        return Ok(dto);
    }

    [HttpPost("matters/{matterId}/deficiency/reviews")]
    public async Task<ActionResult<DeficiencyReviewDTO?>> PostDeficiencyReview(int matterId, [FromBody] DeficiencyReviewDTO review)
    {
        var user = await _userRepository.GetByEmailAsync(_userContext.Username);
        if (user is null) return NotFound("User not found");

        var deficiency =
            await _deficiencyRepository.UpdateReviewedStatusAsync(review.DeficiencyId, user, review.IsDeficient,
                review.Note);
        if (deficiency == null) return NotFound("Deficiency not found");
        if (deficiency.MatterId != matterId)
        {
            return NotFound("MatterId does not match that of the deficiency");
        }

        await _deficiencyReviewRepository.UpdateReviewedStatusAsync(deficiency, review.IsDeficient, review.Note);
        await _deficiencyRepository.SaveChangesAsync();
        return await GetNextDeficiencyReviewByMatterId(matterId);
    }
}