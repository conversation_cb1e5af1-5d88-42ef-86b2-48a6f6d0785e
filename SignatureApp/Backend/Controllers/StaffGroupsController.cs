﻿using AutoMapper;
using Backend.Authentication;
using Backend.DTO.Groups;
using Backend.DTO.Tasks;
using DataInterface.RepositoryInterfaces;
using Microsoft.AspNetCore.Mvc;
using Model.Authorization;
using Model.Workflow;

namespace Backend.Controllers;

[Route("api/[controller]")]
[RoleCheck(RoleType.Admin, RoleType.Manager)]
public class StaffGroupsController : BaseApiController
{
    private readonly IStaffGroupRepository _staffGroupRepository;
    private readonly IGroupTaskAssignmentRepository _groupTaskAssignmentRepository;
    private readonly IUserGroupAssignmentRepository _userGroupAssignmentRepository;
    private readonly IMapper _mapper;

    public StaffGroupsController(
        IStaffGroupRepository staffGroupRepository,
        IGroupTaskAssignmentRepository groupTaskAssignmentRepository,
        IUserGroupAssignmentRepository userGroupAssignmentRepository,
        IMapper mapper,
        ILogger<BaseApiController> logger)
        : base(logger)
    {
        _staffGroupRepository = staffGroupRepository;
        _groupTaskAssignmentRepository = groupTaskAssignmentRepository;
        _userGroupAssignmentRepository = userGroupAssignmentRepository;
        _mapper = mapper;
    }

    [HttpGet("{id}")]
    public async Task<StaffGroup?> GetStaffGroupById(int id)
    {
        return await _staffGroupRepository.GetByIdAsync(id);
    }

    [HttpGet]
    public async Task<IEnumerable<StaffGroup>> GetStaffGroups()
    {
        return await _staffGroupRepository.GetAllAsync();
    }


    [HttpPost]
    public async Task<ActionResult<int>> CreateStaffGroup(CreateGroupDTO dto)
    {
        var staffGroup = _mapper.Map<StaffGroup>(dto);

        var existingGroup = await _staffGroupRepository.GetByNameAsync(staffGroup.Name);
        if (existingGroup != null)
        {
            return Conflict(new { message = $"A staff group with the name '{staffGroup.Name}' already exists." });
        }

        _staffGroupRepository.Add(staffGroup);
        await _staffGroupRepository.SaveChangesAsync();
        return CreatedAtAction(nameof(CreateStaffGroup), new { id = staffGroup.Id }, staffGroup);
    }

    [HttpPut("{id}")]
    public async Task<ActionResult> UpdateStaffGroup(UpdateGroupDTO dto, int id)
    {
        var staffGroup = await _staffGroupRepository.GetByIdAsync(id);

        if (staffGroup is null) return NotFound($"Group {id} not found.");

        var existingGroup = await _staffGroupRepository.GetByNameAsync(dto.Name);
        if (existingGroup != null)
        {
            return Conflict(new { message = $"A staff group with the name '{dto.Name}' already exists." });
        }

        staffGroup.Name = dto.Name;

        _staffGroupRepository.SetModified(staffGroup);
        await _staffGroupRepository.SaveChangesAsync();

        return Ok();
    }

    [HttpDelete("{id}")]
    public async Task<ActionResult> DeleteStaffGroup(int id)
    {
        var staffGroup = await _staffGroupRepository.GetByIdAsync(id);
        if (staffGroup is null) return NotFound($"Group {id} not found");

        _staffGroupRepository.Remove(staffGroup);
        await _staffGroupRepository.SaveChangesAsync();

        return Ok();
    }

    [HttpGet("{groupId}/tasks")]
    public async Task<ActionResult<List<GetTaskDTO>>> GetTasksByStaffGroupId(int groupId, int matterId,
        bool assigned = true)
    {
        List<Model.Workflow.Task> tasks;

        if (!assigned)
        {
            tasks = await _groupTaskAssignmentRepository.GetUnassignedTasksByStaffGroup(groupId, matterId);
        }
        else
        {
            tasks = await _groupTaskAssignmentRepository.GetTasksByStaffGroup(groupId, matterId);
        }


        return Ok(_mapper.Map<List<GetTaskDTO>>(tasks));
    }

    [HttpPost("{groupId}/tasks")]
    public async Task<ActionResult<int>> AddTasksToStaffGroup(int groupId, int matterId, [FromBody] List<int> taskIds)
    {
        var assignedTasks = await _groupTaskAssignmentRepository.GetTasksByStaffGroup(groupId, matterId);
        foreach (var taskId in taskIds)
        {
            if (!assignedTasks.Any(tasks => tasks.Id == taskId))
            {
                var groupTask = new GroupTaskAssignment { StaffGroupId = groupId, TaskId = taskId };
                _groupTaskAssignmentRepository.Add(groupTask);
            }
        }

        await _groupTaskAssignmentRepository.SaveChangesAsync();

        return CreatedAtAction(nameof(AddTasksToStaffGroup), null);
    }

    [HttpDelete("{id}/tasks/{taskId}")]
    public async Task<ActionResult> DeleteTasksByStaffGroupId(int id, int taskId)
    {
        var groupTasks = await _groupTaskAssignmentRepository.GetByStaffGroupAndTaskId(id, taskId);

        foreach (var groupTask in groupTasks)
        {
            _groupTaskAssignmentRepository.Remove(groupTask);
        }

        await _groupTaskAssignmentRepository.SaveChangesAsync();

        return Ok();
    }
}