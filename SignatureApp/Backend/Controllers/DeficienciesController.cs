using AutoMapper;
using Backend.DTO;
using Backend.DTO.Deficiencies;
using DataInterface;
using DataInterface.RepositoryInterfaces;
using Microsoft.AspNetCore.Mvc;
using Service;
using System.Net;
using Backend.Authentication;
using Backend.DTO.Rules;
using Model.Authorization;
using Service.ServiceModels;

namespace Backend.Controllers;

[Route("api")] // matters/{matterId}/deficiency
[RoleCheck(RoleType.Admin)]
public class DeficienciesController : BaseApiController
{
    private readonly DeficiencyService _deficiencyService;
    private readonly IDeficiencyRepository _deficiencyRepository;
    private readonly IMapper _mapper;
    private readonly IUserContext _userContext;
    private readonly IUserRepository _userRepository;
    private readonly PdfManipulationService _pdfManipulationService;
    private readonly WordService _wordService;
    private readonly WorkService _workService;

    public DeficienciesController(
        DeficiencyService deficiencyService,
        IDeficiencyRepository deficiencyRepository,
        ILogger<BaseApiController> logger,
        IMapper mapper,
        IUserContext userContext,
        IUserRepository userRepository,
        PdfManipulationService pdfManipulationService,
        WordService wordService,
        WorkService workService)
        : base(logger)
    {
        _deficiencyService = deficiencyService;
        _deficiencyRepository = deficiencyRepository;
        _mapper = mapper;
        _userContext = userContext;
        _userRepository = userRepository;
        _pdfManipulationService = pdfManipulationService;
        _workService = workService;
        _wordService = wordService;
    }

    [HttpGet("matters/{matterId}/deficiencies/{deficiencyId}")]
    public async Task<ActionResult<DeficiencyWithRuleDTO>> GetDeficiencyById(int matterId, int deficiencyId)
    {
        var deficiency = await _deficiencyRepository.GetDeficiencyAndSignatureSheetByIdAsync(deficiencyId);
        if (deficiency == null)
        {
            return NotFound();
        }

        if (deficiency.MatterId != matterId)
        {
            return StatusCode((int)HttpStatusCode.Forbidden);
        }

        var deficiencyDtos =
            await _deficiencyService.TranslateToDtosAsync([deficiency], matterId, shouldIncludeImages: true);
        var deficiencyDto = deficiencyDtos.First();

        var expressionAndWorks = await _deficiencyService.GetRuleExpressionAndWorks(matterId, deficiency);

        var listOfWorks = new List<RuleExpressionAndWorkDTO>();
        foreach (var ew in expressionAndWorks)
        {
            var expressionAndWork = _mapper.Map<RuleExpressionAndWorkDTO>(ew);
            if (ew.Work != null)
            {
                var workDto = await _workService.GetWorkDTOByWorkAsync(ew.Work);
                expressionAndWork.Work = _mapper.Map<GetWorkDTO>(workDto);
                expressionAndWork.Work.Image = new FileContentResult(workDto.ImageBytes, "image/png");
            }
            listOfWorks.Add(expressionAndWork);
        }

        var dto = new DeficiencyWithRuleDTO
        {
            Deficiency = _mapper.Map<DeficiencyDTO>(deficiencyDto),
            Rule = _mapper.Map<RuleSummaryDTO>(deficiency.Rule),
            ExpressionAndWorks = listOfWorks,
        };
        return Ok(dto);
    }

    [HttpGet("matters/{matterId}/deficiencies/full")]
    public async Task<ActionResult<List<FullSheetDeficiencyDTO>>> GetFullSheetDeficiencies(int matterId)
    {
        List<FullSheetDeficiencyDTO> responses =
            await _deficiencyService.GetFullSheetDeficiencyDTOsByMatterAsync(matterId);

        return Ok(responses);
    }

    [HttpGet("matters/{matterId}/deficiencies/csv")]
    public async Task<ActionResult> GetDeficiencyCsv(int matterId)
    {
        var deficiencies = await _deficiencyRepository.GetAllByMatterAsync(matterId);
        if (deficiencies.Count == 0)
        {
            return NoContent();
        }

        var bytes = await _deficiencyService.GetDeficiencyCsvStreamAsync(deficiencies, matterId);
        return File(bytes, "text/csv");
    }

    [HttpGet("matters/{matterId}/deficiencies/word")]
    public async Task<FileContentResult> GetDeficiencyWord(int matterId)
    {
        List<FullSheetDeficiencyDTO> fullSheetDeficiencies =
            await _deficiencyService.GetFullSheetDeficiencyDTOsByMatterAsync(matterId);

        using MemoryStream memoryStream =
            await _wordService.GenerateWordDocumentForSheetDeficiencies(fullSheetDeficiencies);

        return File(memoryStream.ToArray(),
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            $"DeficienciesBySheet_{matterId}.docx");
    }

    [HttpGet("matters/{matterId}/deficiencies/pdf")]
    public async Task<FileContentResult> GetDeficiencyPdf(int matterId)
    {
        List<FullSheetDeficiencyDTO> fullSheetDeficiencies =
            await _deficiencyService.GetFullSheetDeficiencyDTOsByMatterAsync(matterId);

        using MemoryStream memoryStream =
            await _pdfManipulationService.GeneratePdfDocumentForSheetDeficiencies(fullSheetDeficiencies);

        return File(memoryStream.ToArray(), "application/pdf", $"DeficienciesBySheet_{matterId}.pdf");
    }

    [HttpGet("matters/{matterId}/deficiencies/sheet")]
    public async Task<ActionResult<List<DeficiencyDTO>>> GetDeficienciesBySheet(int matterId)
    {
        var deficienciesBySheet = await _deficiencyRepository.GetAllSheetFieldDeficienciesAsync(matterId);
        var deficiencyDtos = await _deficiencyService.TranslateToDtosAsync(deficienciesBySheet, matterId);

        return Ok(deficiencyDtos);
    }

    [HttpGet("matters/{matterId}/deficiencies/row")]
    public async Task<ActionResult<List<DeficiencyDTO>>> GetDeficienciesByRow(int matterId)
    {
        var deficienciesByRow = await _deficiencyRepository.GetAllRowDeficienciesAsync(matterId);
        var deficiencyDtos = await _deficiencyService.TranslateToDtosAsync(deficienciesByRow, matterId);

        return Ok(deficiencyDtos);
    }

    [HttpGet("matters/{matterId}/deficiencies/cell")]
    public async Task<ActionResult<List<DeficiencyDTO>>> GetDeficienciesByCell(int matterId)
    {
        var deficienciesByCell = await _deficiencyRepository.GetAllCellDeficienciesAsync(matterId);
        var deficiencyDtos = await _deficiencyService.TranslateToDtosAsync(deficienciesByCell, matterId);

        return Ok(deficiencyDtos);
    }

    [HttpGet("matters/{matterId}/deficiencies")]
    public async Task<ActionResult<List<DeficiencyDTO>>> GetDeficiencies(int matterId)
    {
        var deficiencies = await _deficiencyRepository.GetAllByMatterAsync(matterId);
        var deficiencyDtos = await _deficiencyService.TranslateToDtosAsync(deficiencies, matterId);

        return Ok(deficiencyDtos);
    }

    [HttpGet("matters/{matterId}/deficiencies/rules/{ruleId}")]
    public async Task<ActionResult<List<DeficiencyDTO>>> GetDeficienciesByRuleId(int matterId, int ruleId)
    {
        var deficiencies = await _deficiencyRepository.GetDeficienciesByRuleAsync(matterId, ruleId);
        var deficiencyResults = await _deficiencyService.TranslateToDtosAsync(deficiencies, matterId);
        var deficiencyDtos = deficiencyResults.Select(r => _mapper.Map<DeficiencyDTO>(r)).ToList();

        return Ok(deficiencyDtos);
    }

    [HttpGet("matters/{matterId}/deficiencies/matter")]
    public async Task<ActionResult<List<DeficiencyDTO>>> GetMatterDeficienciesOnly(int matterId)
    {
        var deficiencies = await _deficiencyRepository.GetOnlyMatterDeficiencies(matterId);
        var deficiencyDtos = _mapper.Map<List<DeficiencyDTO>>(deficiencies);
        return Ok(deficiencyDtos);
    }

    [HttpPost("matters/{matterId}/deficiencies/{ruleId}")]
    public async Task<ActionResult<DeficiencyDTO>> AddMatterDeficiency([FromRoute] int matterId,
        [FromRoute] int ruleId, [FromBody] ViolationDTO violationDto)
    {
        var user = await _userRepository.GetByEmailAsync(_userContext.Username);
        if (user is null) return NotFound("User not found");

        try
        {
            var deficiency =
                await _deficiencyService.AddMatterDeficiencyAsync(matterId, ruleId, user.Id, violationDto.Note);
            var deficiencyDto = _mapper.Map<DeficiencyDTO>(deficiency);
            return Ok(deficiencyDto);
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("Rule with ID"))
        {
            return BadRequest($"Rule with ID {ruleId} does not exist");
        }
    }

    [HttpDelete("matters/{matterId}/deficiencies/{ruleId}")]
    public async Task<ActionResult<DeficiencyDTO>> DeleteMatterDeficiency([FromRoute] int matterId,
        [FromRoute] int ruleId)
    {
        await _deficiencyService.DeleteMatterDeficiencyAsync(matterId, ruleId);
        return Ok();
    }

    [HttpGet("sheets/{sheetId}/deficiencies")]
    public async Task<ActionResult<List<DeficiencyDTO>>> GetSheetDeficienciesOnly(int sheetId)
    {
        var deficiencies = await _deficiencyRepository.GetOnlySheetDeficiencies(sheetId);
        var deficiencyDtos = _mapper.Map<List<DeficiencyDTO>>(deficiencies);
        return Ok(deficiencyDtos);
    }

    [HttpPost("sheets/{sheetId}/deficiencies/{ruleId}")]
    public async Task<ActionResult<DeficiencyDTO>> AddSheetDeficiency([FromRoute] int sheetId,
        [FromRoute] int ruleId, [FromBody] ViolationDTO violationDto)
    {
        var user = await _userRepository.GetByEmailAsync(_userContext.Username);
        if (user is null) return NotFound("User not found");

        try
        {
            var deficiencyResult =
                await _deficiencyService.AddSheetDeficiencyAsync(sheetId, ruleId, user.Id, violationDto.Note);
            if (!deficiencyResult.IsSuccess)
            {
                return BadRequest(deficiencyResult.ErrorMessages);
            }

            var deficiencyDto = _mapper.Map<DeficiencyDTO>(deficiencyResult.Value);
            return Ok(deficiencyDto);
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("Rule with ID"))
        {
            return BadRequest($"Rule with ID {ruleId} does not exist");
        }
    }

    [HttpDelete("sheets/{sheetId}/deficiencies/{ruleId}")]
    public async Task<ActionResult<DeficiencyDTO>> DeleteSheetDeficiency([FromRoute] int sheetId,
        [FromRoute] int ruleId)
    {
        await _deficiencyService.DeleteSheetDeficiencyAsync(sheetId, ruleId);
        return Ok();
    }
}