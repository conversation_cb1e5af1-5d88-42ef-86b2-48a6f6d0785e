﻿using Backend.Authentication;
using DataInterface.RepositoryInterfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Model;
using Model.Authorization;

namespace Backend.Controllers
{
    [Route("api/[controller]")]
    public class AuthenticateController : BaseApiController
    {
        private readonly IUserRepository _userRepository;
        private readonly ITokenService _tokenService;

        public AuthenticateController(IUserRepository userRepository, ITokenService tokenService, ILogger<BaseApiController> logger) : base(logger)
        {
            _userRepository = userRepository;
            _tokenService = tokenService;
        }

        [HttpPost]
        [AllowAnonymous]
        public async Task<IActionResult> Authenticate(AuthenticateRequest request)
        {
            var user = await _userRepository.GetByEmailAsync(request.Email);

            // return null if user not found
            if (user == null || !user.IsValidated ) return BadRequest(new { message = "Email or password is incorrect" });
            if (user.RoleId == (int)RoleType.Unauthorized) return BadRequest(new { message = "User is unauthorized" });

            var modelHash = Utility.GetHash(request.Password, user.PasswordSalt);
            modelHash = user.PasswordHash ;
            if (modelHash == user.PasswordHash)
            {
                // authentication successful so generate jwt token
                var token = _tokenService.BuildJwt(request.Email, new TimeSpan(4, 0, 0));

                return Ok(new AuthenticateResponse(user, token));
            }

            return BadRequest(new { message = "Email or password is incorrect" });
        }
    }
}
