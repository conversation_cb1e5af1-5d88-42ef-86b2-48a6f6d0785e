﻿using AutoMapper;
using Backend.Authentication;
using DataInterface.RepositoryInterfaces;
using Microsoft.AspNetCore.Mvc;
using Model.Authorization;
using Service;
using Service.ServiceModels;

namespace Backend.Controllers;

[Route("api/[controller]")]
[RoleCheck(RoleType.Admin)]
public class DataTransformationStepController : BaseApiController
{
    private readonly IDataTransformationStepStartRepository _dataTransformationStepStartRepository;
    private readonly IDataTransformationStepResultRepository _dataTransformationStepResultRepository;
    private readonly IMapper _mapper;

    public DataTransformationStepController(
        ILogger<BaseApiController> logger,
        IDataTransformationStepStartRepository dataTransformationStepStartRepository,
        IDataTransformationStepResultRepository dataTransformationStepResultRepository,
        IMapper mapper
    )
        : base(logger)
    {
        _dataTransformationStepStartRepository = dataTransformationStepStartRepository;
        _dataTransformationStepResultRepository = dataTransformationStepResultRepository;
        _mapper = mapper;
    }

    [HttpGet("status/{uploadId}")]
    public async Task<ActionResult<DataTransformationStatus>> GetStatusByUploadId(int uploadId)
    {
        var startedSteps = await _dataTransformationStepStartRepository.GetByUploadIdAsync(uploadId);
        var stepResults = await _dataTransformationStepResultRepository.GetByUploadIdAsync(uploadId);

        DataTransformationStatus result =
            DataTransformationStepService.GetDataTransformationStatus(uploadId, startedSteps, stepResults);
        return Ok(result);
    }
}