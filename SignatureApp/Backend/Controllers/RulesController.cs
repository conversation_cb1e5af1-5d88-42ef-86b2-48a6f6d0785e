﻿using AutoMapper;
using Backend.Authentication;
using Backend.DTO.Rules;
using DataInterface;
using DataInterface.RepositoryInterfaces;
using Microsoft.AspNetCore.Mvc;
using Model.Authorization;
using Model.DTOs;
using Model.Rules;
using Service;

namespace Backend.Controllers;

[Route("api/[controller]")]
[RoleCheck(RoleType.Admin)]
public class RulesController : BaseApiController
{
    private readonly IBackgroundOperationRepository _backgroundOperationRepository;
    private readonly IMatterRepository _matterRepository;
    private readonly IRuleRepository _ruleRepository;
    private readonly IMapper _mapper;
    private readonly IUserContext _userContext;
    private readonly RuleGenerationServiceFactory _ruleGenerationServiceFactory;

    public RulesController(
        IBackgroundOperationRepository backgroundOperationRepository,
        IMatterRepository matterRepository,
        IRuleRepository ruleRepository,
        IMapper mapper,
        IUserContext userContext,
        ILogger<BaseApiController> logger,
        RuleGenerationServiceFactory ruleGenerationServiceFactory)
        : base(logger)
    {
        _backgroundOperationRepository = backgroundOperationRepository;
        _matterRepository = matterRepository;
        _ruleRepository = ruleRepository;
        _mapper = mapper;
        _userContext = userContext;
        _ruleGenerationServiceFactory = ruleGenerationServiceFactory;
    }

    [HttpGet("count")]
    public async Task<int> GetCount(int matterId)
    {
        var count = await _ruleRepository.GetCountByMatterIdAsync(matterId);

        return _mapper.Map<int>(count);
    }


    [HttpGet("{matterId}")]
    public async Task<ActionResult<List<RuleSummaryDTO>>> GetAllRules(int matterId)
    {
        var rules = await _ruleRepository.GetAllWithDeficienciesByMatterIdAsync(matterId);

        return Ok(_mapper.Map<List<RuleSummaryDTO>>(rules));
    }

    [HttpGet("{matterId}/type/{type}")]
    public async Task<ActionResult<List<ViolationCriterion>>> GetRulesByType(int matterId, int type)
    {
        var ruleContextType = (RuleContextType)type;
        var rules = await _ruleRepository.GetAllByMatterAndTypeAsync(matterId, ruleContextType);
        if (ruleContextType >= RuleContextType.SignatureSheet)
        {
            rules = rules.Where(rule => string.IsNullOrEmpty(rule.OperationName)).ToList();
        }

        return Ok(_mapper.Map<List<ViolationCriterion>>(rules));
    }

    [HttpGet("{matterId}/nontask/{type}")]
    public async Task<ActionResult<List<ViolationCriterion>>> GetNonTaskRulesByType(int matterId, int type)
    {
        var ruleContextType = (RuleContextType)type;
        var rules = await _ruleRepository.GetAllNonTaskByMatterAndTypeAsync(matterId, ruleContextType);
        if (ruleContextType == RuleContextType.Matter)
        {
            rules = rules.Where(rule => string.IsNullOrEmpty(rule.OperationName)).ToList();
        }

        return Ok(_mapper.Map<List<ViolationCriterion>>(rules));
    }

    [HttpGet("{matterId:int}/lastrun")]
    public async Task<ActionResult<GetLastRunDTO>> GetSecondsSinceRunRulesCompleted(int matterId)
    {
        var lastRun =
            await _backgroundOperationRepository.GetTimeSpanSinceOperationCompletedAsync(matterId,
                BackgroundOperationType.RunRules);
        if (lastRun == null)
        {
            return Ok();
        }

        var dto = _mapper.Map<GetLastRunDTO>(lastRun);
        if (lastRun.StartedOn != null)
        {
            var timeSpan = DateTime.UtcNow - lastRun.StartedOn.Value;
            dto.SecondsSinceStarted = (int)timeSpan.TotalSeconds;
        }

        if (lastRun.EndedOn != null)
        {
            var timeSpan = DateTime.UtcNow - lastRun.EndedOn.Value;
            dto.SecondsSinceCompletion = (int)timeSpan.TotalSeconds;
        }

        return Ok(dto);
    }


    [HttpPost("create")]
    public async Task<ActionResult<bool>> CreateAllRules(int templateId, int matterId)
    {
        var matter = await _matterRepository.GetByIdAsync(matterId);
        if (matter == null)
        {
            return BadRequest($"Matter {matterId} does not exist");
        }

        var ruleGenerationService = _ruleGenerationServiceFactory.Create(matter.Type);
        var created = await ruleGenerationService.CreateIfNecessaryAsync(matterId, templateId);
        return Ok(created);
    }

    [HttpPost("run/{matterId:int}")]
    public async Task<ActionResult<int>> RunAllRules(int matterId, [FromQuery] bool force,
        [FromServices] IServiceScopeFactory serviceScopeFactory)
    {
        if (force)
        {
            await _backgroundOperationRepository.TerminateRunningJobsAsync(matterId);
        }
        else
        {
            var existingOp = await _backgroundOperationRepository
                .GetRunningJobIdAsync(matterId, BackgroundOperationType.RunRules);
            if (existingOp != null)
            {
                return existingOp.Id;
            }
        }

        var backgroundOperation = new BackgroundOperation
        {
            ExecutionStatus = ExecutionStatus.Running,
            MatterId = matterId,
            OperationType = BackgroundOperationType.RunRules
        };
        _backgroundOperationRepository.Add(backgroundOperation);
        await _backgroundOperationRepository.SaveChangesAsync();

        _ = Task.Run(async () =>
        {
            using var scope = serviceScopeFactory.CreateScope();
            var rulesEngine = scope.ServiceProvider.GetRequiredService<RulesEngine>();
            var backgroundOperationRepository =
                scope.ServiceProvider.GetRequiredService<IBackgroundOperationRepository>();

            try
            {
                var response = await rulesEngine.EvaluateAllAsync(matterId);
                if (response.IsSuccess)
                {
                    backgroundOperation.ExecutionStatus = ExecutionStatus.Succeeded;
                }
                else
                {
                    backgroundOperation.ExecutionStatus = ExecutionStatus.Failed;
                    backgroundOperation.Message = response.ErrorMessage;
                }
            }
            catch (Exception ex)
            {
                backgroundOperation.ExecutionStatus = ExecutionStatus.Failed;
                backgroundOperation.Message = ex.Message;
            }
            finally
            {
                backgroundOperationRepository.SetModified(backgroundOperation);
                await backgroundOperationRepository.SaveChangesAsync();
            }
        });

        return Ok(backgroundOperation.Id);
    }
}