using AutoMapper;
using Backend.Authentication;
using Backend.DTO;
using DataInterface.RepositoryInterfaces;
using DataInterface.ServiceInterfaces;
using Microsoft.AspNetCore.Mvc;
using Model.Authorization;
using Service;

namespace Backend.Controllers;

[Route("api/[controller]")]
[RoleCheck(RoleType.Admin, RoleType.Manager)]
public class TranscribableFieldsController : BaseApiController
{
    private readonly IFileService _fileService;
    private readonly IMapper _mapper;
    private readonly ITemplateRepository _templateRepository;
    private readonly ITemplatePageRepository _templatePageRepository;
    private readonly ITranscribableFieldRepository _transcribableFieldRepository;
    private readonly PdfManipulationService _pdfManipulationService;

    public TranscribableFieldsController(
        IFileService fileService,
        ILogger<BaseApiController> logger,
        IMapper mapper,
        ITemplateRepository templateRepository,
        ITemplatePageRepository templatePageRepository,
        ITranscribableFieldRepository transcribableFieldRepository,
        PdfManipulationService pdfManipulationService
    ) : base(logger)
    {
        _fileService = fileService;
        _mapper = mapper;
        _templateRepository = templateRepository;
        _templatePageRepository = templatePageRepository;
        _transcribableFieldRepository = transcribableFieldRepository;
        _pdfManipulationService = pdfManipulationService;
    }

    [HttpGet("{templateId}/{id?}")]
    public async Task<ActionResult<GetTranscribableFieldDTO>> GetNextTranscribableField(int templateId, int? id)
    {
        var template = await _templateRepository.GetByIdAsync(templateId);
        if (template == null)
        {
            return NotFound();
        }

        var field = (id == null)
            ? (await _transcribableFieldRepository.GetAllByTemplateIdAsync(templateId)).FirstOrDefault()
            : await _transcribableFieldRepository.GetNextForTemplateByIdAsync(templateId, id.Value);
        if (field == null)
        {
            return NotFound();
        }

        var templatePage = await _templatePageRepository.GetByIdAsync(field.TemplatePageId);
        if (templatePage == null)
        {
            throw new Exception($"Template file {template.Name} not found");
        }

        string filename = $"{template.Name}{Path.GetExtension(template.FileName)}";
        Stream? sourceFile = await _fileService.GetFileStreamAsync("templates", filename);
        if (sourceFile == null)
        {
            throw new Exception($"Template file {filename} not found");
        }

        var image = _pdfManipulationService.HighlightAreaOnPdf(sourceFile, field, templatePage.PageNumber);
        var dto = _mapper.Map<GetTranscribableFieldDTO>(field);
        dto.Image = File(image, "image/png");
        return Ok(dto);
    }

    [HttpPut("{templateId}/{id}")]
    public async Task<ActionResult<GetTranscribableFieldDTO>> UpdateTranscribableField(int templateId, int id,
        UpdateTranscribableFieldDTO updateDto)
    {
        var template = await _templateRepository.GetByIdAsync(templateId);
        if (template == null)
        {
            return NotFound();
        }

        var field = await _transcribableFieldRepository.GetByIdAsync(id);
        if (field == null)
        {
            return NotFound();
        }

        _mapper.Map(updateDto, field);
        field.Name = updateDto.IsSkipped ? "SKIPPED" : updateDto.Name;
        _transcribableFieldRepository.SetModified(field);
        await _transcribableFieldRepository.SaveChangesAsync();

        var nextField = await _transcribableFieldRepository.GetNextForTemplateByIdAsync(templateId, id);
        var getDto = _mapper.Map<GetTranscribableFieldDTO>(nextField);
        return Ok(getDto);
    }
}