﻿using AutoMapper;
using Azure.Storage.Blobs;
using Backend.Authentication;
using Backend.DTO;
using DataInterface.RepositoryInterfaces;
using Microsoft.AspNetCore.Mvc;
using Model.Authorization;

namespace Backend.Controllers;

[Route("api/[controller]")]
[RoleCheck(RoleType.Admin)]
public class TrainingModelsController : BaseApiController
{
    private readonly IMatterRepository _matterRepository;
    private readonly ITranscribableFieldRepository _transcribableFieldRepository;
    private readonly ITrainingModelRepository _trainingModelRepository;
    private readonly IMapper _mapper;
    private readonly BlobServiceClient _blobServiceClient;


    public TrainingModelsController(
        IMatterRepository matterRepository,
        ITranscribableFieldRepository transcribableFieldRepository,
        IMapper mapper,
        ITrainingModelRepository trainingModelRepository,
        BlobServiceClient blobServiceClient,
        ILogger<BaseApiController> logger)
        : base(logger)
    {
        _matterRepository = matterRepository;
        _trainingModelRepository = trainingModelRepository;
        _transcribableFieldRepository = transcribableFieldRepository;
        _mapper = mapper;
        _blobServiceClient = blobServiceClient;
    }


    [HttpGet]
    public async Task<IEnumerable<GetTrainingModelDTO>> GetTrainingModels()
    {
        var results = await _trainingModelRepository.GetAllAsync();

        return results.Select(x => new GetTrainingModelDTO { Id = x.Id, Description = x.Description });
    }
}