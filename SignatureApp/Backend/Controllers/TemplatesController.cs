using AutoMapper;
using Backend.Authentication;
using Backend.DTO;
using Backend.DTO.SignatureSheets;
using Service;
using Microsoft.AspNetCore.Mvc;
using Model.Templates;
using Backend.DTO.Templates;
using DataInterface.RepositoryInterfaces;
using DataInterface.ServiceInterfaces;
using Microsoft.Data.SqlClient;
using Model.Authorization;

namespace Backend.Controllers;

[Route("api/[controller]")]
[RoleCheck(RoleType.Admin, RoleType.Manager)]
public class TemplatesController : BaseApiController
{
    private readonly ITemplateRepository _templateRepository;
    private readonly ITemplateIgnoredWordRepository _templateIgnoredWordRepository;
    private readonly ITemplateSignatureColumnRepository _templateSignatureColumnRepository;
    private readonly ITemplateSignatureRowRepository _templateSignatureRowRepository;
    private readonly ITemplateSignatureTableRepository _templateSignatureTableRepository;
    private readonly ITranscribableFieldRepository _transcribableFieldRepository;
    private readonly IMapper _mapper;
    private readonly IFileService _fileService;
    private readonly PdfManipulationService _pdfManipulationService;

    public TemplatesController(
        ITemplateRepository templateRepository,
        ITemplateIgnoredWordRepository templateIgnoredWordRepository,
        ITemplateSignatureColumnRepository templateSignatureColumnRepository,
        ITemplateSignatureRowRepository templateSignatureRowRepository,
        ITemplateSignatureTableRepository templateSignatureTableRepository,
        ITranscribableFieldRepository transcribableFieldRepository,
        IMapper mapper,
        IFileService fileService,
        ILogger<BaseApiController> logger,
        PdfManipulationService pdfManipulationService)
        : base(logger)
    {
        _templateRepository = templateRepository;
        _templateIgnoredWordRepository = templateIgnoredWordRepository;
        _templateSignatureColumnRepository = templateSignatureColumnRepository;
        _templateSignatureRowRepository = templateSignatureRowRepository;
        _templateSignatureTableRepository = templateSignatureTableRepository;
        _transcribableFieldRepository = transcribableFieldRepository;
        _mapper = mapper;
        _fileService = fileService;
        _pdfManipulationService = pdfManipulationService;
    }

    [HttpGet]
    public async Task<ActionResult<List<Template>>> GetTemplates()
    {
        return await _templateRepository.GetAllActiveTemplatesAsync();
    }

    [HttpGet("{id:int}")]
    public async Task<ActionResult<GetTemplateDTO>> GetTemplate(int id)
    {
        var template = await _templateRepository.GetActiveTemplateAsync(id);

        if (template == null) return NotFound($"Id {id} not found.");

        var dto = _mapper.Map<GetTemplateDTO>(template);
        dto.UploadedOn = template.UploadedOn?.ToString("g");

        var maxField = template.Pages
            .SelectMany(p => p.Fields)
            .DefaultIfEmpty()
            .MaxBy(f => f?.ModifiedOn);
        dto.FieldsModifiedBy = maxField?.ModifiedBy;
        dto.FieldsModifiedOn = maxField?.ModifiedOn?.ToString("g");

        var maxColumn = template.Tables
            .SelectMany(t => t.Columns)
            .DefaultIfEmpty()
            .MaxBy(f => f?.ModifiedOn);
        dto.ColumnsModifiedBy = maxColumn?.ModifiedBy;
        dto.ColumnsModifiedOn = maxColumn?.ModifiedOn?.ToString("g");

        return Ok(dto);
    }

    [HttpPut("{id}")]
    public async Task<ActionResult> UpdateTemplate(UpdateTemplateDTO dto, int id)
    {
        var template = await _templateRepository.GetByIdAsync(id);
        if (template is null) return NotFound($"Template: {id} not found.");

        var listAllTemplates = await _templateRepository.GetAllActiveTemplatesAsync();
        if (listAllTemplates.Find(x => x.Name == dto.Name) == null)
        {
            string oldTemplate = template.Name;
            template.Name = dto.Name;
            _templateRepository.SetModified(template);
            await _templateRepository.SaveChangesAsync();

            // rename the file from blob storage only if a template is attached.
            if (template.FileName != null)
            {
                await _fileService.RenameFileStreamAsync("templates",
                    oldTemplate + Path.GetExtension(template.FileName),
                    dto.Name + Path.GetExtension(template.FileName));
            }

            return Ok();
        }

        return BadRequest($"Template Name {template.Name} already exists !");
    }

    [HttpDelete("{id}")]
    public async Task<ActionResult> DeleteTemplate(int id)
    {
        var template = await _templateRepository.GetByIdAsync(id);
        if (template is null) return NotFound($"Template {id} not found");
        try
        {
            await _templateRepository.DeleteTemplateHierarchy(id, shouldDeleteTemplate: true);
        }
        catch (SqlException sqlex)
        {
            _logger.LogError(sqlex, "Error deleting template {id}", id);
            template.IsActive = false;
        }
        finally
        {
            await _templateRepository.SaveChangesAsync();
        }

        await _fileService.DeleteFileStreamAsync("templates", template.Name + Path.GetExtension(template.FileName));
        return Ok();
    }

    [HttpPost]
    public async Task<ActionResult> CreateTemplate(CreateTemplateDTO dto)
    {
        var template = _mapper.Map<Template>(dto);
        var listAllTemplates = await _templateRepository.GetAllActiveTemplatesAsync();
        if (listAllTemplates.Find(x => x.Name == template.Name) == null)
        {
            _templateRepository.Add(template);
            await _templateRepository.SaveChangesAsync();
            return CreatedAtAction(nameof(CreateTemplate), new { id = template.Id }, template);
        }

        return BadRequest($"Template Name {template.Name} already exists !");
    }

    [HttpGet("{templateId}/signaturetablecolumns/{id?}")]
    public async Task<ActionResult<GetSignatureTableColumnDTO>> GetNextSignatureTableColumn(int templateId,
        int? id)
    {
        var template = await _templateRepository.GetByIdAsync(templateId);
        if (template == null)
        {
            return NotFound();
        }

        var field = (id == null)
            ? (await _templateSignatureColumnRepository.GetAllByTemplateIdAsync(templateId)).FirstOrDefault()
            : await _templateSignatureColumnRepository.GetNextForTemplateByIdAsync(templateId, id.Value);
        if (field == null)
        {
            return NotFound();
        }

        string filename = $"{template.Name}{Path.GetExtension(template.FileName)}";
        Stream? sourceFile = await _fileService.GetFileStreamAsync("templates", filename);
        if(sourceFile == Stream.Null || sourceFile == null)
        {
            _logger.LogWarning($"Template file {filename} not found in file storage");
            return NotFound();
        }
        var image = _pdfManipulationService.HighlightAreaOnPdf(sourceFile, field, 1);
        var dto = _mapper.Map<GetSignatureTableColumnDTO>(field);
        dto.Image = File(image, "image/png");
        return Ok(dto);
    }

    [HttpPut("{templateId}/signaturetablecolumns/{id}")]
    public async Task<ActionResult<GetSignatureTableColumnDTO>> UpdateSignatureTableColumn(int templateId,
        int id, UpdateSignatureTableColumnDTO updateDto)
    {
        var template = await _templateRepository.GetByIdAsync(templateId);
        if (template == null)
        {
            return NotFound();
        }

        var field = await _templateSignatureColumnRepository.GetByIdAsync(id);
        if (field == null)
        {
            return NotFound();
        }

        _mapper.Map(updateDto, field);
        field.Name = updateDto.IsSkipped ? "SKIPPED" : updateDto.Name;
        _templateSignatureColumnRepository.SetModified(field);
        await _templateSignatureColumnRepository.SaveChangesAsync();

        var nextField = await _templateSignatureColumnRepository.GetNextForTemplateByIdAsync(templateId, id);
        var getDto = _mapper.Map<GetSignatureTableColumnDTO>(nextField);
        return Ok(getDto);
    }


    [HttpGet("{templateId}/table")]
    public async Task<ActionResult<TemplateSignatureTableDTO>> GetTableByTemplateId(int templateId)
    {
        var table = await _templateSignatureTableRepository.GetByTemplateIdAsync(templateId);
        if (table == null || table.Template.Pages.Count == 0)
        {
            return NotFound($"Template {templateId}: no pages found");
        }

        var template = table.Template;
        var tableDto = _mapper.Map<TemplateSignatureTableDTO>(table);
        var page = table.Template.Pages[0];
        tableDto.ImageAspectRatio = page.Width / page.Height;
        tableDto.TemplateId = templateId;

        string filename = $"{template.Name}{Path.GetExtension(template.FileName)}";
        var pdfStream = await _fileService.GetFileStreamAsync("templates", filename);
        if (pdfStream == null)
        {
            _logger.LogWarning($"Template file {filename} not found in file storage");
            return NotFound($"Template file not found in file storage");
        }

        if (template.PageSize == null)
        {
            return NotFound($"Template {templateId}: no page size found");
        }
        var imageBytes = _pdfManipulationService.ConvertPdfPageToImage(pdfStream, template.PageSize.Value);

        tableDto.SheetImage = new FileContentResult(imageBytes, "image/png");
        return Ok(tableDto);
    }

    [HttpGet("{templateId}/fields")]
    public async Task<ActionResult<List<TranscribableField>>> GetFieldsByTemplateId(int templateId)
    {
        var fields = await _transcribableFieldRepository.GetAllByTemplateIdAsync(templateId);
        if (!fields.Any())
        {
            return NotFound($"Template {templateId}: no fields found");
        }

        return Ok(fields);
    }

    [HttpPost("{templateId}/table")]
    [RoleCheck(RoleType.Admin, RoleType.Manager)]
    public async Task<ActionResult> PostAdjustedSheet(int templateId,
        [FromBody] SignatureTableDTO newSignatureSheetTable)
    {
        var template = await _templateRepository.GetByIdAsync(templateId);
        if (template == null)
        {
            return BadRequest($"Matter {templateId} does not exist");
        }

        var oldTemplateTable =
            await _templateSignatureTableRepository.GetByTemplateIdAsync(templateId);
        if (oldTemplateTable?.Template == null)
        {
            return BadRequest($"Template table for template {templateId} does not exist");
        }

        var oldRows = oldTemplateTable.Rows;
        var oldCols = oldTemplateTable.Columns;
        var newRows = newSignatureSheetTable.Rows;
        var newCols = newSignatureSheetTable.Columns;
        if(newCols == null || newRows == null)
        {
            return BadRequest($"Template table for template {templateId} is missing rows or columns");
        }
        foreach (var newCol in newCols)
        {
            var oldCol = oldCols.First(x => x.ColumnIndex == newCol.ColumnIndex);
            oldCol.Left = (decimal)newCol.Left;
            oldCol.Top = (decimal)newCol.Top;
            oldCol.Right = (decimal)newCol.Right;
            oldCol.Bottom = (decimal)newCol.Bottom;
            _templateSignatureColumnRepository.SetModified(oldCol);
        }

        foreach (var newRow in newRows)
        {
            var oldRow = oldRows.SingleOrDefault(x => x.RowIndex == newRow.RowIndex);
            if (oldRow == null)
            {
                _logger.LogWarning($"Row {newRow.RowIndex} not found in old rows");
                return BadRequest("No matching row found to adjust");
            }
            oldRow.Left = (decimal)newRow.Left;
            oldRow.Top = (decimal)newRow.Top;
            oldRow.Right = (decimal)newRow.Right;
            oldRow.Bottom = (decimal)newRow.Bottom;
            _templateSignatureRowRepository.SetModified(oldRow);
        }

        await _templateSignatureRowRepository.SaveChangesAsync();
        return Ok();
    }

    [HttpGet("{templateId}/ignoredwords/")]
    public async Task<ActionResult<List<TemplateIgnoredWord>>> GetIgnoredWordsByTemplateId(int templateId)
    {
        var existingWords = await _templateIgnoredWordRepository.GetByTemplateIdAsync(templateId);
        return Ok(existingWords);
    }

    [HttpPost("{templateId}/ignoredwords/")]
    public async Task<ActionResult<List<TemplateIgnoredWord>>> UpdateTemplateIgnoredWords(int templateId,
        [FromBody] UpdateIgnoredWordsDTO updateIgnoredWords)
    {
        var existingWords = await _templateIgnoredWordRepository.GetByTemplateIdAsync(templateId);
        var newWords = updateIgnoredWords.NewWords;
        if (newWords.Count > 0)
        {
            foreach (var word in newWords)
            {
                var alreadyExists = existingWords.Any(w => w.Word.Equals(word, StringComparison.OrdinalIgnoreCase));
                if (alreadyExists) continue;

                var ignoredWord = new TemplateIgnoredWord
                {
                    Word = word,
                    TemplateId = templateId
                };

                _templateIgnoredWordRepository.Add(ignoredWord);
                existingWords.Add(ignoredWord);
            }
        }

        var idsToDelete = updateIgnoredWords.DeletedWordIds;
        if (idsToDelete.Count > 0)
        {
            foreach (var wordId in idsToDelete)
            {
                var existingWord = existingWords.FirstOrDefault(w => w.Id == wordId);
                if (existingWord == null) continue;

                _templateIgnoredWordRepository.Remove(existingWord);
                existingWords.Remove(existingWord);
            }
        }

        existingWords.Sort((w1, w2) => StringComparer.OrdinalIgnoreCase.Compare(w1.Word, w2.Word));
        await _templateIgnoredWordRepository.SaveChangesAsync();

        return Ok(existingWords);
    }
}