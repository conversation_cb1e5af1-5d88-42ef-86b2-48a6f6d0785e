using Backend.Authentication;
using Backend.DTO.SignatureSheets;
using DataInterface.RepositoryInterfaces;
using DataInterface.ServiceInterfaces;
using Microsoft.AspNetCore.Mvc;
using Model.Authorization;
using Model.DTOs;
using Model.ExternalDataSources;
using Model.Matters;
using Model.Rules;
using Model.SignatureSheets;
using Model.Templates;
using Service;
using System.Net;
using System.Text;
using System.Text.Json;
using DataInterface;

namespace Backend.Controllers;

[Route("api/upload/externaldata")]
[RoleCheck(RoleType.Admin)]
public class UploadExternalDataController : BaseApiController
{
    private readonly CsvParser _csvParser;
    private readonly ExternalDataSourceService _externalDataSourceService;
    private readonly IBackgroundOperationRepository _backgroundOperationRepository;
    private readonly IFileService _fileService;
    private readonly IExternalDataSourceRepository _externalDataSourceRepository;
    private readonly IRegisteredCirculatorRepository _registeredCirculatorRepository;

    public UploadExternalDataController(
        CsvParser csvParser,
        ExternalDataSourceService externalDataSourceService,
        IBackgroundOperationRepository backgroundOperationRepository,
        IExternalDataSourceRepository externalDataSourceRepository,
        IFileService fileService,
        ILogger<BaseApiController> logger,
        IRegisteredCirculatorRepository registeredCirculatorRepository)
        : base(logger)
    {
        _csvParser = csvParser;
        _externalDataSourceService = externalDataSourceService;
        _backgroundOperationRepository = backgroundOperationRepository;
        _externalDataSourceRepository = externalDataSourceRepository;
        _fileService = fileService;
        _registeredCirculatorRepository = registeredCirculatorRepository;
    }

    [HttpPost("voters")]
    [DisableRequestSizeLimit]
    public async Task<ActionResult> UploadRegisteredVoterFile(
        IFormCollection data, IFormFile file, int matterId, string county, int? part, int? total,
        CancellationToken ct,
        [FromServices] IServiceScopeFactory serviceScopeFactory)
    {
        if (!file.FileName.EndsWith(".csv")) return BadRequest("Unrecognized File Format");

        var memStream = new MemoryStream();
        await file.CopyToAsync(memStream, ct);

        if (part == null)
        {
            part = 1;
        }

        if (total == null)
        {
            total = 1;
        }

        var externalDataSourcePart =
            await _externalDataSourceService.CreateExternalDataSourceIfNeeded(county, part, total, file.FileName,
                UploadType.Voter);
        if (externalDataSourcePart.ExternalDataSource == null)
        {
            return BadRequest("Duplicate upload");
        }

        string standardizedFileName = ExternalDataSourceService.GetStandardizedVoterFileName(county, part, total);

        _ = Task.Run(async () =>
        {
            using var reader = new StreamReader(memStream);
            using var scope = serviceScopeFactory.CreateScope();
            var uploadHelper = new UploadExternalDataControllerHelper(scope);
            await uploadHelper.ProcessVoterFile(externalDataSourcePart.ExternalDataSource,
                externalDataSourcePart, reader, data, matterId, county, standardizedFileName, ct);
        });
        return Ok(externalDataSourcePart.Id);
    }

    [HttpPost("{matterId:int}/invalidvoters")]
    [DisableRequestSizeLimit]
    public async Task<ActionResult> UploadInvalidVoterFile(
        IFormCollection data, IFormFile file, int matterId, CancellationToken ct,
        [FromServices] IServiceScopeFactory serviceScopeFactory)
    {
        if (!file.FileName.EndsWith(".csv")) return BadRequest("Unrecognized File Format");

        int ruleId = 0;
        if (HttpContext.Request.Query.TryGetValue("ruleId", out var ruleIdValue))
        {
            int.TryParse(ruleIdValue, out ruleId);
        }
        else
        {
            return BadRequest("RuleId not provided");
        }

        var memStream = new MemoryStream();
        await file.CopyToAsync(memStream, ct);

        ExternalDataSourcePart externalDataSourcePart =
            await _externalDataSourceService.CreateExternalDataSourceIfNeeded(matterId, file.FileName, ruleId,
                UploadType.InvalidVoter);
        if (externalDataSourcePart.ExternalDataSource == null)
        {
            return BadRequest("Duplicate upload");
        }

        var backgroundOperation = new BackgroundOperation
        {
            ExecutionStatus = ExecutionStatus.Running,
            MatterId = matterId,
            OperationType = BackgroundOperationType.UploadInvalidVoterFile
        };

        _backgroundOperationRepository.Add(backgroundOperation);
        await _backgroundOperationRepository.SaveChangesAsync();

        _ = Task.Run(async () =>
        {
            using var reader = new StreamReader(memStream);
            using var scope = serviceScopeFactory.CreateScope();
            var backgroundOperationRepository =
                scope.ServiceProvider.GetRequiredService<IBackgroundOperationRepository>();
            try
            {
                var uploadHelper = new UploadExternalDataControllerHelper(scope);
                await uploadHelper.ProcessInvalidVoterFile(externalDataSourcePart.ExternalDataSource,
                    externalDataSourcePart, reader, data, matterId, ct);
                backgroundOperation.ExecutionStatus = ExecutionStatus.Succeeded;
            }
            catch (Exception ex)
            {
                backgroundOperation.ExecutionStatus = ExecutionStatus.Failed;
                backgroundOperation.Message = ex.Message;
            }
            finally
            {
                backgroundOperationRepository.SetModified(backgroundOperation);
                await backgroundOperationRepository.SaveChangesAsync();
            }
        });

        return Ok(backgroundOperation.Id);
    }

    [HttpPost("{matterId:int}/invalidcirculators")]
    [DisableRequestSizeLimit]
    public async Task<ActionResult> UploadInvalidCirculatorFile(
        IFormCollection data, IFormFile file, int matterId, [FromQuery] int ruleId, CancellationToken ct,
        [FromServices] IServiceScopeFactory serviceScopeFactory)
    {
        if (!file.FileName.EndsWith(".csv")) return BadRequest("Unrecognized File Format");
        var memStream = new MemoryStream();
        await file.CopyToAsync(memStream, ct);
        memStream.Position = 0;

        ExternalDataSourcePart externalDataSourcePart =
            await _externalDataSourceService.CreateExternalDataSourceIfNeeded(matterId, file.FileName, ruleId,
                UploadType.InvalidCirculator);

        var backgroundOperation = new BackgroundOperation
        {
            ExecutionStatus = ExecutionStatus.Running,
            MatterId = matterId,
            OperationType = BackgroundOperationType.UploadInvalidCirculatorFile
        };
        _backgroundOperationRepository.Add(backgroundOperation);
        await _backgroundOperationRepository.SaveChangesAsync();

        _ = Task.Run(async () =>
        {
            using var reader = new StreamReader(memStream);
            using var scope = serviceScopeFactory.CreateScope();
            var backgroundOperationRepository =
                scope.ServiceProvider.GetRequiredService<IBackgroundOperationRepository>();
            try
            {
                var uploadHelper = new UploadExternalDataControllerHelper(scope);
                await uploadHelper.ProcessInvalidCirculatorFile(externalDataSourcePart.ExternalDataSource, reader,
                    data);
                backgroundOperation.ExecutionStatus = ExecutionStatus.Succeeded;
            }
            catch (Exception ex)
            {
                backgroundOperation.ExecutionStatus = ExecutionStatus.Failed;
                backgroundOperation.Message = ex.Message;
            }
            finally
            {
                backgroundOperationRepository.SetModified(backgroundOperation);
                await backgroundOperationRepository.SaveChangesAsync();
            }
        });
        return Ok(backgroundOperation.Id);
    }

    [HttpGet("voters/{externalDataSourceId}")]
    public async Task<ActionResult> CheckVoterRegistrationFile(int externalDataSourceId, CancellationToken ct)
    {
        int countOfVoters =
            await _externalDataSourceRepository.CountSignaturesTiedToVotersAsync(externalDataSourceId);
        return Ok(countOfVoters);
    }

    [HttpDelete("voters/{externalDataSourceId}")]
    public async Task<ActionResult> DeleteVoterRegistrationFile(int externalDataSourceId, CancellationToken ct,
        [FromServices] IServiceScopeFactory serviceScopeFactory)
    {
        var backgroundOperation = new BackgroundOperation
        {
            ExecutionStatus = ExecutionStatus.Running,
            OperationType = BackgroundOperationType.DeleteVoterRegistrationFile
        };
        _backgroundOperationRepository.Add(backgroundOperation);
        await _backgroundOperationRepository.SaveChangesAsync();

        _ = Task.Run(async () =>
        {
            using var scope = serviceScopeFactory.CreateScope();
            var backgroundOperationRepository =
                scope.ServiceProvider.GetRequiredService<IBackgroundOperationRepository>();
            var externalDataSourceRepository =
                scope.ServiceProvider.GetRequiredService<IExternalDataSourceRepository>();
            try
            {
                await externalDataSourceRepository.DeleteByIdAsync(externalDataSourceId);
                await externalDataSourceRepository.SaveChangesAsync();
                backgroundOperation.ExecutionStatus = ExecutionStatus.Succeeded;
            }
            catch (Exception ex)
            {
                backgroundOperation.ExecutionStatus = ExecutionStatus.Failed;
                backgroundOperation.Message = ex.Message;
            }
            finally
            {
                backgroundOperationRepository.SetModified(backgroundOperation);
                await backgroundOperationRepository.SaveChangesAsync();
            }
        });
        return Ok(backgroundOperation.Id);
    }

    [HttpPost("circulators")]
    public async Task<ActionResult> UploadCirculatorsFile(IFormFile file, int matterId, CancellationToken ct)
    {
        if (!file.FileName.EndsWith(".csv")) return BadRequest("Unrecognized File Format");

        using var reader = new StreamReader(file.OpenReadStream());

        reader.BaseStream.Position = 0;
        var fileStream = reader.BaseStream;
        string? error =
            await _fileService.SaveFileStreamAsync($"matter{matterId}", $"circulators.csv", fileStream, true, ct);
        if (error != null)
        {
            return BadRequest(error);
        }

        reader.BaseStream.Position = 0;

        List<RegisteredCirculator> records = _csvParser.ReadRegisteredCirculatorRecordsFromCsv(reader);

        var externalDataSource = new ExternalDataSource
        {
            FileName = file.FileName,
            UploadType = UploadType.Circulator,
            MatterId = matterId,
            ExternalDataSourceMatters =
                new List<ExternalDataSourceMatter> { new ExternalDataSourceMatter { MatterId = matterId } },
        };
        _externalDataSourceRepository.Add(externalDataSource);
        await _externalDataSourceRepository.SaveChangesAsync();

        foreach (var record in records)
        {
            record.ExternalDataSourceId = externalDataSource.Id;
        }

        await _registeredCirculatorRepository.BulkInsertAsync(records);
        //await _registeredCirculatorRepository.SaveChangesAsync();
        return Ok();
    }

    // new route for deletecircuator
    [HttpDelete("circulators/{externalDataSourceId}")]
    public async Task<ActionResult> DeleteCirculator(int externalDataSourceId, CancellationToken ct,
        [FromServices] IServiceScopeFactory serviceScopeFactory)
    {
        var backgroundOperation = new BackgroundOperation
        {
            ExecutionStatus = ExecutionStatus.Running,
            OperationType = BackgroundOperationType.DeleteCirculatorFile
        };
        _backgroundOperationRepository.Add(backgroundOperation);
        await _backgroundOperationRepository.SaveChangesAsync();

        _ = Task.Run(async () =>
        {
            using var scope = serviceScopeFactory.CreateScope();
            var backgroundOperationRepository =
                scope.ServiceProvider.GetRequiredService<IBackgroundOperationRepository>();
            var externalDataSourceRepository =
                scope.ServiceProvider.GetRequiredService<IExternalDataSourceRepository>();
            try
            {
                await externalDataSourceRepository.DeleteByIdAsync(externalDataSourceId);
                await externalDataSourceRepository.SaveChangesAsync();
                backgroundOperation.ExecutionStatus = ExecutionStatus.Succeeded;
            }
            catch (Exception ex)
            {
                backgroundOperation.ExecutionStatus = ExecutionStatus.Failed;
                backgroundOperation.Message = ex.Message;
            }
            finally
            {
                backgroundOperationRepository.SetModified(backgroundOperation);
                await backgroundOperationRepository.SaveChangesAsync();
            }
        });
        return Ok(backgroundOperation.Id);
    }
}