﻿using Backend.Authentication;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Model.Authorization;
using DataInterface.RepositoryInterfaces;

namespace Backend.Controllers;

[Route("api/[controller]")]
public class RolesController : BaseApiController
{
    private readonly IRoleRepository _roleRepository;

    public RolesController(IRoleRepository roleRepository, ILogger<BaseApiController> logger)
        : base(logger)
    {
        _roleRepository = roleRepository;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<Role>>> GetRoles()
    {
        return await _roleRepository.GetAllAsync();
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<Role>> GetRole(int id)
    {
        var role = await _roleRepository.GetByIdAsync(id);

        if (role == null)
        {
            return NotFound();
        }

        return role;
    }

    [HttpPut("{id}")]
    [RoleCheck(RoleType.Admin)]
    public async Task<IActionResult> PutRole(int id, Role role)
    {
        if (id != role.Id)
        {
            return BadRequest();
        }

        _roleRepository.SetModified(role);

        try
        {
            await _roleRepository.SaveChangesAsync();
        }
        catch (DbUpdateConcurrencyException)
        {
            if (!await RoleExists(id))
            {
                return NotFound();
            }
            else
            {
                throw;
            }
        }

        return NoContent();
    }

    [HttpPost]
    [RoleCheck(RoleType.Admin)]
    public async Task<ActionResult<Role>> PostRole(Role role)
    {
        _roleRepository.Add(role);
        await _roleRepository.SaveChangesAsync();

        return CreatedAtAction("GetRole", new { id = role.Id }, role);
    }

    [HttpDelete("{id}")]
    [RoleCheck(RoleType.Admin)]
    public async Task<IActionResult> DeleteRole(int id)
    {
        var role = await _roleRepository.GetByIdAsync(id);
        if (role == null)
        {
            return NotFound();
        }

        _roleRepository.Remove(role);
        await _roleRepository.SaveChangesAsync();

        return NoContent();
    }

    private async Task<bool> RoleExists(int id)
    {
        return (await _roleRepository.GetByIdAsync(id)) != null;
    }
}