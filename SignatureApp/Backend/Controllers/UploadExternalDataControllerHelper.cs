﻿using Backend.Services;
using DataInterface.RepositoryInterfaces;
using DataInterface.ServiceInterfaces;
using Model.ExternalDataSources;
using Service;

namespace Backend.Controllers;

public class UploadExternalDataControllerHelper(IServiceScope scope)
{
    private readonly IDataProcessingLogRepository _dataProcessingLog = scope.ServiceProvider.GetRequiredService<IDataProcessingLogRepository>();
    private readonly IInvalidVoterRepository _invalidVoterRepository = scope.ServiceProvider.GetRequiredService<IInvalidVoterRepository>();
    private readonly IInvalidCirculatorRepository _invalidCirculatorRepository = scope.ServiceProvider.GetRequiredService<IInvalidCirculatorRepository>();
    private readonly IFileService _fileService = scope.ServiceProvider.GetRequiredService<IFileService>();
    private readonly CsvParser _csvParser = scope.ServiceProvider.GetRequiredService<CsvParser>();
    private readonly RegisteredVoterService _registeredVoterService = scope.ServiceProvider.GetRequiredService<RegisteredVoterService>();

    public async Task ProcessVoterFile(
        ExternalDataSource externalDataSource,
        ExternalDataSourcePart externalDataSourcePart,
        StreamReader reader, IFormCollection data, int matterId, string county, string fileName, CancellationToken ct)
    {

        int voterFilePartId = externalDataSourcePart.Id;
        try
        {
            await _dataProcessingLog.AddExternalDataSourcePartOperationAsync(voterFilePartId, "ProcessVoterFile started");
            reader.BaseStream.Position = 0;
            var fileStream = reader.BaseStream;

            await _fileService.SaveFileStreamAsync($"matter{matterId}", $"registrationFiles/{fileName}", fileStream, true, ct);
            await _dataProcessingLog.AddExternalDataSourcePartOperationAsync(voterFilePartId, "ProcessVoterFile uploaded to storage");
            reader.BaseStream.Position = 0;

            var wrappedForm = new AspNetCoreIncomingFormCollection(data);
            var records = _csvParser.ReadVoterRecordsFromCsv<RegisteredVoter>(reader, wrappedForm);
            await _dataProcessingLog.AddExternalDataSourcePartOperationAsync(voterFilePartId, "ProcessVoterFile read from CSV");

            foreach (var record in records)
            {
                record.ExternalDataSourceId = externalDataSource.Id;
                record.ExternalDataSourcePartId = voterFilePartId;
                record.County = county;
            }
            await _registeredVoterService.BulkUpsertAsync(records);
            await _dataProcessingLog.AddExternalDataSourcePartOperationAsync(voterFilePartId, "ProcessVoterFile finished");        }
        catch (Exception ex)
        {
            await _dataProcessingLog.AddExternalDataSourcePartOperationAsync(voterFilePartId, ex.ToString());
        }
    }

    public async Task<int> ProcessInvalidVoterFile(ExternalDataSource externalDataSource, ExternalDataSourcePart externalDataSourcePart, StreamReader reader, IFormCollection data, int matterId, CancellationToken ct)
    {
        var wrappedForm = new AspNetCoreIncomingFormCollection(data);
        var records = _csvParser.ReadVoterRecordsFromCsv<InvalidVoter>(reader, wrappedForm);

        foreach (var record in records)
        {
            record.ExternalDataSourceId = externalDataSource.Id;
            record.ExternalDataSourcePartId = externalDataSourcePart.Id;
        }
        await _invalidVoterRepository.BulkInsertAsync(records);
        await _invalidVoterRepository.SaveChangesAsync();
        return records.Count;
    }

    public async Task<int> ProcessInvalidCirculatorFile(ExternalDataSource externalDataSource, StreamReader reader, IFormCollection data)
    {
        var wrappedForm = new AspNetCoreIncomingFormCollection(data);
        var records = _csvParser.ReadCirculatorRecordsFromCsv<InvalidCirculator>(reader, wrappedForm);

        foreach (var record in records)
        {
            record.ExternalDataSourceId = externalDataSource.Id;
        }
        await _invalidCirculatorRepository.BulkInsertAsync(records);
        await _invalidCirculatorRepository.SaveChangesAsync();
        return records.Count;
    }

}