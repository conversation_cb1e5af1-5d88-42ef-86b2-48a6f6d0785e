using Backend.Authentication;
using DataInterface.RepositoryInterfaces;
using DataInterface.ServiceInterfaces;
using Microsoft.AspNetCore.Mvc;
using Model.Authorization;
using Model.Templates;
using Service;

namespace Backend.Controllers;

[Route("api/upload/templates")]
[RoleCheck(RoleType.Admin)]
public class UploadTemplatesController : BaseApiController
{
    private readonly IFileService _fileService;
    private readonly ITemplateRepository _templateRepository;
    private readonly ITemplatePageRepository _templatePageRepository;
    private readonly PdfManipulationService _pdfManipulation;
    private readonly TemplateHierarchyService _templateHierarchyService;

    public UploadTemplatesController(
        IFileService fileService,
        ILogger<BaseApiController> logger,
        ITemplateRepository templateRepository,
        ITemplatePageRepository templatePageRepository,
        PdfManipulationService pdfManipulation,
        TemplateHierarchyService templateHierarchyService)
        : base(logger)
    {
        _fileService = fileService;
        _pdfManipulation = pdfManipulation;
        _templateRepository = templateRepository;
        _templatePageRepository = templatePageRepository;
        _templateHierarchyService = templateHierarchyService;
    }

    [HttpPost("")]
    public async Task<ActionResult> UploadTemplateForm(IFormFile file, int templateId, string pageSize,
        int usStateId, CancellationToken ct)
    {
        var template = await _templateRepository.GetByIdAsync(templateId);
        if (template == null)
        {
            return BadRequest();
        }

        // First put this into a memory stream, because we are going to be looking through it multiple times
        var memStream = new MemoryStream();
        await file.CopyToAsync(memStream, ct);
        memStream.Position = 0;

        var supportedPageSize = (SupportedPageSize)Enum.Parse(typeof(SupportedPageSize), pageSize);
        template.PageSize = supportedPageSize;

        if (file.FileName.EndsWith(".pdf"))
        {
            // We might need to adjust the rotation before we do anything else
            memStream = await _pdfManipulation.AdjustPdfOrientationAsync(memStream, supportedPageSize);
        }

        string? error = await _fileService.SaveFileStreamAsync($"templates",
            $"{template.Name}{Path.GetExtension(file.FileName)}", memStream, true, ct);
        if (error != null)
        {
            return BadRequest(error);
        }

        memStream.Position = 0;
        var pages = await _templatePageRepository.GetByTemplateIdAsync(templateId);
        if (pages.Count > 0)
        {
            await _templateHierarchyService.DeleteTemplateHierarchy(templateId, shouldDeleteTemplate: false);
        }

        await _templateHierarchyService.CreateTemplateHierarchy(template, memStream);

        template.FileName = file.FileName;
        template.UsStateId = usStateId;
        _templateRepository.SetModified(template);
        await _templateRepository.SaveChangesAsync();

        return Ok();
    }

}