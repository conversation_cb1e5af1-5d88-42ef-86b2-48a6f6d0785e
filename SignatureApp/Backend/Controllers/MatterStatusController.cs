﻿using AutoMapper;
using Backend.Authentication;
using Backend.DTO;
using Backend.DTO.Matters;
using DataInterface.RepositoryInterfaces;
using Microsoft.AspNetCore.Mvc;
using Model.Authorization;
using Model.SignatureSheets;
using Model.Workflow;

namespace Backend.Controllers;

[Route("api/matters")] // {matterId}/status
[RoleCheck(RoleType.Admin, RoleType.Manager)]
public class MatterStatusController : BaseApiController
{
    private readonly IInvalidSheetRepository _invalidSheetRepository;
    private readonly IMatterRepository _matterRepository;
    private readonly IMatterVariableRepository _matterVariableRepository;
    private readonly IMapper _mapper;
    private readonly IRegisteredCirculatorRepository _registeredCirculatorRepository;
    private readonly IRegisteredVoterRepository _registeredVoterRepository;
    private readonly IRuleRepository _ruleRepository;
    private readonly ISignatureSheetRowRepository _signatureSheetRowRepository;
    private readonly ISignatureSheetRepository _signatureSheetRepository;
    private readonly ITemplateSignatureColumnRepository _templateSignatureColumnRepository;
    private readonly ITemplateRepository _templateRepository;
    private readonly ITranscribableFieldRepository _transcribableFieldRepository;
    private readonly ITaskRepository _taskRepository;
    private readonly IWorkRepository _workRepository;

    public MatterStatusController(
        IInvalidSheetRepository invalidSheetRepository,
        ILogger<BaseApiController> logger,
        IMatterRepository matterRepository,
        IMatterVariableRepository matterVariableRepository,
        IMapper mapper,
        IRegisteredCirculatorRepository registeredCirculatorRepository,
        IRegisteredVoterRepository registeredVoterRepository,
        IRuleRepository ruleRepository,
        ISignatureSheetRowRepository signatureSheetRowRepository,
        ISignatureSheetRepository signatureSheetRepository,
        ITemplateSignatureColumnRepository templateSignatureColumnRepository,
        ITaskRepository taskRepository,
        ITemplateRepository templateRepository,
        ITranscribableFieldRepository transcribableFieldRepository,
        IWorkRepository workRepository
    )
        : base(logger)
    {
        _invalidSheetRepository = invalidSheetRepository;
        _matterRepository = matterRepository;
        _matterVariableRepository = matterVariableRepository;
        _mapper = mapper;
        _registeredCirculatorRepository = registeredCirculatorRepository;
        _registeredVoterRepository = registeredVoterRepository;
        _ruleRepository = ruleRepository;
        _signatureSheetRowRepository = signatureSheetRowRepository;
        _signatureSheetRepository = signatureSheetRepository;
        _templateSignatureColumnRepository = templateSignatureColumnRepository;
        _taskRepository = taskRepository;
        _templateRepository = templateRepository;
        _transcribableFieldRepository = transcribableFieldRepository;
        _workRepository = workRepository;
    }

    [HttpGet("{matterId}/status/signatures")]
    public async Task<ActionResult<DocumentUploadInfoDTO>> GetUploadInfo(int matterId)
    {
        var uploadInfo = await _signatureSheetRepository.GetUploadInfoByMatterIdAsync(matterId);
        var result = _mapper.Map<DocumentUploadInfoDTO>(uploadInfo);
        result.TotalInvalidCount = await _invalidSheetRepository.GetCountByMatterIdAsync(matterId);
        return Ok(result);
    }

    [HttpGet("{matterId}/status/voters")]
    public async Task<ActionResult<List<DocumentUploadInfoByCountyDTO>>> GetVoterRegistrationInfo(int matterId)
    {
        var uploadInfo = await _registeredVoterRepository.GetUploadInfoByMatterIdAsync(matterId);
        return Ok(_mapper.Map<List<DocumentUploadInfoByCountyDTO>>(uploadInfo));
    }

    [HttpGet("{matterId}/status/circulators")]
    public async Task<ActionResult<DocumentUploadInfoDTO>> GetCirculatorRegistrationInfo(int matterId)
    {
        var uploadInfo = await _registeredCirculatorRepository.GetUploadInfoByMatterIdAsync(matterId);
        return Ok(_mapper.Map<DocumentUploadInfoDTO>(uploadInfo));
    }

    [HttpGet("{matterId}/status/progress")]
    public async Task<ActionResult<MatterProgressDTO>> GetProgress(int matterId)
    {
        var matter = await _matterRepository.GetByIdAsync(matterId);
        if (matter is null) return NotFound($"Matter {matterId} not found");

        var validityCounts = (await _signatureSheetRowRepository.GetRowsGroupedByValidityForMatterAsync(matterId))
            .ToDictionary(vc => vc.Validity, vc => vc.Count);

        validityCounts.TryGetValue(Validity.Invalid, out int deficient);
        validityCounts.TryGetValue(Validity.Valid, out int reviewed);
        validityCounts.TryGetValue(Validity.Strikethrough, out int strikethough);
        validityCounts.TryGetValue(Validity.Unknown, out int remaining);
        return new MatterProgressDTO
        {
            Deficient = deficient,
            Remaining = remaining,
            Reviewed = reviewed,
            Strikethrough = strikethough,
            Threshold = matter.NumberSignaturesRequired
        };
    }

    [HttpGet("{matterId}/status/checklist")]
    public async Task<ActionResult<ChecklistItem[]>> GetChecklist(int matterId)
    {
        var matter = await _matterRepository.GetByIdAsync(matterId);
        if (matter is null) return NotFound($"Matter {matterId} not found");

        var matterVariables = await _matterVariableRepository.GetByMatterIdAsync(matterId);
        var uploadTemplates = new ChecklistItem(ChecklistStep.UploadTemplates)
        {
            Status = (await _templateRepository.GetCountAsync()) > 0,
        };
        var setupTemplates = new ChecklistItem(ChecklistStep.SetupTemplates)
        {
            Status = (await _transcribableFieldRepository.GetCountAsync()) > 0 &&
                     (await _templateSignatureColumnRepository.GetCountAsync()) > 0,
        };
        var setupParameters = new ChecklistItem(ChecklistStep.SetupParameters)
        {
            Status = (matterVariables.Count > 0) && matterVariables.Count(mv => string.IsNullOrEmpty(mv.Value)) == 0,
        };
        var uploadCirculators = new ChecklistItem(ChecklistStep.UploadCirculators)
        {
            Status = (await _registeredCirculatorRepository.GetCountByMatterIdAsync(matterId)) > 0,
        };
        var uploadVoters = new ChecklistItem(ChecklistStep.UploadVoters)
        {
            Status = (await _registeredVoterRepository.GetUploadInfoByMatterIdAsync(matterId)).Count > 0,
        };
        var createTasks = new ChecklistItem(ChecklistStep.CreateTasks)
        {
            Status = (await _taskRepository.GetCountByMatterIdAsync(matterId)) > 0,
        };
        var uploadSignatures = new ChecklistItem(ChecklistStep.UploadSignatures)
        {
            //TODO - for this one we need to check the number of sheets * the templates tables number of rows
            //   to make sure it is greater than the matters number of required signatures
            Status = (await _signatureSheetRepository.GetCountByMatterIdAsync(matterId)) > 0,
        };
        var createRules = new ChecklistItem(ChecklistStep.CreateRules)
        {
            Status = (await _ruleRepository.GetCountByMatterIdAsync(matterId)) > 0,
        };

        var totalWork = await _workRepository.GetCountByMatterIdAndExpressionAsync(matterId, w => w != null);
        var unfinishedWork =
            await _workRepository.GetCountByMatterIdAndExpressionAsync(matterId,
                w => w.WorkStatus != WorkStatus.Completed);
        var finishTranscription = new ChecklistItem(ChecklistStep.FinishTranscription)
        {
            Status = totalWork > 0 && unfinishedWork == 0,
        };

        return new ChecklistItem[]
        {
            uploadTemplates,
            setupTemplates,
            setupParameters,
            uploadCirculators,
            uploadVoters,
            createTasks,
            uploadSignatures,
            createRules,
            finishTranscription,
        };
    }
}