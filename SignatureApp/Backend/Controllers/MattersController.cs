﻿using AutoMapper;
using Backend.DTO;
using Microsoft.AspNetCore.Mvc;
using Model.Matters;
using Backend.Authentication;
using Model.Authorization;
using DataInterface.RepositoryInterfaces;
using CsvHelper.Configuration;
using System.Globalization;
using CsvHelper;
using Model.Deficiencies;

namespace Backend.Controllers;

[Route("api/[controller]")]
[RoleCheck(RoleType.Admin, RoleType.Manager)]
public class MattersController : BaseApiController
{
    private readonly IMapper _mapper;
    private readonly IMatterRepository _matterRepository;
    private readonly ISignatureSheetRowRepository _signatureSheetRowRepository;

    public MattersController(
        ILogger<BaseApiController> logger,
        IMapper mapper,
        IMatterRepository matterRepository,
        ISignatureSheetRowRepository signatureSheetRowRepository
    )
        : base(logger)
    {
        _mapper = mapper;
        _matterRepository = matterRepository;
        _signatureSheetRowRepository = signatureSheetRowRepository;
    }

    [HttpGet]
    public async Task<ActionResult<List<GetMattersDTO>>> GetMatters()
    {
        return _mapper.Map<List<GetMattersDTO>>(await _matterRepository.GetAllActiveMattersAsync());
    }

    [HttpGet("{matterId}")]
    public async Task<ActionResult<GetMatterDTO>> GetMatter(int matterId)
    {
        return _mapper.Map<GetMatterDTO>(await _matterRepository.GetActiveMatterAsync(matterId));
    }

    [HttpPost]
    [RoleCheck(RoleType.Admin)]
    public async Task<ActionResult> CreateMatter(CreateMatterDTO dto)
    {
        var matter = _mapper.Map<Matter>(dto);
        _matterRepository.Add(matter);

        // var newMatterVariable = new MatterVariable { Key = "SheetNumberRegex", Value = "", Matter = matter };
        // _matterVariableRepository.Add(newMatterVariable);

        await _matterRepository.SaveChangesAsync();

        return CreatedAtAction(nameof(CreateMatter), new { id = matter.Id }, matter);
    }

    [HttpPut("{matterId}")]
    [RoleCheck(RoleType.Admin)]
    public async Task<ActionResult> UpdateMatter(int matterId, UpdateMatterDTO dto)
    {
        var matter = await _matterRepository.GetByIdAsync(matterId);

        if (matter is null) return NotFound($"Matter {matterId} not found.");

        matter.Name = dto.Name;
        matter.NumberSignaturesRequired = dto.NumberSignaturesRequired;
        matter.DueDate = dto.DueDate;
        matter.Type = dto.Type != null
            ? (MatterType)dto.Type
            : MatterType.Unknown;

        _matterRepository.SetModified(matter);
        await _matterRepository.SaveChangesAsync();

        return Ok();
    }

    [HttpDelete("{matterId}")]
    [RoleCheck(RoleType.Admin)]
    public async Task<ActionResult> DeleteMatter(int matterId)
    {
        var matter = await _matterRepository.GetByIdAsync(matterId);
        if (matter is null) return NotFound($"Matter {matterId} not found");

        matter.IsActive = false;
        _matterRepository.SetModified(matter);
        await _matterRepository.SaveChangesAsync();

        return Ok();
    }

    [HttpGet("{matterId}/validsignatures/csv")]
    public async Task<ActionResult> GetValidSignaturesCsv(int matterId)
    {
        var rows = await _signatureSheetRowRepository.GetAllValidByMatterIdAsync(matterId);
        if (rows.Count == 0) return NoContent();

        var headers = new[]
            {
                nameof(DeficiencyExport.SheetNumber), nameof(DeficiencyExport.LineNumber),
                nameof(DeficiencyExport.SignerFirstName), nameof(DeficiencyExport.SignerLastName),
                nameof(DeficiencyExport.SignerVoterId)
            }
            .ToList();

        var csvConfig = new CsvConfiguration(CultureInfo.InvariantCulture)
        {
            ReadingExceptionOccurred = args =>
            {
                if (args.Exception is FieldValidationException)
                    return false;

                return true;
            }
        };

        using var memoryStream = new MemoryStream();
        using var streamWriter = new StreamWriter(memoryStream);
        var csv = new CsvWriter(streamWriter, csvConfig);

        foreach (var header in headers)
        {
            csv.WriteField(header);
        }

        csv.NextRecord();

        foreach (var row in rows)
        {
            var sheet = row.SignatureSheet;
            if (sheet == null || row == null)
            {
                continue;
            }

            csv.WriteField(sheet.SheetNumber);
            csv.WriteField(row.RowNumber);
            csv.WriteField(row.RegisteredVoter?.FirstName);
            csv.WriteField(row.RegisteredVoter?.LastName);
            csv.WriteField(row.RegisteredVoterId);
            csv.NextRecord();
        }

        streamWriter.Close();

        return File(memoryStream.ToArray(), "text/csv");
    }
}