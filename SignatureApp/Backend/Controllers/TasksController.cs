﻿using Microsoft.AspNetCore.Mvc;
using Model.Workflow;
using Backend.DTO.Tasks;
using AutoMapper;
using Backend.Authentication;
using Model.Templates;
using DataInterface.RepositoryInterfaces;
using Model.Authorization;
using Service;

namespace Backend.Controllers;

[Route("api/[controller]")]
[RoleCheck(RoleType.Admin)]
public class TasksController : BaseApiController
{
    private readonly IMapper _mapper;
    private readonly IMatterRepository _matterRepository;
    private readonly ITemplateSignatureColumnRepository _templateSignatureColumnRepository;
    private readonly ITaskRepository _taskRepository;
    private readonly ITemplateRepository _templateRepository;
    private readonly TaskGenerationService _taskGenerationService;
    private readonly ITranscribableFieldRepository _transcribableFieldRepository;

    public TasksController(
        ILogger<BaseApiController> logger,
        IMapper mapper,
        IMatterRepository matterRepository,
        ITemplateSignatureColumnRepository templateSignatureColumnRepository,
        ITaskRepository taskRepository,
        TaskGenerationService taskGenerationService,
        ITemplateRepository templateRepository,
        ITranscribableFieldRepository transcribableFieldRepository
    )
        : base(logger)
    {
        _mapper = mapper;
        _matterRepository = matterRepository;
        _templateSignatureColumnRepository = templateSignatureColumnRepository;
        _taskRepository = taskRepository;
        _taskGenerationService = taskGenerationService;
        _templateRepository = templateRepository;
        _transcribableFieldRepository = transcribableFieldRepository;
    }

    [HttpGet("matters/{matterId}/all")]
    public async Task<List<GetTaskDTO>> GetAll(int matterId)
    {
        var tasks = await _taskRepository.GetAllByMatterIdAsync(matterId);

        return _mapper.Map<List<GetTaskDTO>>(tasks);
    }

    [HttpGet("matters/{matterId}/count")]
    public async Task<int> GetCount(int matterId)
    {
        var count = await _taskRepository.GetCountByMatterIdAsync(matterId);

        return _mapper.Map<int>(count);
    }


    [HttpPost("matters/{matterId}/all")]
    public async Task<ActionResult> CreateAll(int matterId, int? templateId)
    {
        var matter = await _matterRepository.GetByIdAsync(matterId);
        if (matter == null)
        {
            return BadRequest();
        }

        List<Template>? templates;
        if (templateId != null)
        {
            var template = await _templateRepository.GetByIdAsync(templateId.Value);
            if (template == null)
            {
                return BadRequest("Template not found");
            }

            templates = new List<Template> { template };
        }
        else
        {
            // Get all the template for this matter
            templates = await _templateRepository.GetAllByMattterAsync(matterId);
        }

        foreach (var template in templates)
        {
            await _taskGenerationService.CreateTasksForTemplateAndMatterAsync(template, matter);
        }

        await _taskRepository.SaveChangesAsync();
        return Ok();
    }


    [HttpPost]
    public async Task<ActionResult> CreateTask(CreateTaskDTO dto)
    {
        switch (dto.TaskType)
        {
            case TaskType.TranscribableField:
                var transcribableField = await _transcribableFieldRepository.GetByIdAsync(dto.TranscribableFieldId);
                if (transcribableField is null)
                    return NotFound($"Transcribable Field {dto.TranscribableFieldId} does not exist");
                break;
            case TaskType.SignatureTableColumn:
                var templateSignatureColumn =
                    await _templateSignatureColumnRepository.GetByIdAsync(dto.FirstSignatureColumnId);
                if (templateSignatureColumn is null)
                    return NotFound($"Signature Column {dto.FirstSignatureColumnId} does not exist");
                break;
            default:
                return BadRequest($"TaskType {dto.TaskType} is not supported");
        }

        var task = _mapper.Map<Model.Workflow.Task>(dto);
        _taskRepository.Add(task);
        await _taskRepository.SaveChangesAsync();

        return CreatedAtAction(nameof(CreateTask), new { task.Id });
    }

    [HttpPut("{taskId}")]
    public async Task<ActionResult> UpdateTask(UpdateTaskDTO dto, int taskId)
    {
        var task = await _taskRepository.GetByIdAsync(taskId);

        if (task is null) return NotFound($"Task: {taskId} Not found");

        task = _mapper.Map<Model.Workflow.Task>(dto);
        task.Id = task.Id;

        _taskRepository.SetModified(task);
        await _taskRepository.SaveChangesAsync();

        return Ok();
    }

    [HttpGet("{taskId}")]
    public async Task<ActionResult<GetTaskDTO>> GetTask(int taskId)
    {
        var task = await _taskRepository.GetByIdAsync(taskId);

        if (task is null) return NotFound($"Task {taskId} not found.");

        return _mapper.Map<GetTaskDTO>(task);
    }
}