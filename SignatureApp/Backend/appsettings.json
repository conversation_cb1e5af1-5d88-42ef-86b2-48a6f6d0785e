{
  "ApiKey": "",
  "EmailConfiguration": {
    "SendGridApiKey": "itsASecretHarry",
    "From": "<EMAIL>",
  },
  "Jwt": {
    "Key": "RandomSecretNeedToMakeThis128BitsAtLeast",
    "Issuer": "https://localhost:7280",
    "Audience": "https://localhost:3000",
    "EmailKey": "SomeOtherRandomSecretatleast128Bits"
  },
  "Database": {
    "ChunkSize": 10000
  },
  "ConnectionStrings": {
    "AzureBlobStorage": "",
    "SqlServer": ""
  },
  "FormRecognizer": {
    "Endpoint": "",
    "ApiKey": ""
  },
  "FunctionApp": {
    "Endpoint": "https://veracityingressfunctionapp.azurewebsites.net/",
    "FunctionKey": ""
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore.Database.Command": "Warning",
      "Microsoft.EntityFrameworkCore.Infrastructure": "Warning"
    },
    "ApplicationInsights": {
      "LogLevel": {
        "Default": "Information"
      }
    }
  },
  "AllowedHosts": "*",
  "ApplicationInsights": {
    "ConnectionString": "InstrumentationKey=7868fbca-03a1-4eed-b079-9c65788f1117;IngestionEndpoint=https://westus-0.in.applicationinsights.azure.com/;LiveEndpoint=https://westus.livediagnostics.monitor.azure.com/quickpulseservice.svc"
  }
}