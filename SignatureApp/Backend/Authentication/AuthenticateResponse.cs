﻿using Model.Authorization;

namespace Backend.Authentication;

public class AuthenticateResponse
{
    public int Id { get; set; }
    public int RoleId { get; set; }
    public string? Role { get; set; }
    public string? ShortName { get; set; }
    public string? FullName { get; set; }
    public string AccessToken { get; set; }


#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
    // For serialization
    public AuthenticateResponse()
    {
    }

    public AuthenticateResponse(User user, string accessToken)
    {
        Id = user.Id;
        RoleId = user.RoleId;
        ShortName = user.ShortName;
        FullName = user.FullName;
        AccessToken = accessToken;
        Role = Enum.GetName(typeof(RoleType), RoleId);
    }
}