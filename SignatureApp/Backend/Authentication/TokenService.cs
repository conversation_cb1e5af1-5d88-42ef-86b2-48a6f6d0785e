﻿using Backend.Configuration;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace Backend.Authentication;

public interface ITokenService
{
    string BuildJwt(string email, TimeSpan expiryDuration);
    string? GetJwtName(string token);
    string GetEmailValidationToken(string token);
}

public class TokenService : ITokenService
{
    private readonly string _key;
    private readonly string? _issuer;
    private readonly string? _audience;
    private readonly string _emailKey;

    public TokenService(IOptions<JwtConfig> jwtOptions)
    {
        JwtConfig jwtConfig = jwtOptions.Value ?? throw new ArgumentNullException(nameof(jwtOptions));
        _key = jwtConfig.Key ?? throw new InvalidOperationException("JWT key is not configured.");
        _issuer = jwtConfig.Issuer;
        _audience = jwtConfig.Audience;
        _emailKey = jwtConfig.EmailKey ?? throw new InvalidOperationException("Email key is not configured.");
    }

    public string BuildJwt(string email, TimeSpan expiryDuration)
    {
        var claims = new[]
        {
            new Claim(JwtRegisteredClaimNames.Sub, email),
        };

        var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_key));
        var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256Signature);
        // TODO: Should this be UtcNow?
        var tokenDescriptor = new JwtSecurityToken(_issuer, _audience, claims,
            expires: DateTime.Now.Add(expiryDuration), signingCredentials: credentials);

        return new JwtSecurityTokenHandler().WriteToken(tokenDescriptor);
    }

    public string? GetJwtName(string payload)
    {
        var handler = new JwtSecurityTokenHandler();
        var tokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuerSigningKey = true,
            ValidateIssuer = true,
            ValidIssuer = _issuer,
            ValidateAudience = true,
            ValidAudience = _audience,
            ValidIssuers = new List<string?> { _issuer },
            ValidateLifetime = true,
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_key)),
        };

        var token = handler.ValidateToken(payload, tokenValidationParameters, out _);

        var claim = token.Claims.FirstOrDefault(c => c.Type == JwtRegisteredClaimNames.Sub);
        return claim?.Value;
    }

    public string GetEmailValidationToken(string payload)
    {
        var handler = new JwtSecurityTokenHandler();
        var tokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuerSigningKey = true,
            ValidateIssuer = true,
            ValidIssuer = _issuer,
            ValidateAudience = true,
            ValidAudience = _audience,
            ValidIssuers = new List<string?> { _issuer },
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_emailKey)),
        };

        var token = handler.ValidateToken(payload, tokenValidationParameters, out _);

        var claim = token.Claims.FirstOrDefault(c => c.Type == JwtRegisteredClaimNames.Sub);
        return claim?.Value ?? string.Empty;
    }
}