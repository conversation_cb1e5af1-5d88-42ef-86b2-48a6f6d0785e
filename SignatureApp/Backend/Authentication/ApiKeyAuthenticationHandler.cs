using System.Security.Claims;
using System.Text.Encodings.Web;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.JsonWebTokens;

namespace Backend.Authentication;

public class ApiKeyAuthenticationHandler : AuthenticationHandler<ApiKeyAuthenticationOptions>
{
    public ApiKeyAuthenticationHandler(IOptionsMonitor<ApiKeyAuthenticationOptions> options,
        ILoggerFactory logger, UrlEncoder encoder) : base(options, logger, encoder)
    {
    }
/*
    protected override async Task InitializeHandlerAsync()
    {
    }
*/
#pragma warning disable CS1998 // Async method lacks 'await' operators and will run synchronously
    protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        if (!Request.Headers.TryGetValue("Authorization", out var authHeader))
        {
            return AuthenticateResult.Fail("Missing Authorization header");
        }
        if (authHeader.First() == null)
        {
            return AuthenticateResult.Fail("Invalid Authorization header");
        }

        var parts = authHeader.First()?.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        if (parts?[0] != "ApiKey")
        {
            return AuthenticateResult.NoResult();
        }

        var providedApiKey = parts[1];
        var expectedApiKey = Options.ApiKey;

        if (string.IsNullOrEmpty(providedApiKey) || providedApiKey != expectedApiKey)
        {
            return AuthenticateResult.Fail("Invalid API Key");
        }

        var claims = new[]
        {
            new Claim(JwtRegisteredClaimNames.Sub, "<EMAIL>"),
        };

        var identity = new ClaimsIdentity(claims, Scheme.Name);
        var principal = new ClaimsPrincipal(identity);
        var ticket = new AuthenticationTicket(principal, Scheme.Name);

        return AuthenticateResult.Success(ticket);
    }
#pragma warning restore CS1998 // Async method lacks 'await' operators and will run synchronously

}