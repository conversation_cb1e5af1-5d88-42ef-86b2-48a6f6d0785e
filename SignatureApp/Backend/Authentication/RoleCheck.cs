﻿using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using DataInterface.RepositoryInterfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Model.Authorization;

namespace Backend.Authentication;

public class RoleCheck : Attribute, IAuthorizationFilter
{
    private readonly IList<RoleType> _roles;
    public RoleCheck (params RoleType[] roles)
    {
        _roles = roles;
    }

    public void OnAuthorization(AuthorizationFilterContext context)
    {
        var userRepository = context.HttpContext
            .RequestServices
            .GetRequiredService<IUserRepository>();

        var identity = context.HttpContext.User.Identity as ClaimsIdentity;
        if (identity == null)
        {
            context.Result = new ObjectResult("Insufficient Access") { StatusCode = 403 };
            return;
        }
        var subject = identity.Claims.FirstOrDefault(c => c.Type == JwtRegisteredClaimNames.Sub);
        if (subject?.Value != null)
        {
            var user = userRepository.GetByEmailAsync(subject.Value).Result;

            if (user == null || !_roles.Contains((RoleType)user.RoleId))
            {
                context.Result = new ObjectResult("Insufficient Access") { StatusCode = 403 };
            }
        }
    }
}

