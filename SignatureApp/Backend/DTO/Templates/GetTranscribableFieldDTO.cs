﻿using Microsoft.AspNetCore.Mvc;
using Model.Templates;

namespace Backend.DTO;

public class GetTranscribableFieldDTO
{
    public int Id { get; set; }
    public FileContentResult Image { get; set; } = default!;
    public string? Name { get; set; }
    public bool IsSkipped { get; set; }
    public bool IsHandwritten { get; set; }
    public bool CanBeInvalid { get; set; }
    public bool IsSignature { get; set; }
    public bool IsDate { get; set; }
    public bool IsPrefixed { get; set; }
    public string Prefix { get; set; } = default!;
    public InputType InputType { get; set; }
    public string? GroupName { get; set; }
    public int PageNumber { get; set; }
}