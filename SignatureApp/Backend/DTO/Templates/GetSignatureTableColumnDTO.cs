﻿using Microsoft.AspNetCore.Mvc;

namespace Backend.DTO
{
    public class GetSignatureTableColumnDTO
    {
        public int Id { get; set; }
        public FileContentResult Image { get; set; } = default!;
        public string? Name { get; set; }
        public bool IsSkipped { get; set; }
        public bool IsHandwritten { get; set; }
        public bool CanBeInvalid { get; set; }
        public bool IsSignature { get; set; }
        public bool IsName { get; set; }
        public bool IsAddress { get; set; }
        public bool IsDate { get; set; }
        public bool IsVoterId { get; set; }
    }

}
