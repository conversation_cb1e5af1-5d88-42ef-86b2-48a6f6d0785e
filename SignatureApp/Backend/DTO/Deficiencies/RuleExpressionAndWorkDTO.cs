using Service.ServiceModels;

namespace Backend.DTO.Deficiencies;

public class RuleExpressionAndWorkDTO
{
    public required string Expression { get; set; }
    public GetWorkDTO? Work { get; set; }

    public MatterVariableDTO? MatterVariable { get; set; }
    public BoundaryPointDTO? SignatoryPoint { get; set; }
    public CirculatorDTO? Circulator { get; set; }
    public SignatoryDTO? Signatory { get; set; }
}

public class SignatoryDTO
{
    public string FirstName { get; set; } = default!;
    public string LastName { get; set; } = default!;
    public string? Address { get; set; }
    public string? City { get; set; }
    public string? State { get; set; }
    public string? PostalCode { get; set; }
}

public class CirculatorDTO
{
    public string? RegistrationId { get; set; }
    public string Name { get; set; } = default!;
    public string? Address { get; set; }
    public string? City { get; set; }
    public string? State { get; set; }
    public string? PostalCode { get; set; }
}