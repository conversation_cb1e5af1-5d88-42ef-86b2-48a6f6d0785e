using Microsoft.AspNetCore.Mvc;

namespace Backend.DTO.SignatureSheets;

public class SignatureTableDTO
{
    public FileContentResult? SheetImage { get; set; }
    public float ImageAspectRatio { get; set; }
    public int PageNumber { get; set; }
    public int TemplateId { get; set; }
    public RowDTO[]? Rows { get; set; }
    public ColumnDTO[]? Columns { get; set; }
    public double Left { get; set; }
    public double Top { get; set; }
    public double Right { get; set; }
    public double Bottom { get; set; }
}

public class SignatureSheetTableDTO : SignatureTableDTO
{
    public int SignatureSheetId { get; set; }
    public SignatureSheetDTO? SignatureSheet { get; set; }
}

public class TemplateSignatureTableDTO : SignatureTableDTO
{
    public TemplateDTO? Template { get; set; }
}


public class ColumnDTO
{
    public int Id { get; set; }
    public int ColumnIndex { get; set; }
    public double Left { get; set; }
    public double Top { get; set; }
    public double Right { get; set; }
    public double Bottom { get; set; }
    public string? Name { get; set; }
    public bool IsMissing { get; set; }
}

public class RowDTO
{
    public int Id { get; set; }
    public double Left { get; set; }
    public double Top { get; set; }
    public double Right { get; set; }
    public double Bottom { get; set; }
    public bool IsReviewed { get; set; }
    public int RowIndex { get; set; }
    public int RowNumber { get; set; }
    public bool IsMissing { get; set; }
}

public class SignatureSheetDTO
{
    public int Id { get; set; }
    public int SheetNumber { get; set; }
    public PageDTO[]? Pages { get; set; }
}

public class TemplateDTO
{
    public int Id { get; set; }
    public PageDTO[]? Pages { get; set; }
}

public class PageDTO
{
    public int PageNumber { get; set; }
    public double Height { get; set; }
    public double Width { get; set; }
}