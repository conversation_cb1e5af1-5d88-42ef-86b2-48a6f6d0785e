﻿using AutoMapper;
using Backend.DTO;
using Backend.DTO.Deficiencies;
using Backend.DTO.ExternalDataSource;
using Backend.DTO.Groups;
using Backend.DTO.Rules;
using Backend.DTO.SignatureSheets;
using Backend.DTO.StaffGroups;
using Backend.DTO.Tasks;
using Backend.DTO.Templates;
using Backend.DTO.Users;
using Microsoft.AspNetCore.Mvc;
using Model.Authorization;
using Model.Deficiencies;
using Model.DTOs;
using Model.ExternalDataSources;
using Model.Geocoding;
using Model.Matters;
using Model.Rules;
using Model.SignatureSheets;
using Model.Templates;
using Model.Workflow;
using Service;
using Service.ServiceModels;

namespace Backend.Mapping
{
    public class BackendMappingProfile : Profile
    {
        public BackendMappingProfile()
        {
            CreateMap<User, UserDTO>()
                .ForMember(destination => destination.Role,
                opt => opt.MapFrom(source => Enum.GetName(typeof(RoleType), source.RoleId)));

            CreateMap<UserDTO, User>();

            CreateMap<InviteUserDTO, User>();

            CreateMap<Role, RoleDTO>();

            CreateMap<BackgroundOperation, GetLastRunDTO>();

            CreateMap<CreateMatterDTO, Matter>();
            CreateMap<Matter, GetMatterDTO>();
            CreateMap<Matter, GetMattersDTO>();
            CreateMap<Matter, UpdateMatterDTO>();

            CreateMap<CreateTemplateDTO, Template>();
            CreateMap<Template, GetTemplateDTO>();
            CreateMap<Template, TemplateDTO>();
            CreateMap<TemplateSignatureTable, TemplateSignatureTableDTO>();
            CreateMap<TemplatePage, PageDTO>();
            CreateMap<TemplateSignatureRow, RowDTO>();
            CreateMap<TemplateSignatureColumn, ColumnDTO>();

            CreateMap<CreateGroupDTO, StaffGroup>();

            CreateMap<TranscribableField, GetTranscribableFieldDTO>()
                .ForMember(dest => dest.PageNumber, opt => opt.MapFrom(src => src.TemplatePage.PageNumber));
            CreateMap<UpdateTranscribableFieldDTO, TranscribableField>();
            CreateMap<TemplateSignatureColumn, GetSignatureTableColumnDTO>();
            CreateMap<UpdateSignatureTableColumnDTO, TemplateSignatureColumn>();

            CreateMap<CreateTaskDTO, Model.Workflow.Task>();
            CreateMap<UpdateTaskDTO, Model.Workflow.Task>();
            CreateMap<GetTaskDTO, Model.Workflow.Task>();
            CreateMap<Model.Workflow.Task, GetTaskDTO>();
            CreateMap<StaffGroup, StaffGroupDTO>();

            CreateMap<RuleSummaryDTO, Rule>();
            CreateMap<Rule, RuleSummaryDTO>();
            CreateMap<Rule, RuleDTO>();
            CreateMap<ViolationCriterion, Rule>();
            CreateMap<Rule, ViolationCriterion>()
                .ForMember(dest => dest.RuleId, opt => opt.MapFrom(src => src.Id));

            CreateMap<DeficiencyDTO, Deficiency>();
            CreateMap<Deficiency, DeficiencyDTO>();

            CreateMap<DeficiencyDTO, DeficiencyResult>();
            CreateMap<Deficiency, DeficiencyResult>();

            CreateMap<DeficiencyReview, DeficiencyReviewDTO>();
            CreateMap<DeficiencyReviewDTO, DeficiencyReview>();

            CreateMap<FileContentResult?, byte[]?>().ConvertUsing<BytesToFileContentResultConverter>();
            CreateMap<byte[]?, FileContentResult?>().ConvertUsing<BytesToFileContentResultConverter>();
            CreateMap<DeficiencyResult, DeficiencyDTO>()
                .ForMember(dest => dest.RuleName, opt => opt.MapFrom(src => src.Rule.Name))
                ;

            CreateMap<Deficiency, DeficiencyWithRuleDTO>()
                .ForMember(dest => dest.Deficiency, opt => opt.MapFrom(src => src))
                .ForMember(dest => dest.Rule, opt => opt.MapFrom(src => src.Rule))
                ;
            CreateMap<RuleExpressionAndWork, RuleExpressionAndWorkDTO>();

            CreateMap<MatterVariable, MatterVariableDTO>()
                .ForMember(dest => dest.MatterVariableId, opt => opt.MapFrom(src => src.Id));

            CreateMap<ExternalDataSource, ExternalDataSourceDTO>();
            CreateMap<ExternalDataSourceDTO, ExternalDataSource>();
            CreateMap<ExternalDataSourcePart, ExternalDataSourcePartDTO>();
            CreateMap<ExternalDataSourcePartDTO, ExternalDataSourcePart>();

            CreateMap<WorkMatterStatus, MatterWorkStatusDTO>();
            CreateMap<Work, GetWorkDTO>();
            CreateMap<WorkDTO, GetWorkDTO>();

            CreateMap<Circulator, CirculatorDTO>()
                .ForMember(dest => dest.State,
                    opt =>
                        opt.MapFrom(src => src.UsStateId > 0 ? UsStates.FromId(src.UsStateId).Abbreviation : null));

            CreateMap<Signatory, SignatoryDTO>()
                .ForMember(dest => dest.Address, opt =>
                    opt.MapFrom(src => AddressParsingService.CreateAddressLine1(src)))
                .ForMember(dest => dest.State,
                    opt =>
                        opt.MapFrom(src => src.UsStateId > 0 ? UsStates.FromId(src.UsStateId).Abbreviation : null));


            CreateMap<DocumentUploadInfo, DocumentUploadInfoDTO>()
                .ForMember(dest => dest.LastUploadedOn,
                    opt => opt.MapFrom(src =>
                        src.LastUploadedOn != null
                        ? src.LastUploadedOn.Value.ToShortDateString()
                        : null));
            CreateMap<DocumentUploadInfoByCounty, DocumentUploadInfoByCountyDTO>()
                .ForMember(dest => dest.LastUploadedOn,
                    opt => opt.MapFrom(src =>
                        src.LastUploadedOn != null
                        ? src.LastUploadedOn.Value.ToShortDateString()
                        : null));

            CreateMap<SignatureSheet, SignatureSheetDTO>();
            CreateMap<SignatureSheetTable, SignatureSheetTableDTO>();
            CreateMap<SignatureSheetPage, PageDTO>();
            CreateMap<SignatureSheetRow, RowDTO>();
            CreateMap<SignatureSheetColumn, ColumnDTO>();

            CreateMap<RegisteredVoter, VoterSearchDTO>()
                .ForMember(dest => dest.State,
                    opt => opt.MapFrom(src => src.UsState.Abbreviation));
            CreateMap<VoterSearchDTO, RegisteredVoter>();
        }

        private class BytesToFileContentResultConverter : ITypeConverter<FileContentResult?, byte[]?>, ITypeConverter<byte[]?, FileContentResult?>
        {
            public byte[]? Convert(FileContentResult? source, byte[]? destination, ResolutionContext context)
                => source?.FileContents;

            public FileContentResult? Convert(byte[]? source, FileContentResult? destination, ResolutionContext context)
                => source != null ? new FileContentResult(source, "image/png") : null;
        }
    }
}
