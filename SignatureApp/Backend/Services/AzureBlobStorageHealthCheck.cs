using Azure.Storage.Blobs;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Backend.Services;

/// <summary>
/// Azure Blob Storage health check.
/// </summary>
public sealed class AzureBlobStorageHealthCheck : BaseHealthCheckService
{
    private readonly BlobServiceClient? _blobServiceClient;


    public AzureBlobStorageHealthCheck(BlobServiceClient blobServiceClient, string serviceDescription) : base(serviceDescription)
    {
        _blobServiceClient = blobServiceClient;
    }

    /// <inheritdoc />
    public override async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        if (_blobServiceClient == null)
        {
            return new HealthCheckResult(context.Registration.FailureStatus, "BlobServiceClient is not configured.");
        }
        try
        {
            // Note: BlobServiceClient.GetPropertiesAsync() cannot be used with only the role assignment
            // "Storage Blob Data Contributor," so BlobServiceClient.GetBlobContainersAsync() is used instead to probe service health.
            // However, BlobContainerClient.GetPropertiesAsync() does have sufficient permissions.
            await _blobServiceClient
                .GetBlobContainersAsync(cancellationToken: cancellationToken)
                .AsPages(pageSizeHint: 1)
                .GetAsyncEnumerator(cancellationToken)
                .MoveNextAsync()
                .ConfigureAwait(false);

            return new HealthCheckResult(HealthStatus.Healthy, ServiceDescription);
        }
        catch (Exception ex)
        {
            return new HealthCheckResult(context.Registration.FailureStatus, description: ServiceDescription, exception: ex);
        }
    }
}
