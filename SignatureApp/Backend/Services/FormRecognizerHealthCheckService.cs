using Azure;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Service;

namespace Backend.Services;

public class FormRecognizerHealthCheckService : BaseHealthCheckService
{
    private readonly SimpleFormRecognizerService _formRecognizerService;
    private readonly ILogger<FormRecognizerHealthCheckService> _logger;

    private static readonly string Base64Document =
        "JVBERi0xLjQKJeLjz9MKMiAwIG9iago8PC9MZW5ndGggNjIvRmlsdGVyL0ZsYXRlRGVjb2RlPj5zdHJlYW0KeJwr5HIK4TIyVbAwNFIISeEyUNA1tAAx9N0MFUBCaVwaHqk5OfkK4flFOSmaIVlAJQYgBa4hXIFcALj2DooKZW5kc3RyZWFtCmVuZG9iago0IDAgb2JqCjw8L1R5cGUvUGFnZS9NZWRpYUJveFswIDAgNTk1IDg0Ml0vUmVzb3VyY2VzPDwvUHJvY1NldCBbL1BERiAvVGV4dCAvSW1hZ2VCIC9JbWFnZUMgL0ltYWdlSV0vRm9udDw8L0YxIDEgMCBSPj4+Pi9Db250ZW50cyAyIDAgUi9QYXJlbnQgMyAwIFI+PgplbmRvYmoKMSAwIG9iago8PC9UeXBlL0ZvbnQvU3VidHlwZS9UeXBlMS9CYXNlRm9udC9IZWx2ZXRpY2EvRW5jb2RpbmcvV2luQW5zaUVuY29kaW5nPj4KZW5kb2JqCjMgMCBvYmoKPDwvVHlwZS9QYWdlcy9Db3VudCAxL0tpZHNbNCAwIFJdL0lUWFQoMy40LjE0LjApPj4KZW5kb2JqCjUgMCBvYmoKPDwvVHlwZS9DYXRhbG9nL1BhZ2VzIDMgMCBSPj4KZW5kb2JqCjYgMCBvYmoKPDwvUHJvZHVjZXIoaVRleHRTaGFycC5MR1BMdjIuQ29yZSAzLjQuMTQuMCkvQ3JlYXRpb25EYXRlKEQ6MjAyNTA2MTAxNzU2MzgtMDcnMDAnKS9Nb2REYXRlKEQ6MjAyNTA2MTAxNzU2MzgtMDcnMDAnKT4+CmVuZG9iagp4cmVmCjAgNwowMDAwMDAwMDAwIDY1NTM1IGYgCjAwMDAwMDAzMDAgMDAwMDAgbiAKMDAwMDAwMDAxNSAwMDAwMCBuIAowMDAwMDAwMzg4IDAwMDAwIG4gCjAwMDAwMDAxNDMgMDAwMDAgbiAKMDAwMDAwMDQ1NCAwMDAwMCBuIAowMDAwMDAwNDk5IDAwMDAwIG4gCnRyYWlsZXIKPDwvU2l6ZSA3L1Jvb3QgNSAwIFIvSW5mbyA2IDAgUi9JRCBbPDY4OGQ1YTgwNjJkNjM3YzJhMTVhNGI3MDA1ZGI5ZWRhPjw2ZDRmNGUxMzIzZmY3ZTgyZmRkMmU2NWQwODA3NTQxZT5dPj4Kc3RhcnR4cmVmCjYzMgolJUVPRgo=";

    public FormRecognizerHealthCheckService(
        SimpleFormRecognizerService formRecognizerService,
        ILogger<FormRecognizerHealthCheckService> logger, string serviceDescription) : base(serviceDescription)
    {
        _formRecognizerService = formRecognizerService;
        _logger = logger;
    }

    public override async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = new CancellationToken())
    {
        var isHealthy = await IsFormRecognizerServiceHealthyAsync();
        var status = isHealthy ? HealthStatus.Healthy : HealthStatus.Unhealthy;
        return new HealthCheckResult(status, description: ServiceDescription);
    }

    public async Task<bool> IsFormRecognizerServiceHealthyAsync()
    {
        try
        {
            var documentStream = new MemoryStream(Convert.FromBase64String(Base64Document));
            var result = await _formRecognizerService.AnalyzeDocumentAsync(documentStream);
            return result != null && result.Pages.Count > 0;
        }
        catch (RequestFailedException ex) when (ex.Status != 401)
        {
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Form Recognizer service health check failed");
            return false;
        }
    }
}