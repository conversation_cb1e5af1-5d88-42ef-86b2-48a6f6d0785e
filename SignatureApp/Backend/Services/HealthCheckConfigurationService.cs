using Microsoft.Extensions.Diagnostics.HealthChecks;
using Model.Geocoding;

namespace Backend.Services;

public static class HealthCheckConfigurationService
{
    private const string MISSING_URL = "missing:url";

    public static void ConfigureHealthCheck(this IServiceCollection services, ConfigurationManager configuration)
    {
        var ingressFunctionEndpoint =
            new Uri(configuration["IngressFunctionApp:Endpoint"] ?? MISSING_URL);
        var documentIntelligenceEndpoint =
            new Uri(configuration["DocumentIntelligenceApp:Endpoint"] ?? MISSING_URL);
        var updateUserStatsFunction =
            new Uri(configuration["UpdateUserStatsApp:Endpoint"] ?? MISSING_URL);

        var connString = configuration["ConnectionStrings:SqlServer"] ?? MISSING_URL;

        var pollingInterval = configuration.GetValue("HealthCheckUI:PollingInterval", 60 * 60);

        var sendgridApiKey = configuration["EmailConfiguration:SendGridApiKey"] ?? string.Empty;

        var azureBlobStorageDescription = $"ConnectionStrings:AzureBlobStorage | KeyLength: {configuration["ConnectionStrings:AzureBlobStorage"]?.Length ?? 0}";
        var azureQueueStorageDescription = $"ConnectionStrings:AzureQueueStorage | KeyLength: {configuration["ConnectionStrings:AzureQueueStorage"]?.Length ?? 0}";
        var geoCodingDescription = $"Keys:AzureMapsApiKey | KeyLength {configuration["Keys:AzureMapsApiKey"]?.Length ?? 0}";
        var mapPlacesDescription = $"Keys:GoogleMapsApiKey | KeyLength {configuration["Keys:GoogleMapsApiKey"]?.Length ?? 0}";
        var formRecognizerDescription = $"URL: {configuration["FormRecognizer:Endpoint"]} | KeyLength: {configuration["FormRecognizer:ApiKey"]?.Length ?? 0}";
        var sendGridDescription = $"EmailConfiguration:SendGridApiKey | KeyLength: {configuration["EmailConfiguration:SendGridApiKey"]?.Length ?? 0}";
        var sqlServerDescription = $"ConnectionStrings:SqlServer | KeyLength: {connString.Length}";
        var testAddress = new AddressInput
        {
            AddressLine = "1700 W Washington St",
            City = "Phoenix",
            PostalCode = "85007",
            State = "AZ",
        };

        services.AddHealthChecks()
            .AddAzureBlobStorage(azureBlobStorageDescription)
            .AddAzureQueueStorage(azureQueueStorageDescription)
            .AddGeoCodingHealthCheck(testAddress, geoCodingDescription)
            .AddFormRecognizerHealthCheck(formRecognizerDescription)
            .AddMapPlacesHealthCheck(mapPlacesDescription)
            .AddSendGrid(sendgridApiKey, serviceDescription: sendGridDescription)
            .AddSqlServer(connString, serviceDescription: sqlServerDescription);

        services.AddHealthChecksUI(setup =>
        {
            setup.AddHealthCheckEndpoint("Backend", "/api/health");

            setup.AddHealthCheckEndpoint("Document Intelligence App",
                new Uri(documentIntelligenceEndpoint, "api/HealthCheckFunction").ToString());

            setup.AddHealthCheckEndpoint("Ingress Function App",
                new Uri(ingressFunctionEndpoint, "api/HealthCheckFunction").ToString());

            setup.AddHealthCheckEndpoint("Update User Stats Function App",
                new Uri(updateUserStatsFunction, "api/HealthCheckFunction").ToString());

            setup.UseApiEndpointHttpMessageHandler(sp => sp.GetRequiredService<FunctionEndpointHandler>());
            setup.SetEvaluationTimeInSeconds(pollingInterval);
            setup.MaximumHistoryEntriesPerEndpoint(500);
        }).AddInMemoryStorage();
    }
}