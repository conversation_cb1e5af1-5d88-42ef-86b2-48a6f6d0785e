using Azure.Storage.Blobs;
using Azure.Storage.Queues;
using DataInterface.ServiceInterfaces;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Model.Geocoding;
using Service;
namespace Backend.Services;

public static class HealthCheckBuilderExtensions
{
    public static IHealthChecksBuilder AddAzureBlobStorage(
        this IHealthChecksBuilder builder,
        string serviceDescription,
       Func<IServiceProvider, BlobServiceClient>? clientFactory = default,
        string name = "Azure Blob Storage",
        HealthStatus? failureStatus = default,
        IEnumerable<string>? tags = default,
        TimeSpan? timeout = default)
    {
        return builder.Add(new HealthCheckRegistration(
          name,

          sp => new AzureBlobStorageHealthCheck(
                   blobServiceClient: clientFactory?.Invoke(sp) ?? sp.GetRequiredService<BlobServiceClient>(),
                     serviceDescription: serviceDescription
                   ),
          failureStatus,
          tags,
          timeout));
    }

    public static IHealthChecksBuilder AddAzureQueueStorage(
       this IHealthChecksBuilder builder,
       string serviceDescription,
       Func<IServiceProvider, QueueServiceClient>? clientFactory = default,
       string name = "Azure Queue Storage",
       HealthStatus? failureStatus = default,
       IEnumerable<string>? tags = default,
       TimeSpan? timeout = default)
    {
        return builder.Add(new HealthCheckRegistration(
           name,
           sp => new AzureQueueStorageHealthCheck(
                    queueServiceClient: clientFactory?.Invoke(sp) ?? sp.GetRequiredService<QueueServiceClient>(),
                    serviceDescription: serviceDescription),
           failureStatus,
           tags,
           timeout));
    }

    public static IHealthChecksBuilder AddGeoCodingHealthCheck(
        this IHealthChecksBuilder builder,
        AddressInput testAddress,
        string serviceDescription,
        Func<IServiceProvider, IGeocodingService>? geocodingServiceFactory = default,
        Func<IServiceProvider, ILogger<GeocodingHealthCheckService>>? loggerFactory = default,
        string name = "Geocoding Service",
        HealthStatus? failureStatus = default,
        IEnumerable<string>? tags = default,
        TimeSpan? timeout = default)
    {
        return builder.Add(new HealthCheckRegistration(
            name,
            sp => new GeocodingHealthCheckService(geocodingServiceFactory?.Invoke(sp) ?? sp.GetRequiredService<IGeocodingService>(),
            loggerFactory?.Invoke(sp) ?? sp.GetRequiredService<ILogger<GeocodingHealthCheckService>>(), testAddress, serviceDescription),
            failureStatus,
            tags,
            timeout));
    }

    public static IHealthChecksBuilder AddMapPlacesHealthCheck(
        this IHealthChecksBuilder builder,
        string serviceDescription,
        Func<IServiceProvider, IMapPlacesService>? mapPlacesServiceFactory = default,
        Func<IServiceProvider, ILogger<MapPlacesHealthCheckService>>? loggerFactory = default,
        string name = "Map Places Service",
        HealthStatus? failureStatus = default,
        IEnumerable<string>? tags = default,
        TimeSpan? timeout = default)
    {
        return builder.Add(new HealthCheckRegistration(
            name,
            sp => new MapPlacesHealthCheckService(
                mapPlacesService: mapPlacesServiceFactory?.Invoke(sp) ?? sp.GetRequiredService<IMapPlacesService>(),
                logger: loggerFactory?.Invoke(sp) ?? sp.GetRequiredService<ILogger<MapPlacesHealthCheckService>>(),
                serviceDescription: serviceDescription),
            failureStatus,
            tags,
            timeout));
    }

    public static IHealthChecksBuilder AddFormRecognizerHealthCheck(
        this IHealthChecksBuilder builder,

        string serviceDescription,
        Func<IServiceProvider, SimpleFormRecognizerService>? formRecognizerFactory = default,
        Func<IServiceProvider, ILogger<FormRecognizerHealthCheckService>>? loggerFactory = default,
        string name = "Form Recognizer Service",
        HealthStatus? failureStatus = default,
        IEnumerable<string>? tags = default,
        TimeSpan? timeout = default)
    {
        return builder.Add(new HealthCheckRegistration(
            name,
            sp => new FormRecognizerHealthCheckService(
                formRecognizerService: formRecognizerFactory?.Invoke(sp) ?? sp.GetRequiredService<SimpleFormRecognizerService>(),
                logger: loggerFactory?.Invoke(sp) ?? sp.GetRequiredService<ILogger<FormRecognizerHealthCheckService>>(),
                serviceDescription: serviceDescription),
            failureStatus,
            tags,
            timeout));
    }

    public static IHealthChecksBuilder AddSendGrid(
        this IHealthChecksBuilder builder,
        string apiKey,
        string serviceDescription,
        Func<IServiceProvider, IHttpClientFactory>? httpClientFactory = default,
        string? name = default,
        HealthStatus? failureStatus = default,
        IEnumerable<string>? tags = default,
        TimeSpan? timeout = default)
    {
        return builder.Add(new HealthCheckRegistration(
            name ?? "SendGrid Email Service",
            sp => new SendGridHealthCheck(
                apiKey: apiKey,
                httpClientFactory: httpClientFactory?.Invoke(sp) ?? sp.GetRequiredService<IHttpClientFactory>(),
                serviceDescription),
            failureStatus,
            tags,
            timeout));
    }

    public static IHealthChecksBuilder AddSqlServer(
        this IHealthChecksBuilder builder,
        string connectionString,
        string serviceDescription,
        string? name = default,
        HealthStatus? failureStatus = default,
        IEnumerable<string>? tags = default,
        TimeSpan? timeout = default)
    {
        return builder.Add(new HealthCheckRegistration(
            name ?? "SQL Server Database",
            sp => new SqlServerHealthCheck(connectionString, serviceDescription),
            failureStatus,
            tags,
            timeout));
    }

}