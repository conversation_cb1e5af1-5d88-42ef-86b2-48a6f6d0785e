using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Backend.Services;

/// <summary>
/// A health check for SqlServer services.
/// </summary>
public class SqlServerHealthCheck : BaseHealthCheckService
{
    private readonly string? _connectionString;

    public SqlServerHealthCheck(string connectionString, string serviceDescription): base(serviceDescription)
        
    {
        _connectionString = connectionString;
    }

    /// <inheritdoc />
    public override async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        if(string.IsNullOrWhiteSpace(_connectionString))
        {
            return new HealthCheckResult(context.Registration.FailureStatus, "Connection string is not configured.");
        }
        try
        {
            using var connection = new SqlConnection(_connectionString);

            // _options.Configure?.Invoke(connection);
            await connection.OpenAsync(cancellationToken).ConfigureAwait(false);

            using var command = connection.CreateCommand();
            command.CommandText = "SELECT 1;";
            object result = await command.ExecuteScalarAsync(cancellationToken).ConfigureAwait(false);

            return new HealthCheckResult(HealthStatus.Healthy, ServiceDescription);
        }
        catch (Exception ex)
        {
            return new HealthCheckResult(context.Registration.FailureStatus, description:ServiceDescription, exception: ex);
        }
    }
}
