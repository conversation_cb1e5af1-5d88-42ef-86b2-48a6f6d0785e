using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Backend.Services;

public abstract class BaseHealthCheckService : IHealthCheck
{
    public string ServiceDescription { get; set; }

    public BaseHealthCheckService(string serviceDescription)
    {
        ServiceDescription = serviceDescription;
    }

    public abstract Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default);

}