using DataInterface.ServiceInterfaces;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Model.Geocoding;

namespace Backend.Services;

public class GeocodingHealthCheckService : BaseHealthCheckService
{
    private readonly IGeocodingService _geocodingService;
    private readonly ILogger<GeocodingHealthCheckService> _logger;
    private readonly AddressInput _testAddress;


    public GeocodingHealthCheckService(
        IGeocodingService geocodingService,
        ILogger<GeocodingHealthCheckService> logger,
        AddressInput testAddress, string serviceDescription):base(serviceDescription)
    {
        _geocodingService = geocodingService;
        _logger = logger;
        _testAddress = testAddress;

    }

    public override async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = new CancellationToken())
    {
        var isHealthy = await IsGeocodingServiceHealthyAsync();
        var status = isHealthy ? HealthStatus.Healthy : HealthStatus.Unhealthy;
        return new HealthCheckResult(status, description: ServiceDescription);
    }

    public async Task<bool> IsGeocodingServiceHealthyAsync()
    {
        try
        {
            _logger.LogInformation("Starting geocoding service health check");


            var result = await _geocodingService.GeocodeAsync(_testAddress);
            if (!result.IsSuccess || result.Value == null)
            {
                _logger.LogWarning("Geocoding service returned null result");
                return false;
            }

            // Validate that we got meaningful coordinates
            var exactMatch = result.Value.ExactMatch;
            if (exactMatch == null || (exactMatch.Latitude == 0 && exactMatch.Longitude == 0))
            {
                _logger.LogWarning("Geocoding service returned zero coordinates");
                return false;
            }

            _logger.LogInformation("Geocoding service health check passed successfully");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Geocoding service health check failed");
            return false;
        }
    }
}