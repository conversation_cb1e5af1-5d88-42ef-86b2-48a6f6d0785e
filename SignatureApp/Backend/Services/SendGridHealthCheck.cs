using System.Net;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using SendGrid;
using SendGrid.Helpers.Mail;

namespace Backend.Services;

public class SendGridHealthCheck : BaseHealthCheckService
{
    private const string MAIL_ADDRESS_NAME = "Health Check User";
    private const string MAIL_ADDRESS = "<EMAIL>";
    private const string SUBJECT = "Checking health is Fun";
    private const string NAME = "sendgrid";

    private readonly string _apiKey;
    private readonly IHttpClientFactory? _httpClientFactory;

    public SendGridHealthCheck(string apiKey, IHttpClientFactory httpClientFactory, string serviceDescription) : base(serviceDescription)
    {
        _apiKey = apiKey ?? "";
        _httpClientFactory = httpClientFactory;
    }

    /// <inheritdoc />
    public override async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        if(string.IsNullOrWhiteSpace(_apiKey))
        {
            return new HealthCheckResult(context.Registration.FailureStatus, "API key is not configured.");
        }
        if (_httpClientFactory == null)
        {
            return new HealthCheckResult(context.Registration.FailureStatus, "HTTP client factory is not configured.");
        }
        try
        {
            using var httpClient = _httpClientFactory.CreateClient(NAME);

            var client = new SendGridClient(httpClient, _apiKey);
            var from = new EmailAddress(MAIL_ADDRESS, MAIL_ADDRESS_NAME);
            var to = new EmailAddress(MAIL_ADDRESS, MAIL_ADDRESS_NAME);
            var msg = MailHelper.CreateSingleEmail(from, to, SUBJECT, SUBJECT, null);
            msg.SetSandBoxMode(true);

            var response = await client.SendEmailAsync(msg, cancellationToken).ConfigureAwait(false);

            if (response.StatusCode != HttpStatusCode.OK)
            {
                return new HealthCheckResult(context.Registration.FailureStatus,
                    $"Sending an email to SendGrid using the sandbox mode is not responding with 200 OK, the current status is {response.StatusCode}",
                    null,
                    new Dictionary<string, object>
                    {
                        { "responseStatusCode", (int)response.StatusCode }
                    });
            }

            return new HealthCheckResult(HealthStatus.Healthy, description: ServiceDescription);
        }
        catch (Exception ex)
        {
            return new HealthCheckResult(context.Registration.FailureStatus, description: ServiceDescription,  exception: ex);
        }
    }
}
