using Azure.Storage.Queues;
using HealthChecks.Azure.Storage.Queues;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Backend.Services;

/// <summary>
/// Azure Queue Storage health check.
/// </summary>
public sealed class AzureQueueStorageHealthCheck : BaseHealthCheckService
{

    private readonly QueueServiceClient? _queueServiceClient;
    private readonly string _queueUrl;

    public AzureQueueStorageHealthCheck(QueueServiceClient queueServiceClient, string serviceDescription):base(serviceDescription)
    {
        _queueServiceClient =queueServiceClient;
        _queueUrl = serviceDescription;

    }

    /// <inheritdoc />
    public override async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        if (_queueServiceClient == null)
        {
            return new HealthCheckResult(context.Registration.FailureStatus, "QueueServiceClient is not configured.");
        }
        try
        {
            // throw new Exception("This is a test exception to check the health check service.");
            // Note: QueueServiceClient.GetPropertiesAsync() cannot be used with only the role assignment
            // "Storage Queue Data Contributor," so QueueServiceClient.GetQueuesAsync() is used instead to probe service health.
            // However, QueueClient.GetPropertiesAsync() does have sufficient permissions.
            await _queueServiceClient
                .GetQueuesAsync(cancellationToken: cancellationToken)
                .AsPages(pageSizeHint: 1)
                .GetAsyncEnumerator(cancellationToken)
                .MoveNextAsync()
                .ConfigureAwait(false);

            return new HealthCheckResult(HealthStatus.Healthy, ServiceDescription);
        }
        catch (Exception ex)
        {
            return new HealthCheckResult(context.Registration.FailureStatus, description: ServiceDescription, exception: ex);
        }
    }
}
