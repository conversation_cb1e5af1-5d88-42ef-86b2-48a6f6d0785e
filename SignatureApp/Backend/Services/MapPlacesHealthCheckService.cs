using DataInterface.ServiceInterfaces;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Model.Geocoding;

namespace Backend.Services;

public class MapPlacesHealthCheckService : BaseHealthCheckService
{
    private readonly IMapPlacesService _mapPlacesService;
    private readonly ILogger<MapPlacesHealthCheckService> _logger;

    public MapPlacesHealthCheckService(
        IMapPlacesService mapPlacesService,
        ILogger<MapPlacesHealthCheckService> logger, string serviceDescription) : base(serviceDescription)
    {
        _mapPlacesService = mapPlacesService;
        _logger = logger;
    }

    public string KeyLength { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }


    public override async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = new CancellationToken())
    {
        var isHealthy = await IsMapPlacesServiceHealthyAsync();
        var status = isHealthy ? HealthStatus.Healthy : HealthStatus.Unhealthy;
        return new HealthCheckResult(status, description: ServiceDescription);
    }

    public async Task<bool> IsMapPlacesServiceHealthyAsync()
    {
        try
        {
            _logger.LogInformation("Starting map places service health check");

            // Empire State Building
            var result = await _mapPlacesService.GetPlaceTypeAsync(40.7484m, -73.9857m);

            if (!result.IsSuccess || result.Value == null)
            {
                _logger.LogWarning("Map places service returned null result");
                return false;
            }

            if (!result.Value.Categories.Any())
            {
                _logger.LogWarning("Map places service returned empty result");
                return false;
            }

            _logger.LogInformation("Map places service health check passed successfully");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Map places service health check failed");
            return false;
        }
    }
}