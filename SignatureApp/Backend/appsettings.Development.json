{
  "ConnectionStrings": {
    "AzureBlobStorage": "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1;QueueEndpoint=http://127.0.0.1:10001/devstoreaccount1;TableEndpoint=http://127.0.0.1:10002/devstoreaccount1;",
  },
  "HealthCheckUI":{
    "PollingInterval": 360000
  },
  "SplitQueries": true,
  "Logging": {
    "LogLevel": {
      "Microsoft.EntityFrameworkCore.Database.Command": "Warning" // show in Dev
    },
    "EnableRequestLogging" : true,
    "LogRequestParameters" : true
  },
  "Aspects":{
    "RepositoryLogging": true,
    "LogEnumerables": true
  }
}
