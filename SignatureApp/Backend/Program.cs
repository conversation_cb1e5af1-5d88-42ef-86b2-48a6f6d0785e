using Azure.Storage.Blobs;
using Backend.Authentication;
using Backend.Configuration;
using Backend.Middleware;
using Backend.Services;
using Data;
using DataInterface;
using DataInterface.Configuration;
using Microsoft.ApplicationInsights.AspNetCore.Extensions;
using Microsoft.ApplicationInsights.Extensibility.Implementation;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Service;
using Service.Email;
using System.Net.Mime;
using System.Reflection;
using System.Text;
using Azure.Storage.Queues;
using DataInterface.RepositoryInterfaces;
using HealthChecks.UI.Client;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddControllers();
builder.Services.AddAuthorization();
var jwtKey = builder.Configuration["Jwt:Key"];
var jwtIssuer = builder.Configuration["Jwt:Issuer"];
var jwtAudience = builder.Configuration["Jwt:Audience"];
var emailKey = builder.Configuration["Jwt:EmailKey"];

if (string.IsNullOrEmpty(jwtKey))
{
    throw new InvalidOperationException("JWT configuration is missing.");
}

builder.Services
    .AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(opt =>
    {
        opt.MapInboundClaims = false;
        opt.TokenValidationParameters = new()
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ClockSkew = TimeSpan.Zero,
            ValidateIssuerSigningKey = true,
            ValidIssuer = jwtIssuer,
            ValidAudience = jwtAudience,
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtKey))
        };
    });

builder.Services
    .AddAuthentication("ApiKey")
    .AddScheme<ApiKeyAuthenticationOptions, ApiKeyAuthenticationHandler>("ApiKey",
        options => { options.ApiKey = builder.Configuration["ApiKey"] ?? throw new InvalidOperationException(); });

builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("ApiKeyOrBearer", policy =>
    {
        policy.AddAuthenticationSchemes(JwtBearerDefaults.AuthenticationScheme, "ApiKey");
        policy.RequireAuthenticatedUser();
    });
});

// Configuration Options
var emailConfig = builder.Configuration.GetSection(EmailConfiguration.SectionName);
builder.Services.Configure<EmailConfiguration>(emailConfig);
var jwtConfig = builder.Configuration.GetSection(JwtConfig.SectionName);
builder.Services.Configure<JwtConfig>(jwtConfig);
var databaseConfig = builder.Configuration.GetSection(DatabaseConfig.SectionName);
builder.Services.Configure<DatabaseConfig>(databaseConfig);


// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(options =>
{
    var xmlFilename = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    options.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, xmlFilename));
});

// Dependency Injection place
builder.Services.AddHttpContextAccessor();
var connString = builder.Configuration["ConnectionStrings:SqlServer"];
bool splittingBehavior = Convert.ToBoolean(builder.Configuration["SplitQueries"]);
builder.Services.AddDbContext<SignatureAppDbContext>(options =>
    options.UseSqlServer(connString,
        opts =>
        {
            opts.UseNetTopologySuite();
            opts.EnableRetryOnFailure();
            opts.UseQuerySplittingBehavior(splittingBehavior
                ? QuerySplittingBehavior.SplitQuery
                : QuerySplittingBehavior.SingleQuery);
        })
);
builder.Services.AddAutoMapper(AppDomain.CurrentDomain.GetAssemblies());

builder.Services.AddScoped<IEmailSender, SendGridEmailSender>();
builder.Services.AddSingleton<ITokenService, TokenService>();
builder.Services.AddScoped(_ => new BlobServiceClient(builder.Configuration.GetConnectionString("AzureBlobStorage")));
builder.Services.AddScoped(_ => new QueueServiceClient(builder.Configuration.GetConnectionString("AzureQueueStorage")));
builder.Services.AddScoped<IUserContext, AspNetCoreUserContext>();
builder.Services.AddFunctionClient("IngressFunctionApp","Download", builder.Configuration);

builder.Services.AddScoped<FunctionEndpointHandler>();
DataRegistrationService.RegisterRepositories(builder.Services);
ServiceRegistrationService.RegisterServices(builder.Services);

//builder.Logging.ClearProviders();
builder.Logging.AddConsole();
var aiOptions = new ApplicationInsightsServiceOptions
{
    ConnectionString = builder.Configuration["APPINSIGHTS_CONNECTIONSTRING"],
    EnableAdaptiveSampling = false,
    //EnableQuickPulseMetricStream = false,
};
builder.Services.AddApplicationInsightsTelemetry(aiOptions);

builder.Services.Configure<FormOptions>(x =>
{
    x.ValueLengthLimit = int.MaxValue;
    x.MultipartBodyLengthLimit = int.MaxValue; // In case of multipart
});

builder.Services.ConfigureHealthCheck(builder.Configuration);

var aspectLoggingEnabled = builder.Configuration.GetValue("Aspects:RepositoryLogging", false);
var aspectLogEnumerables = builder.Configuration.GetValue("Aspects:LogEnumerables", false);

var app = builder.Build();
var enableRequestLogging = builder.Configuration.GetValue("Logging:EnableRequestLogging", false);

if (enableRequestLogging)
{
    app.UseMiddleware<RequestLoggingMiddleware>();
}

var aspectLogger = app.Services.GetRequiredService<ILogger<RepositoryLoggingAspect>>();
RepositoryLoggingAspect.Initialize(aspectLoggingEnabled,
    aspectLogEnumerables, aspectLogger);
using (var scope = app.Services.CreateScope())
{
    var boundaryRepository = scope.ServiceProvider.GetRequiredService<IBoundaryRepository>();
    await AddressParsingService.InitializeAsync(boundaryRepository);
}

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
    builder.Logging.AddConsole();
    TelemetryDebugWriter.IsTracingDisabled = true;
}
else
{
    app.UseExceptionHandler(exceptionHandlerApp =>
    {
        exceptionHandlerApp.Run(async context =>
        {
            context.Response.StatusCode = StatusCodes.Status500InternalServerError;

            // using static System.Net.Mime.MediaTypeNames;
            context.Response.ContentType = MediaTypeNames.Text.Plain;

            await context.Response.WriteAsync("An exception was thrown.");

            var exceptionHandlerPathFeature =
                context.Features.Get<IExceptionHandlerPathFeature>();

            if (exceptionHandlerPathFeature?.Error is FileNotFoundException)
            {
                await context.Response.WriteAsync("The file was not found.");
            }
        });
    });
}

app.UseHttpsRedirection();

// global cors policy
app.UseCors(x => x
    .AllowAnyOrigin()
    .AllowAnyMethod()
    .AllowAnyHeader());

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();
app.MapHealthChecks("/api/health", new HealthCheckOptions()
{
    Predicate = _ => true,
    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
});
app.MapHealthChecksUI(setup =>
{
    setup.PageTitle = "Health Checks";
    setup.AddCustomStylesheet("health-check.css");
});

app.Run();

public partial class Program { }