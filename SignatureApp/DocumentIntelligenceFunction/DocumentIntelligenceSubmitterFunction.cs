using System.Text;
using Azure.Storage.Blobs;
using Azure.Storage.Queues.Models;
using DataInterface.RepositoryInterfaces;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Model.DataTransformation;
using Model.DTOs;
using Newtonsoft.Json;
using Service.ServiceModels;

namespace DocumentIntelligenceFunction
{
    public class DocumentIntelligenceSubmitterFunction
    {
        private readonly IDataTransformationStepStartRepository _dataTransformationStepStartRepository;
        private readonly IDataTransformationStepResultRepository _dataTransformationStepResultRepository;
        private readonly ILogger<DocumentIntelligenceSubmitterFunction> _logger;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly BlobServiceClient _blobServiceClient;

        public DocumentIntelligenceSubmitterFunction(
            IDataTransformationStepStartRepository dataTransformationStepStartRepository,
            IDataTransformationStepResultRepository dataTransformationStepResultRepository,
            IHttpClientFactory httpClientFactory,
            BlobServiceClient blobServiceClient,
            ILogger<DocumentIntelligenceSubmitterFunction> logger)
        {
            _dataTransformationStepStartRepository = dataTransformationStepStartRepository;
            _dataTransformationStepResultRepository = dataTransformationStepResultRepository;
            _httpClientFactory = httpClientFactory;
            _blobServiceClient = blobServiceClient;
            _logger = logger;
        }

        // This function is triggered by the queue message
        [Function(nameof(DocumentIntelligenceSubmitterFunction))]
        public async Task Run(
            [QueueTrigger("document-intelligence", Connection = "ConnectionStrings:AzureQueueStorage")]
            QueueMessage queueMessage,
            FunctionContext executionContext)
        {
            var message = JsonConvert.DeserializeObject<DocumentMessage>(queueMessage.MessageText);
            if (message == null)
            {
                _logger.LogWarning($"Message is null, returning");
                return;
            }
            var blobContainerClient = _blobServiceClient.GetBlobContainerClient("unverified");
            var blobClient = blobContainerClient.GetBlobClient(message.FilePath);
            _logger.LogInformation($"{nameof(DocumentIntelligenceSubmitterFunction)} Run called with {blobClient.Name}");

            _dataTransformationStepStartRepository.Add(new DataTransformationStepStart
            {
                SignatureSheetUploadId = message.UploadId,
                InvocationId = executionContext.InvocationId,
                MatterId = message.MatterId,
                TemplateId = message.TemplateId,
                FunctionStepName = nameof(DocumentIntelligenceSubmitterFunction),
                Input = message.FilePath,
                StartDateTime = DateTime.UtcNow,
            });
            await _dataTransformationStepStartRepository.SaveChangesAsync();

            string responseString;
            int resultStatusCode;

            var stepResults = await _dataTransformationStepResultRepository.GetByMatterIdAsync(message.MatterId);
            var isDuplicate = stepResults.Any(
                result => result.Output.Contains($"/{message.PageNumber}.pdf") &&
                result.ResultStatusCode == (int)DocumentIntelligenceResultStatus.Valid);
            if (!isDuplicate)
            {
                var httpClient = _httpClientFactory.CreateClient("DocumentIntelligenceApi");
                var uploadSignatureSheetDto = new UploadSignatureSheetDTO
                {
                    SignatureSheetUploadId = message.UploadId,
                    FilePath = message.FilePath,
                };
                var content = new StringContent(JsonConvert.SerializeObject(uploadSignatureSheetDto), Encoding.UTF8, "application/json");
                var response = await httpClient.PostAsync("api/upload/signaturesheets/pipeline", content);
                responseString = await response.Content.ReadAsStringAsync();
                resultStatusCode = response.IsSuccessStatusCode
                    ? (int)DocumentIntelligenceResultStatus.Valid
                    : (int)DocumentIntelligenceResultStatus.Invalid;
                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogWarning($"Sheet validate failed {response.StatusCode} {responseString}");
                }
            }
            else
            {
                responseString = DocumentIntelligenceResultStatus.Duplicated.ToString();
                resultStatusCode = (int)DocumentIntelligenceResultStatus.Duplicated;
            }

            // TODO: technically the API should be telling US where IT put the successful sheet,
            // and if it fails, WE need to move it to where WE want it to go
            _dataTransformationStepResultRepository.Add(new DataTransformationStepResult
            {
                SignatureSheetUploadId = message.UploadId,
                InvocationId = executionContext.InvocationId,
                MatterId = message.MatterId,
                TemplateId = message.TemplateId,
                FunctionStepName = nameof(DocumentIntelligenceSubmitterFunction),
                Output = resultStatusCode != (int)DocumentIntelligenceResultStatus.Invalid
                    ? $"matter{message.MatterId}/{message.TemplateId}/{message.PageNumber}.pdf" // TODO: should be coming from API
                    : blobClient.Name, // We should be moving this ourselves above
                OutputDateTime = DateTime.UtcNow,
                ResultStatusCode = resultStatusCode,
                Message = responseString.Substring(0, Math.Min(responseString.Length, 4000)),
            });
            await _dataTransformationStepResultRepository.SaveChangesAsync();

            _logger.LogWarning($"Calling API with {message.FilePath} returned {resultStatusCode} {responseString}");
        }
    }
}
