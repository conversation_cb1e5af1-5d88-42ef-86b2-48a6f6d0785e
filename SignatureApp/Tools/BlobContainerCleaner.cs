using Azure.Storage.Blobs;
using Microsoft.Extensions.DependencyInjection;
using Service.Test;

namespace Tools;

public class BlobContainerCleaner: DatabaseTestsBase
{
    private static readonly string[] blacklistedContainerNames = [
        "kptesting05032024", "matter0", "templates", "database-backups"];
    private static readonly string[] blacklistedPrefixes = ["azure-", "candidate-", "initiative-", "testhubname-", "veracity"];

    public async Task RunAsync()
    {
        await InitializeAsync();
        var blobServiceClient = _serviceProvider.GetRequiredService<BlobServiceClient>();
        // List all containers in the storage account
        var containers = blobServiceClient.GetBlobContainersAsync();

        // Iterate through each container and delete it
        await foreach (var container in containers)
        {
            if (blacklistedContainerNames.Contains(container.Name))
            {
                continue;
            }
            if (blacklistedPrefixes.Any(prefix => container.Name.StartsWith(prefix)))
            {
                continue;
            }

            var containerClient = blobServiceClient.GetBlobContainerClient(container.Name);
            await containerClient.DeleteIfExistsAsync();
            Console.WriteLine($"Container '{container.Name}' deleted (if it existed).");
        }
    }
}