using Data.Repositories;
using DataInterface.RepositoryInterfaces;
using Microsoft.Extensions.DependencyInjection;
using Service.Test;
using Service;

namespace Tools;

public class InvalidSheetCreator : DatabaseTestsBase
{
    public async Task RunAsync()
    {
        await InitializeAsync();

        var matterRepository = _serviceProvider.GetRequiredService<IMatterRepository>();
        var stepResultRepository = _serviceProvider.GetRequiredService<IDataTransformationStepResultRepository>();
        var invalidSheetService = _serviceProvider.GetRequiredService<InvalidSheetService>();
        var sheetNumberService = _serviceProvider.GetRequiredService<SheetNumberService>();

        var matters = await matterRepository.GetAllAsync();
        foreach (var matter in matters)
        {
            var invalidSheets = await stepResultRepository.GetInvalidSheetsByMatterIdAsync(matter.Id);
            if (!invalidSheets.Any())
            {
                Console.WriteLine($"{matter.Id} {matter.Name} has 0 invalid sheets");
                continue;
            }

            foreach (var invalidSheet in invalidSheets)
            {
                var sheetNumber = sheetNumberService.GetSheetNumberFromFilename(invalidSheet.Filename);
                if (sheetNumber == null)
                {
                    Console.WriteLine($"{invalidSheet.Filename} has no sheet number");
                    continue;
                }

                await invalidSheetService.AddInvalidSheetAsync(
                    invalidSheet.SignatureSheetUpload, invalidSheet.Filename, sheetNumber.Value);
                Console.WriteLine($"Added invalid sheet {sheetNumber.Value} {invalidSheet.Filename}");
            }
        }
    }
}