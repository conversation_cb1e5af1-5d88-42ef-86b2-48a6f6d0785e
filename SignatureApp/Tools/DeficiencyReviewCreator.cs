using DataInterface.RepositoryInterfaces;
using Microsoft.Extensions.DependencyInjection;
using Model.Deficiencies;
using Service.Test;

namespace Tools;

public class DeficiencyReviewCreator : DatabaseTestsBase
{
    private static string[] ruleOperationNames =
    [
        "IsDateOutOfSequenceWithin", "IsBadAddress", "AreAddressLinesInconsistent",
        "IsNonResidentialCirculatorAddress", "IsNonResidentialVoterAddress", "AreDuplicateVoter"
    ];

    public async Task RunAsync()
    {
        await InitializeAsync();
        var matterRepository = _serviceProvider.GetRequiredService<IMatterRepository>();
        var ruleRepository = _serviceProvider.GetRequiredService<IRuleRepository>();
        var deficiencyRepository = _serviceProvider.GetRequiredService<IDeficiencyRepository>();
        var deficiencyReviewRepository = _serviceProvider.GetRequiredService<IDeficiencyReviewRepository>();

        var matters = await matterRepository.GetAllActiveMattersAsync();
        foreach (var matter in matters)
        {
            var rules = await ruleRepository.GetAllByMatterIdAsync(matter.Id);
            rules = rules.Where(r => ruleOperationNames.Contains(r.OperationName)).ToList();

            var deficiencies = await deficiencyRepository.GetAllByMatterAsync(matter.Id);
            deficiencies = deficiencies.Where(d => rules.Any(r => r.Id == d.RuleId)).ToList();
            if (deficiencies.Count == 0)
            {
                Console.WriteLine($"No reviewable deficiencies found for matter {matter.Id}");
                continue;
            }

            foreach (var deficiency in deficiencies)
            {
                var deficiencyReview = new DeficiencyReview
                {
                    MatterId = matter.Id,
                    SignatureSheetId = deficiency.SignatureSheetId,
                    RecordId = deficiency.RecordId,
                    RuleId = deficiency.RuleId,
                    RecordIdType = deficiency.RecordIdType,
                };

                deficiencyReviewRepository.Add(deficiencyReview);
            }

            await deficiencyReviewRepository.SaveChangesAsync();
            Console.WriteLine($"Created {deficiencies.Count} deficiency reviews for matter {matter.Id}");
        }
    }
}