using DataInterface.RepositoryInterfaces;
using Microsoft.Extensions.DependencyInjection;
using Model.ExternalDataSources;
using Model.SignatureSheets;
using Model.Templates;
using Service;
using Service.Test;

namespace Tools;

public class SignatoryCreator : DatabaseTestsBase
{
    public async Task RunAsync()
    {
        await InitializeAsync();

        var signatureSheetRowRepository = _serviceProvider.GetRequiredService<ISignatureSheetRowRepository>();
        var signatoryRepository = _serviceProvider.GetRequiredService<ISignatoryRepository>();
        var signatoryService = _serviceProvider.GetRequiredService<SignatoryService>();
        var templateSignatureColumnRepository =
            _serviceProvider.GetRequiredService<ITemplateSignatureColumnRepository>();

        var allColumns = await templateSignatureColumnRepository.GetAllAsync();

        var columnsByTemplateId = allColumns
            .Where(c => c.SignatureTable.TemplateId != null)
            .GroupBy(c => c.SignatureTable.TemplateId)
            .Where(c => c.Key.HasValue)
            .ToDictionary(g => g.Key ?? 0, g => g.ToList());

        var rows = await signatureSheetRowRepository.GetAllReviewedWithoutSignatoryAsync();
        await UpdateReviewRowsWithNoSignatory(rows, columnsByTemplateId, signatoryService);
        await signatureSheetRowRepository.SaveChangesAsync();

        var nonGeocodedSignatories = await signatoryRepository.GetAllNonGeocodedSignatories();
        await UpdateNonGeocodedSignatories(nonGeocodedSignatories, signatoryService, signatoryRepository);
        await signatoryRepository.SaveChangesAsync();
    }

    private static async Task UpdateReviewRowsWithNoSignatory(IList<SignatureSheetRow> rows,
        Dictionary<int, List<TemplateSignatureColumn>> columnsByTemplateId, SignatoryService signatoryService)
    {
        Console.WriteLine($"Found {rows.Count} rows");
        for (int index = 0; index < rows.Count; index++)
        {
            var row = rows[index];
            Console.WriteLine($"{row.Id} - {index + 1}/{rows.Count}");
            var columns = columnsByTemplateId[row.SignatureSheet.TemplateId];
            try
            {
                var result = await signatoryService.UpdateSignatoryForRowAsync(row, columns);
                if (!result.IsSuccess)
                {
                    Console.WriteLine(
                        $"Failed to update signatory for row {row.Id}: {string.Join(", ", result.ErrorMessages)}");
                    continue;
                }
            }
            catch (Exception e)
            {
                Console.WriteLine($"Error processing row {row.Id}: {e.Message}");
            }
        }
    }

    private static async Task UpdateNonGeocodedSignatories(List<Signatory> nonGeocodedSignatories,
        SignatoryService signatoryService,
        ISignatoryRepository signatoryRepository)
    {
        Console.WriteLine($"Found {nonGeocodedSignatories.Count} non-geocoded signatories");
        for (int index = 0; index < nonGeocodedSignatories.Count; index++)
        {
            var signatory = nonGeocodedSignatories[index];
            Console.WriteLine($"{signatory.Id} - {index + 1}/{nonGeocodedSignatories.Count}");
            try
            {
                var addressInput = signatoryService.GetAddressInputFromSignatory(signatory);
                await signatoryService.GeocodeSignatoryAsync(signatory, addressInput);
                if (signatory.Latitude != null && signatory.Longitude != null)
                {
                    signatoryRepository.SetModified(signatory);
                }
                else
                {
                    Console.WriteLine($"Error processing signatory {signatory.Id}: Lat/Long is still null");
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(
                    $"Failed to update signatory {signatory.Id}: {e.Message}");
            }

        }
    }
}