using DataInterface.RepositoryInterfaces;
using Microsoft.Extensions.DependencyInjection;
using Model.Templates;
using Service;
using Service.Test;

namespace Tools;

public class CirculatorCreator: DatabaseTestsBase
{
    public async Task RunAsync()
    {
        var templateIdToFields = new Dictionary<int, List<TranscribableField>>();
        await InitializeAsync();

        var signatureSheetRepository = _serviceProvider.GetRequiredService<ISignatureSheetRepository>();
        var circulatorService = _serviceProvider.GetRequiredService<CirculatorService>();
        var transcribableFieldRepository = _serviceProvider.GetRequiredService<ITranscribableFieldRepository>();

        var sheets = await signatureSheetRepository.GetAllFieldReviewedAsync();
        foreach (var sheet in sheets)
        {
            if (!templateIdToFields.TryGetValue(sheet.TemplateId, out var transcribableFields))
            {
                transcribableFields = await transcribableFieldRepository.GetAllByTemplateIdAsync(sheet.TemplateId);
                templateIdToFields[sheet.TemplateId] = transcribableFields;
            }
            Console.WriteLine($"Processing sheet {sheet.Id} - {sheet.MatterId} - {sheet.SheetNumber}");
            var result = await circulatorService.UpdateCirculatorFromSheetAsync(sheet, transcribableFields);
            if (!result.IsSuccess)
            {
                Console.WriteLine($"Failed to update circulator for sheet {sheet.Id}: {string.Join(", ", result.ErrorMessages)}");
            }
            else
            {
                Console.WriteLine($"Successfully updated circulator for sheet {sheet.Id}");
            }
        }
    }

}