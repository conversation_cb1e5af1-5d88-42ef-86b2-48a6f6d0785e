using System.Data;
using System.Text.Json;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Service.Test;

namespace Tools;

public class MatterArchiever : DatabaseTestsBase
{
    private readonly JsonSerializerOptions _jsonOptions;

    public MatterArchiever()
    {
        _jsonOptions = new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    public async Task RunAsync()
    {
        await InitializeAsync();

        Console.WriteLine("Matter Archiver Tool");
        Console.WriteLine("===================");

        // Get matter ID from user input
        Console.Write("Enter Matter ID to archive: ");
        var matterIdInput = Console.ReadLine();

        if (!int.TryParse(matterIdInput, out int matterId))
        {
            Console.WriteLine("Invalid Matter ID. Please enter a valid integer.");
            return;
        }

        // Verify matter exists
        if (!await MatterExistsAsync(matterId))
        {
            Console.WriteLine($"Matter with ID {matterId} does not exist.");
            return;
        }

        Console.WriteLine($"Archiving data for Matter ID: {matterId}");

        // Create output directory
        var outputDir = Path.Combine(Directory.GetCurrentDirectory(), "archive", $"matter-{matterId}");
        Directory.CreateDirectory(outputDir);
        Console.WriteLine($"Output directory: {outputDir}");

        // Load configuration
        var config = await LoadArchiveConfigurationAsync();
        if (config == null)
        {
            Console.WriteLine("Failed to load archive configuration.");
            return;
        }

        // Process each table
        var totalTables = config.Tables.Count;
        var processedTables = 0;

        foreach (var tableConfig in config.Tables)
        {
            try
            {
                Console.WriteLine($"Processing {tableConfig.Name}... ({++processedTables}/{totalTables})");
                await ProcessTableAsync(matterId, tableConfig, outputDir);
                Console.WriteLine($"✓ {tableConfig.Name} completed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Error processing {tableConfig.Name}: {ex.Message}");
            }
        }

        Console.WriteLine($"\nArchive completed! Files saved to: {outputDir}");
    }

    private async Task<bool> MatterExistsAsync(int matterId)
    {
        var connectionString = _configuration.GetConnectionString("SqlServer");
        using var connection = new SqlConnection(connectionString);
        await connection.OpenAsync();

        using var command = new SqlCommand("SELECT COUNT(1) FROM Matters WHERE Id = @MatterId", connection);
        command.Parameters.AddWithValue("@MatterId", matterId);

        var count = (int)await command.ExecuteScalarAsync();
        return count > 0;
    }

    private async Task<ArchiveConfiguration?> LoadArchiveConfigurationAsync()
    {
        try
        {
            var configPath = Path.Combine(Directory.GetCurrentDirectory(), "archive-config.json");
            if (!File.Exists(configPath))
            {
                Console.WriteLine($"Configuration file not found: {configPath}");
                return null;
            }

            var jsonContent = await File.ReadAllTextAsync(configPath);
            return JsonSerializer.Deserialize<ArchiveConfiguration>(jsonContent, _jsonOptions);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading configuration: {ex.Message}");
            return null;
        }
    }

    private async Task ProcessTableAsync(int matterId, TableConfiguration tableConfig, string outputDir)
    {
        var connectionString = _configuration.GetConnectionString("SqlServer");
        using var connection = new SqlConnection(connectionString);
        await connection.OpenAsync();

        using var command = new SqlCommand(tableConfig.Query, connection);
        command.Parameters.AddWithValue("@MatterId", matterId);

        var dataTable = new DataTable();
        using var adapter = new SqlDataAdapter(command);
        adapter.Fill(dataTable);

        // Convert DataTable to JSON
        var jsonData = ConvertDataTableToJson(dataTable);

        // Write to file
        var filePath = Path.Combine(outputDir, tableConfig.Filename);
        await File.WriteAllTextAsync(filePath, jsonData);

        Console.WriteLine($"  → {dataTable.Rows.Count} records exported to {tableConfig.Filename}");
    }

    private string ConvertDataTableToJson(DataTable dataTable)
    {
        var rows = new List<Dictionary<string, object?>>();

        foreach (DataRow row in dataTable.Rows)
        {
            var dict = new Dictionary<string, object?>();
            foreach (DataColumn column in dataTable.Columns)
            {
                var value = row[column];
                dict[column.ColumnName] = value == DBNull.Value ? null : value;
            }
            rows.Add(dict);
        }

        return JsonSerializer.Serialize(rows, _jsonOptions);
    }
}

// Configuration classes
public class ArchiveConfiguration
{
    public List<TableConfiguration> Tables { get; set; } = new();
}

public class TableConfiguration
{
    public string Name { get; set; } = string.Empty;
    public string Query { get; set; } = string.Empty;
    public string Filename { get; set; } = string.Empty;
}