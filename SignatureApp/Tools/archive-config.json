{"tables": [{"name": "Matters", "query": "SELECT * FROM Matters WHERE Id = @MatterId", "filename": "matter.json"}, {"name": "SignatureSheets", "query": "SELECT * FROM SignatureSheets WHERE MatterId = @MatterId", "filename": "signature-sheets.json"}, {"name": "SignatureSheetPages", "query": "SELECT ssp.* FROM SignatureSheetPages ssp INNER JOIN SignatureSheets ss ON ssp.SignatureSheetId = ss.Id WHERE ss.MatterId = @MatterId", "filename": "signature-sheet-pages.json"}, {"name": "SignatureSheetFormLines", "query": "SELECT ssfl.* FROM SignatureSheetFormLines ssfl INNER JOIN SignatureSheetPages ssp ON ssfl.SignatureSheetPageId = ssp.Id INNER JOIN SignatureSheets ss ON ssp.SignatureSheetId = ss.Id WHERE ss.MatterId = @MatterId", "filename": "signature-sheet-form-lines.json"}, {"name": "SignatureSheetFields", "query": "SELECT ssf.* FROM SignatureSheetFields ssf INNER JOIN SignatureSheetFormLines ssfl ON ssf.SignatureSheetFormLineId = ssfl.Id INNER JOIN SignatureSheetPages ssp ON ssfl.SignatureSheetPageId = ssp.Id INNER JOIN SignatureSheets ss ON ssp.SignatureSheetId = ss.Id WHERE ss.MatterId = @MatterId", "filename": "signature-sheet-fields.json"}, {"name": "SignatureSheetTables", "query": "SELECT sst.* FROM SignatureSheetTables sst INNER JOIN SignatureSheetPages ssp ON sst.SignatureSheetPageId = ssp.Id INNER JOIN SignatureSheets ss ON ssp.SignatureSheetId = ss.Id WHERE ss.MatterId = @MatterId", "filename": "signature-sheet-tables.json"}, {"name": "SignatureSheetColumns", "query": "SELECT ssc.* FROM SignatureSheetColumns ssc INNER JOIN SignatureSheetTables sst ON ssc.SignatureSheetTableId = sst.Id INNER JOIN SignatureSheetPages ssp ON sst.SignatureSheetPageId = ssp.Id INNER JOIN SignatureSheets ss ON ssp.SignatureSheetId = ss.Id WHERE ss.MatterId = @MatterId", "filename": "signature-sheet-columns.json"}, {"name": "SignatureSheetRows", "query": "SELECT ssr.* FROM SignatureSheetRows ssr INNER JOIN SignatureSheetTables sst ON ssr.SignatureSheetTableId = sst.Id INNER JOIN SignatureSheetPages ssp ON sst.SignatureSheetPageId = ssp.Id INNER JOIN SignatureSheets ss ON ssp.SignatureSheetId = ss.Id WHERE ss.MatterId = @MatterId", "filename": "signature-sheet-rows.json"}, {"name": "SignatureSheetCells", "query": "SELECT ssc.* FROM SignatureSheetCells ssc INNER JOIN SignatureSheetRows ssr ON ssc.SignatureSheetRowId = ssr.Id INNER JOIN SignatureSheetTables sst ON ssr.SignatureSheetTableId = sst.Id INNER JOIN SignatureSheetPages ssp ON sst.SignatureSheetPageId = ssp.Id INNER JOIN SignatureSheets ss ON ssp.SignatureSheetId = ss.Id WHERE ss.MatterId = @MatterId", "filename": "signature-sheet-cells.json"}, {"name": "Signatories", "query": "SELECT s.* FROM Signatories s INNER JOIN SignatureSheets ss ON s.SignatureSheetId = ss.Id WHERE ss.MatterId = @MatterId", "filename": "signatories.json"}, {"name": "Works", "query": "SELECT w.* FROM Works w INNER JOIN SignatureSheets ss ON w.SignatureSheetId = ss.Id WHERE ss.MatterId = @MatterId", "filename": "works.json"}, {"name": "WorkFields", "query": "SELECT wf.* FROM WorkFields wf INNER JOIN Works w ON wf.WorkId = w.Id INNER JOIN SignatureSheets ss ON w.SignatureSheetId = ss.Id WHERE ss.MatterId = @MatterId", "filename": "work-fields.json"}, {"name": "Deficiencies", "query": "SELECT d.* FROM Deficiencies d INNER JOIN SignatureSheets ss ON d.SignatureSheetId = ss.Id WHERE ss.MatterId = @MatterId", "filename": "deficiencies.json"}, {"name": "DeficiencyReviews", "query": "SELECT dr.* FROM DeficiencyReviews dr INNER JOIN Deficiencies d ON dr.DeficiencyId = d.Id INNER JOIN SignatureSheets ss ON d.SignatureSheetId = ss.Id WHERE ss.MatterId = @MatterId", "filename": "deficiency-reviews.json"}, {"name": "SignatureSheetUploads", "query": "SELECT * FROM SignatureSheetUploads WHERE MatterId = @MatterId", "filename": "signature-sheet-uploads.json"}, {"name": "SignatureSheetProcessings", "query": "SELECT ssp.* FROM SignatureSheetProcessings ssp INNER JOIN SignatureSheetUploads ssu ON ssp.SignatureSheetUploadId = ssu.Id WHERE ssu.MatterId = @MatterId", "filename": "signature-sheet-processings.json"}, {"name": "DataTransformationStepResults", "query": "SELECT * FROM DataTransformationStepResults WHERE MatterId = @MatterId", "filename": "data-transformation-step-results.json"}, {"name": "BackgroundOperations", "query": "SELECT * FROM BackgroundOperations WHERE MatterId = @MatterId", "filename": "background-operations.json"}]}