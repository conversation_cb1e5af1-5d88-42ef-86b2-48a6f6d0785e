﻿// See https://aka.ms/new-console-template for more information

using Tools;

//var program = new BlobContainerCleaner();
//var program = new BoundaryChecker();
//var program = new CirculatorCreator();
//var program = new DateColumnWorker();
//var program = new DeficiencyReviewCreator();
var program = new DeficiencyDiagnostic();
//var program = new DownloadedFileMessageEnqueuer();
//var program = new GeometryAdjuster();
//var program = new InvalidSheetCreator();
//var program = new KmlUploader();
//var program = new MatterArchiever();
//var program = new ReviewedValueMover();
//var program = new SignatoryCreator();
await program.RunAsync();