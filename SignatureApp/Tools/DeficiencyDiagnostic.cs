using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Service.Test;

namespace Tools;

public class DeficiencyDiagnostic : DatabaseTestsBase
{
    public async Task RunAsync()
    {
        await InitializeAsync();

        Console.WriteLine("Deficiency Diagnostic Tool");
        Console.WriteLine("=========================");
        
        await CheckOrphanedDeficiencies();
        await CheckOrphanedDeficiencyReviews();
        await ShowRuleStatistics();
    }

    private async Task CheckOrphanedDeficiencies()
    {
        Console.WriteLine("\n1. Checking for deficiencies with non-existent rules...");
        
        var connectionString = _configuration.GetConnectionString("SqlServer");
        await using var connection = new SqlConnection(connectionString);
        await connection.OpenAsync();

        var query = @"
            SELECT d.Id, d.RuleId, d.MatterId, d.RecordId, d.RecordIdType
            FROM Deficiencies d
            LEFT JOIN Rules r ON d.RuleId = r.Id
            WHERE r.Id IS NULL";

        await using var command = new SqlCommand(query, connection);
        await using var reader = await command.ExecuteReaderAsync();

        var orphanedDeficiencies = new List<(int Id, int RuleId, int MatterId, int RecordId, string RecordIdType)>();
        
        while (await reader.ReadAsync())
        {
            orphanedDeficiencies.Add((
                reader.GetInt32(0), // Id
                reader.GetInt32(1), // RuleId
                reader.GetInt32(2), // MatterId
                reader.GetInt32(3), // RecordId
                reader.GetInt32(4).ToString() // RecordIdType
            ));
        }

        if (orphanedDeficiencies.Any())
        {
            Console.WriteLine($"   ❌ Found {orphanedDeficiencies.Count} deficiencies with non-existent rules:");
            foreach (var deficiency in orphanedDeficiencies.Take(10))
            {
                Console.WriteLine($"      Deficiency ID: {deficiency.Id}, Rule ID: {deficiency.RuleId}, Matter ID: {deficiency.MatterId}");
            }
            if (orphanedDeficiencies.Count > 10)
            {
                Console.WriteLine($"      ... and {orphanedDeficiencies.Count - 10} more");
            }
        }
        else
        {
            Console.WriteLine("   ✅ No orphaned deficiencies found");
        }
    }

    private async Task CheckOrphanedDeficiencyReviews()
    {
        Console.WriteLine("\n2. Checking for deficiency reviews with non-existent rules...");
        
        var connectionString = _configuration.GetConnectionString("SqlServer");
        await using var connection = new SqlConnection(connectionString);
        await connection.OpenAsync();

        var query = @"
            SELECT dr.Id, dr.RuleId, dr.MatterId
            FROM DeficiencyReviews dr
            LEFT JOIN Rules r ON dr.RuleId = r.Id
            WHERE r.Id IS NULL";

        await using var command = new SqlCommand(query, connection);
        await using var reader = await command.ExecuteReaderAsync();

        var orphanedReviews = new List<(int Id, int RuleId, int MatterId)>();
        
        while (await reader.ReadAsync())
        {
            orphanedReviews.Add((
                reader.GetInt32(0), // Id
                reader.GetInt32(1), // RuleId
                reader.GetInt32(2)  // MatterId
            ));
        }

        if (orphanedReviews.Any())
        {
            Console.WriteLine($"   ❌ Found {orphanedReviews.Count} deficiency reviews with non-existent rules:");
            foreach (var review in orphanedReviews.Take(10))
            {
                Console.WriteLine($"      Review ID: {review.Id}, Rule ID: {review.RuleId}, Matter ID: {review.MatterId}");
            }
            if (orphanedReviews.Count > 10)
            {
                Console.WriteLine($"      ... and {orphanedReviews.Count - 10} more");
            }
        }
        else
        {
            Console.WriteLine("   ✅ No orphaned deficiency reviews found");
        }
    }

    private async Task ShowRuleStatistics()
    {
        Console.WriteLine("\n3. Rule statistics by matter...");
        
        var connectionString = _configuration.GetConnectionString("SqlServer");
        await using var connection = new SqlConnection(connectionString);
        await connection.OpenAsync();

        var query = @"
            SELECT 
                m.Id as MatterId,
                m.Name as MatterName,
                COUNT(r.Id) as RuleCount,
                COUNT(d.Id) as DeficiencyCount
            FROM Matters m
            LEFT JOIN Rules r ON m.Id = r.MatterId
            LEFT JOIN Deficiencies d ON r.Id = d.RuleId
            WHERE m.IsActive = 1
            GROUP BY m.Id, m.Name
            ORDER BY m.Id";

        await using var command = new SqlCommand(query, connection);
        await using var reader = await command.ExecuteReaderAsync();

        Console.WriteLine("   Matter ID | Matter Name | Rules | Deficiencies");
        Console.WriteLine("   ----------|-------------|-------|-------------");
        
        while (await reader.ReadAsync())
        {
            var matterId = reader.GetInt32(0); // MatterId
            var matterName = reader.IsDBNull(1) ? "N/A" : reader.GetString(1); // MatterName
            var ruleCount = reader.GetInt32(2); // RuleCount
            var deficiencyCount = reader.GetInt32(3); // DeficiencyCount

            Console.WriteLine($"   {matterId,9} | {matterName,-11} | {ruleCount,5} | {deficiencyCount,11}");
        }
    }
}
